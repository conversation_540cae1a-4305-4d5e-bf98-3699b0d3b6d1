{"name": "rubiconcarbon-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:webpack": "next dev", "build": "next build", "build:local": "env-cmd -f .env.deploy.local next build", "build:dev": "env-cmd -f .env.deploy.dev next build", "build:testing": "env-cmd -f .env.deploy.testing next build", "build:staging": "env-cmd -f .env.deploy.staging next build", "build:sandbox": "env-cmd -f .env.deploy.sandbox next build", "build:prod": "env-cmd -f .env.deploy.prod next build", "type-check": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix"}, "dependencies": {"@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@hcorta/react-echarts": "^2.0.0", "@hookform/resolvers": "^5.1.1", "@mui/icons-material": "^5.11.0", "@mui/lab": "^5.0.0-alpha.137", "@mui/material": "^5.11.8", "@mui/x-date-pickers": "^6.5.0", "@next/font": "13.3.2", "@opentelemetry/api": "^1.7.0", "@opentelemetry/api-logs": "^0.52.1", "@opentelemetry/auto-instrumentations-web": "^0.40.0", "@opentelemetry/context-zone": "^1.20.0", "@opentelemetry/exporter-logs-otlp-http": "^0.52.1", "@opentelemetry/exporter-trace-otlp-http": "^0.52.1", "@opentelemetry/instrumentation-document-load": "^0.39.0", "@opentelemetry/sdk-logs": "^0.52.1", "@opentelemetry/sdk-trace-web": "^1.20.0", "@react-oauth/google": "^0.7.0", "@rubiconcarbon/frontend-shared": "0.0.142", "@rubiconcarbon/shared-types": "^1.3.4", "@superset-ui/embedded-sdk": "^0.1.2", "@types/papaparse": "^5.3.14", "axios": "^1.3.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cookies-next": "^5.1.0", "date-fns": "^2.29.3", "date-fns-tz": "^2.0.0", "dayjs": "^1.11.7", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "fast-equals": "^5.0.1", "filefy": "^0.1.11", "flagged": "^2.0.9", "framer-motion": "^11.11.7", "jotai": "^2.8.4", "jotai-scope": "^0.6.0", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "material-react-table": "^1.14.0", "notistack": "3.0.1", "papaparse": "^5.4.1", "react-echarts-core": "^1.0.2", "react-error-boundary": "^3.1.4", "react-fast-marquee": "^1.6.4", "react-hook-form": "^7.56.4", "react-idle-timer": "^5.7.2", "react-number-format": "^5.1.4", "react-remark": "^2.1.0", "react-use": "^17.4.0", "reflect-metadata": "^0.2.1", "server-only": "^0.0.1", "sql-formatter": "^15.3.2", "swr": "^2.0.3", "victory": "36.6.2", "yup": "^1.0.2"}, "devDependencies": {"@next/eslint-plugin-next": "^15.3.2", "@types/lodash": "^4.14.191", "@types/node": "18.13.0", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "env-cmd": "^10.1.0", "eslint": "^9.29.0", "eslint-config-next": "^15.3.3", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-react-hooks": "4.6.0", "next": "^15.3.1", "prettier": "^3.2.4", "react": "^19.1.0", "react-dom": "^19.1.0", "sass": "^1.58.0", "typescript": "^5.8.3", "uuid": "^9.0.0"}}