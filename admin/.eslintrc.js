module.exports = {
  "extends": [
    "next/core-web-vitals",  // Essential Next.js rules + Core Web Vitals
    "next/typescript"        // TypeScript-specific rules (Next.js 15+ feature)
  ],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "project": false // Disable project-wide type checking
  },
  "rules": {
    "@typescript-eslint/no-explicit-any": "off", // Allow explicit any but should be used sparingly
  }
}