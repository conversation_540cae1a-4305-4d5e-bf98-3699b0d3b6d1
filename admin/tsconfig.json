// {
//   "compilerOptions": {
//     "target": "es5",
//     "lib": [
//       "dom",
//       "dom.iterable",
//       "esnext"
//     ],
//     "allowJs": true,
//     "skipLibCheck": true,
//     "strict": true,
//     "forceConsistentCasingInFileNames": true,
//     "noEmit": true,
//     "esModuleInterop": true,
//     "emitDecoratorMetadata": true,
//     "strictPropertyInitialization": false,
//     "experimentalDecorators": true,
//     "downlevelIteration": true,
//     "module": "esnext",
//     "moduleResolution": "node",
//     "resolveJsonModule": true,
//     "isolatedModules": true,
//     "jsx": "preserve",
//     "incremental": true,
//     "baseUrl": ".",
//     "paths": {
//       "@/*": ["./src/*"],
//       "@app/*": ["./src/app/*"],
//       "@components/*": ["./src/components/*"],
//       "@constants/*": ["./src/constants/*"],
//       "@hooks/*": ["./src/hooks/*"],
//       "@models/*": ["./src/models/*"],
//       "@providers/*": ["./src/providers/*"],
//       "@utils/*": ["./src/utils/*"],
//       "@assets/*": ["./public/*"]
//     },
//     "plugins": [
//       {
//         "name": "next"
//       }
//     ],
//   },
//   "include": [
//     "**/*.ts",
//     "**/*.tsx",
//     "next-env.d.ts",
//     ".next/types/**/*.ts"
//   ],
//   "exclude": [
//     "node_modules",
//     ".next"
//   ]
// }
{
  "compilerOptions": {
    // Essential settings
    "target": "ES2022",
    "lib": [
      "dom",
      "dom.iterable",
      "es6",
      "es2022",
    ],
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "allowJs": true,
    "esModuleInterop": true,
    "downlevelIteration": true,
    "strict": true,
    "plugins": [
      {
        "name": "next"
      }
    ],

    // Performance optimizations
    "skipLibCheck": true, // Skip type checking of declaration files
    "skipDefaultLibCheck": true,
    "noEmit": true,

    // Additional node_modules exclusion settings
    "typeRoots": ["./node_modules/@types"],
    "maxNodeModuleJsDepth": 0,

    // Relaxed strict settings for faster builds
    // "noImplicitAny": false,
    "strictNullChecks": false,
    "strictFunctionTypes": false,
    "noImplicitReturns": false,
    "noImplicitThis": false,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    
    // Path mapping (if needed)
    "baseUrl": ".",
    "paths": {
      "@/*": [
        "./src/*"
      ],
      "@app/*": [
        "./src/app/*"
      ],
      "@components/*": [
        "./src/components/*"
      ],
      "@constants/*": [
        "./src/constants/*"
      ],
      "@hooks/*": [
        "./src/hooks/*"
      ],
      "@models/*": [
        "./src/models/*"
      ],
      "@providers/*": [
        "./src/providers/*"
      ],
      "@utils/*": [
        "./src/utils/*"
      ],
      "@assets/*": [
        "./public/*"
      ]
    },
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts"
  ],
  "exclude": [
    "node_modules",
    "node_modules/**/*",
    "**/node_modules/**",
    ".next",
    "out",
    "dist"
  ],
  // Memory optimization
  "ts-node": {
    "compilerOptions": {
      "module": "commonjs"
    }
  }
}
