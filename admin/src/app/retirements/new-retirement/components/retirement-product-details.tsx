import { <PERSON><PERSON>, Autocomplete, Box, Divider, <PERSON>rid, <PERSON><PERSON><PERSON><PERSON>on, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Typo<PERSON> } from "@mui/material";
import {
  Control,
  Controller,
  FieldArrayWithId,
  FieldErrors,
  UseFieldArrayAppend,
  UseFieldArrayRemove,
  UseFormResetField,
  UseFormSetValue,
  UseFormTrigger,
  UseFormWatch,
} from "react-hook-form";
import { AddCircleRounded, DeleteRounded } from "@mui/icons-material";
import { AllKeys, calculator, Maybe, numberFormat, toDecimal, toNumber } from "@rubiconcarbon/frontend-shared";
import useAutoCompleteOptions, { UseAutoCompleteOptionsReturnEntry } from "@hooks/use-auto-complete-options";
import {
  AllocationResponse,
  AssetType,
  TrimmedBookResponse,
  TrimmedProjectVintageResponse,
  uuid,
} from "@rubiconcarbon/shared-types";
import { NumericFormat } from "react-number-format";
import { Fragment, useCallback, useMemo } from "react";
import { MISSING_DATA } from "@constants/constants";
import { useToggle } from "react-use";
import { AssetOrder, NonTradeAssetDetails, TransactionModel } from "@models/transaction";
import usePerformantEffect from "@/hooks/use-performant-effect";

import classes from "../../styles/product-details.module.scss";

type LineItemProps = {
  index: number;
  type?: AssetType;
  productOptions: UseAutoCompleteOptionsReturnEntry<NonTradeAssetDetails>[];
} & Pick<
  RetirementProductDetailsProps,
  "loadingAssets" | "control" | "errors" | "trigger" | "setValue" | "watch" | "remove"
>;

type RetirementProductDetailsProps = {
  loadingAssets: boolean;
  allocations?: AllocationResponse[];
  control: Control<TransactionModel, any>;
  errors: FieldErrors<TransactionModel>;
  lineItems: FieldArrayWithId<TransactionModel, "assets", "id">[];
  trigger: UseFormTrigger<TransactionModel>;
  setValue: UseFormSetValue<TransactionModel>;
  resetField: UseFormResetField<TransactionModel>;
  watch: UseFormWatch<TransactionModel>;
  append: UseFieldArrayAppend<TransactionModel, "assets">;
  remove: UseFieldArrayRemove;
};

const parserBlacklist = ["$", ","];

const LineItem = ({
  index,
  type,
  productOptions = [],
  loadingAssets,
  control,
  errors,
  trigger,
  setValue,
  watch,
  remove,
}: LineItemProps): JSX.Element => {
  const customerPortfolio = watch("retirement.customerPortfolio");
  const productType = watch("retirement.productType");
  const lineItem = watch(`assets.${index}`);
  const asset = lineItem?.supplementaryAssetDetails;

  const [focused, setFocused] = useToggle(false);

  const lineItemErrors = useMemo(() => errors?.assets?.at?.(index), [errors?.assets, index]);
  const isPortfolioSelection = useMemo(() => type === AssetType.RCT, [type]);
  const grossAvailable = useMemo(() => asset?.available, [asset?.available]);
  const netAvailable = useMemo(
    () =>
      calculator(grossAvailable)
        .subtract(lineItem?.amount || 0)
        .calculate()
        .toNumber(),
    [grossAvailable, lineItem?.amount],
  );
  const positiveAvailability = useMemo(() => netAvailable >= 0, [netAvailable]);

  usePerformantEffect(() => {
    setValue(`assets.${index}.amountAvailable`, grossAvailable);
  }, [grossAvailable, index, setValue]);

  usePerformantEffect(() => {
    if ((asset?.type === "portfolio" && !isPortfolioSelection) || (asset?.type === "vintage" && isPortfolioSelection)) {
      setValue(`assets.${index}.supplementaryAssetDetails`, undefined);
    }
  }, [asset?.type, index, isPortfolioSelection]);

  usePerformantEffect(() => {
    const subscription = watch(({ assets = [] }, { type, name }) => {
      if (type === "change") {
        const item = assets?.at(index);

        const amount = toDecimal(item?.amount, { parserBlacklist, treatNothingAsNaN: true });

        switch (name) {
          case `assets.${index}.supplementaryAssetDetails`:
            if (!assets?.at(index)?.supplementaryAssetDetails) setValue(`assets.${index}.amount`, "" as any);

            if (!amount?.isNaN()) trigger(`assets.${index}.amount`);

            break;
          default:
            break;
        }
      }
    });
    return (): void => subscription.unsubscribe();
  }, [index, watch]);

  const hasError = (field: AllKeys<AssetOrder>): boolean => !!lineItemErrors?.[field as keyof AssetOrder];

  const onFocus = (): void => setFocused(true);

  const onBlur = (): void => setFocused(false);

  return (
    <Grid className={classes.BodyRow} container item alignItems="start" gap={0.5}>
      <Grid className={classes.BodyCell} item xs={8}>
        <Controller
          name={`assets.${index}.supplementaryAssetDetails`}
          control={control}
          render={({ field: { value, onChange, ...otherProps } }): JSX.Element => {
            const selectedOption = productOptions.find((option) => value?.id === option?.value?.id);

            return (
              <Autocomplete
                options={productOptions}
                getOptionDisabled={(option) => !!option?.disabled}
                value={selectedOption || null}
                loading={loadingAssets}
                onChange={(_, selection) => onChange(selection?.value)}
                id="asset"
                renderInput={({ InputProps, ...params }) => (
                  <Box position="relative">
                    <TextField
                      {...params}
                      InputProps={{
                        ...InputProps,
                        sx: {
                          position: "relative",
                          zIndex: 1,
                          color: focused ? "black" : "transparent",
                        },
                      }}
                      label="Search and select product"
                      {...otherProps}
                      error={!!lineItemErrors?.supplementaryAssetDetails}
                      helperText={
                        !hasError("supplementaryAssetDetails") && !productType
                          ? "Please select Product Type"
                          : lineItemErrors?.supplementaryAssetDetails?.message
                      }
                      fullWidth
                      onFocus={onFocus}
                      onBlur={onBlur}
                    />
                    <Maybe condition={!focused}>
                      <Box
                        sx={{
                          position: "absolute",
                          top: 10,
                          left: 13,
                          right: 60,
                          bottom: 10,
                          display: "flex",
                          alignContent: "center",
                          zIndex: 2,
                          pointerEvents: "none",
                        }}
                      >
                        <Stack justifyContent="center" alignItems="flex-start">
                          <Maybe condition={selectedOption?.value?.type === "portfolio"}>
                            <Typography variant="body1" color="black">
                              {selectedOption?.value?.name}
                            </Typography>
                          </Maybe>
                          <Maybe condition={selectedOption?.value?.type === "vintage"}>
                            <Typography variant="body1" color="black">
                              {selectedOption?.value?.registryProjectId} - {selectedOption?.value?.name}
                            </Typography>
                            <Typography variant="caption" color="GrayText">
                              {selectedOption?.value?.projectName}
                            </Typography>
                          </Maybe>
                        </Stack>
                      </Box>
                    </Maybe>
                  </Box>
                )}
                renderOption={(props, option) => (
                  <li {...props} key={option.value?.id}>
                    {option?.displayLabel}
                  </li>
                )}
                fullWidth
                classes={{
                  root: classes.Autocomplete,
                  inputRoot: classes.InputRoot,
                  input: classes.Input,
                }}
                disabled={!customerPortfolio || !type}
              />
            );
          }}
        />
      </Grid>
      <Grid className={classes.BodyCell} item xs={2.9}>
        <Controller
          control={control}
          name={`assets.${index}.amount`}
          render={({ field: { ref, value, ...otherProps } }): JSX.Element => {
            return (
              <NumericFormat
                allowNegative={false}
                thousandSeparator
                decimalScale={0}
                label="Enter quantity"
                value={value}
                isAllowed={({ floatValue }) => !floatValue || (grossAvailable !== undefined && floatValue <= grossAvailable)}
                customInput={TextField}
                InputProps={{
                  ref,
                  classes: {
                    formControl: classes.FormControl,
                    root: classes.InputRoot,
                    input: classes.Input,
                  },
                }}
                error={!!lineItemErrors?.amount}
                helperText={
                  !hasError("amount") ? (
                    !asset ? (
                      "Please select Product"
                    ) : positiveAvailability && !!grossAvailable ? (
                      `Available: ${numberFormat(grossAvailable)}`
                    ) : null
                  ) : (
                    <Stack component="span" gap={0.5}>
                      <Maybe condition={!positiveAvailability && !!grossAvailable}>
                        <span>Available: {numberFormat(grossAvailable || 0)}</span>
                      </Maybe>
                      <Maybe
                        condition={
                          lineItemErrors?.amount?.type !== "maxOfField" ||
                          (lineItemErrors?.amount?.type === "maxOfField" && !positiveAvailability && !!grossAvailable)
                        }
                      >
                        <span>{lineItemErrors?.amount?.message}</span>
                      </Maybe>
                    </Stack>
                  )
                }
                {...otherProps}
                fullWidth
                disabled={!asset}
              />
            );
          }}
        />
      </Grid>
      <Grid className={classes.BodyCell} item xs={1} paddingTop="8px">
        <IconButton onClick={() => remove(index)}>
          <DeleteRounded />
        </IconButton>
      </Grid>
    </Grid>
  );
};

const RetirementProductDetails = ({
  loadingAssets: loadingProducts,
  allocations = [],
  control,
  errors,
  lineItems,
  trigger,
  setValue,
  resetField,
  watch,
  append,
  remove,
}: RetirementProductDetailsProps): JSX.Element => {
  const type = watch("retirement.productType");
  const watchedLineItems = watch("assets");
  const selectedAssetIds = watchedLineItems
    ?.map(({ supplementaryAssetDetails }) => supplementaryAssetDetails?.id)
    ?.filter((id) => !!id);

  const isPortfolioSelection = useMemo(() => type === AssetType.RCT, [type]);

  const hiddenLines = isPortfolioSelection
    ? lineItems?.reduce<string[]>((ids, { _id }, index) => {
        if (index > 0 && _id) {
          return [...ids, _id];
        }
        return ids;
      }, [])
    : [];

  const productOptions = useAutoCompleteOptions<AllocationResponse, NonTradeAssetDetails>({
    data: allocations?.filter((allocation) =>
      isPortfolioSelection
        ? !("project" in (allocation?.detailedAsset || {}))
        : "project" in (allocation?.detailedAsset || {}),
    ),
    keys: [
      "amountAllocated",
      "amountAvailable",
      "amountPendingRetirement",
      "amountPendingCustomerTransferOutflow",
      "detailedAsset",
    ],
    label: (entry) =>
      isPortfolioSelection
        ? (entry?.detailedAsset as TrimmedBookResponse)?.name
        : `${(entry?.detailedAsset as TrimmedProjectVintageResponse)?.project?.registryProjectId} - ${(entry?.detailedAsset as TrimmedProjectVintageResponse)?.name} - ${(entry?.detailedAsset as TrimmedProjectVintageResponse)?.project?.name}`,
    displayLabel: (entry) => {
      return (
        <Stack width="100%" direction="row" justifyContent="space-between" alignItems="center">
          <Maybe condition={isPortfolioSelection}>
            <span>{entry?.detailedAsset?.name}</span>
          </Maybe>
          <Maybe condition={!isPortfolioSelection}>
            <Stack>
              <span>
                {(entry?.detailedAsset as TrimmedProjectVintageResponse)?.project?.registryProjectId} - {entry?.detailedAsset?.name}
              </span>
              <Typography variant="caption" color="GrayText">
                {(entry?.detailedAsset as TrimmedProjectVintageResponse)?.project?.["name"]}
              </Typography>
            </Stack>
          </Maybe>
          <Typography variant="caption" color="GrayText">
            {numberFormat(entry?.amountAvailable || 0)}
          </Typography>
        </Stack>
      );
    },
    disabled: (entry) => !!selectedAssetIds?.includes(entry?.detailedAsset?.id),
    value: (entry) => ({
      id: entry?.detailedAsset?.id as uuid,
      type: isPortfolioSelection ? "portfolio" : "vintage",
      name: entry?.detailedAsset?.name ?? "",
      available: entry?.amountAvailable || 0,
      projectName: (entry?.detailedAsset as TrimmedProjectVintageResponse)?.project?.["name"],
      registryProjectId: (entry?.detailedAsset as TrimmedProjectVintageResponse)?.project?.["registryProjectId"],
    }),
    postTransform: (options) =>
      options
        .filter((option) => !!option?.value?.available)
        .sort((a, b) => {
          const firstOrder = !isPortfolioSelection
            ? (a.value?.registryProjectId || '').localeCompare(b.value?.registryProjectId || '')
            : a.value?.name?.localeCompare(b.value?.name);

          if (!firstOrder) return a.value?.name?.localeCompare(b.value?.name);

          return firstOrder;
        }),
  });

  const addNewLineItem = useCallback((): void => {
    append(new AssetOrder());
    if (errors?.assets) resetField("assets", { keepDirty: false, keepError: false });
  }, [append, errors?.assets, resetField]);

  return (
    <Stack className={classes.ProductDetails} gap={1}>
      <Typography color="black">Product Details</Typography>
      <Grid className={classes.Table} container>
        <Grid className={classes.Header} container item gap={0.5}>
          <Grid className={classes.HeaderCell} item xs={8}>
            <Typography variant="body2">Product</Typography>
          </Grid>
          <Grid className={classes.HeaderCell} item xs={2.9}>
            <Typography variant="body2">Quantity</Typography>
          </Grid>
          <Grid className={classes.HeaderCell} item xs={1}>
            <Typography variant="body2">Actions</Typography>
          </Grid>
        </Grid>
        <Grid className={classes.Body} container item justifyContent={"center"} gap={1}>
          <Maybe condition={!!lineItems?.length}>
            {lineItems
              ?.filter((item) => !hiddenLines?.length || !hiddenLines.includes(item?._id))
              ?.map((lineItem, index) => (
                <Fragment key={lineItem?.id}>
                  <LineItem
                    index={index}
                    type={type}
                    productOptions={productOptions}
                    loadingAssets={loadingProducts}
                    control={control}
                    errors={errors}
                    trigger={trigger}
                    setValue={setValue}
                    watch={watch}
                    remove={remove}
                  />
                  <Maybe condition={index !== lineItems?.length - 1}>
                    <Divider sx={{ width: "100%" }} />
                  </Maybe>
                </Fragment>
              ))}
          </Maybe>
          <Maybe condition={!lineItems?.length}>
            <Alert
              classes={{
                root: `${classes.Alert}${errors?.assets ? ` ${classes.Error}` : ""}`,
                icon: classes.Icon,
                message: classes.Message,
              }}
              severity={errors?.assets ? "error" : "info"}
            >
              <Typography>Please add a product entry</Typography>
            </Alert>
          </Maybe>
        </Grid>
        <Maybe condition={!!lineItems?.length}>
          <Grid className={classes.Footer} container item justifyContent={"center"} gap={0.5}>
            <Divider sx={{ width: "100%" }} />
            <Grid className={classes.FooterRow} container item alignItems="center" gap={1} justifyContent="flex-end">
              <Grid item xs={7.3} />
              <Grid
                className={classes.FooterCell}
                container
                item
                xs={2.9}
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography className={classes.Label} variant="caption">
                  Total Quantity:
                </Typography>
                <Typography className={classes.Value} variant="body2">
                  {numberFormat(
                    (watchedLineItems || [])
                      ?.filter((item) => !hiddenLines?.length || !hiddenLines.includes(item?._id))
                      .reduce((sum, { amount }) => sum + toNumber(amount, { parserBlacklist }), 0),
                    { fallback: MISSING_DATA },
                  )}
                </Typography>
              </Grid>
              <Grid item xs={1} />
            </Grid>
          </Grid>
        </Maybe>
      </Grid>
      <Box display="flex" justifyContent="flex-end">
        <IconButton
          color="primary"
          disabled={(!type && !!watchedLineItems?.length) || (isPortfolioSelection && !!watchedLineItems?.length)}
          onClick={addNewLineItem}
        >
          <AddCircleRounded fontSize="large" />
        </IconButton>
      </Box>
    </Stack>
  );
};

export default RetirementProductDetails;
