import { useLogger } from "@providers/logging";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import useNavigation from "@/hooks/use-navigation";
import { useBoolean, usePrevious } from "react-use";
import { classValidatorResolver } from "@hookform/resolvers/class-validator";
import { Controller, useFieldArray, useForm } from "react-hook-form";
import { useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import {
  AdminRetirementRequest,
  AssetAllocationQueryResponse,
  AssetType,
  AssetWithNestedAllocationQuery,
  AdminBookQueryResponse,
  AdminBookResponse,
  RetirementType,
  uuid,
} from "@rubiconcarbon/shared-types";
import { useEffect, useMemo } from "react";
import { KeyboardArrowRightRounded } from "@mui/icons-material";
import { Container, Stack, Autocomplete, TextField, Button } from "@mui/material";
import useAutoCompleteOptions, { UseAutoCompleteOptionsReturnEntry } from "@hooks/use-auto-complete-options";
import { MISSING_DATA, SERVER_PAGINATION_LIMIT } from "@constants/constants";
import GenericDialog from "@components/ui/generic-dialog/generic-dialog";
import { toDecimal, toNumber } from "@rubiconcarbon/frontend-shared";
import { TransactionModel } from "@models/transaction";
import { getNewTransferOutflowModel } from "@utils/helpers/transaction/get-new-transaction-models";
import TransferProductDetails from "./transfer-product-details";
import TransferConfirmation from "./transfer-confirmation";

import classes from "../../styles/new-form.module.scss";

const parserBlacklist = ["$", ","];

const TransferModelResolver = classValidatorResolver(TransactionModel);

const NewTransferForm = ({
  customerPortfoliosResponse,
}: {
  customerPortfoliosResponse: AdminBookQueryResponse;
}): JSX.Element => {
  const { logger } = useLogger();
  const { popFromPath } = useNavigation();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();

  const [canConfirm, setCanConfirm] = useBoolean(false);

  const model = getNewTransferOutflowModel();

  const {
    control,
    formState: { errors },
    trigger,
    setValue,
    watch,
    resetField,
    handleSubmit,
  } = useForm<TransactionModel>({
    resolver: TransferModelResolver,
    defaultValues: model,
    mode: "onSubmit",
  });

  const {
    fields: lineItems,
    append,
    remove,
  } = useFieldArray({
    control,
    name: "assets",
  });

  const data = watch();
  const customerPortfolio = data?.transfer?.customerPortfolio;

  const previousSelectedCustomerPortfolioId = usePrevious(customerPortfolio?.id);

  const {
    data: assets,
    trigger: getAssets,
    isMutating: loadingAssets,
  } = useTriggerRequest<AssetAllocationQueryResponse, null, { id: uuid }, AssetWithNestedAllocationQuery>({
    url: "admin/books/{id}/assets/holdings",
    pathParams: {
      id: customerPortfolio?.id,
    },
    queryParams: {
      assetTypes: [AssetType.REGISTRY_VINTAGE],
      limit: SERVER_PAGINATION_LIMIT,
    },
    swrOptions: {
      onError: (error: any): void => {
        enqueueError(`Unable to fetch assets for ${customerPortfolio?.name}`);
        logger.error(`Unable to load assets for ${customerPortfolio?.name}: ${error?.message}`, {});
      },
    },
  });

  const { trigger: commitTransfer, isMutating: commitingRetirement } = useTriggerRequest<any, AdminRetirementRequest>({
    url: "admin/retirements",
    method: "post",
    requestBody: {
      customerPortfolioId: data?.transfer?.customerPortfolio?.id,
      isPublic: false,
      assetType: AssetType.REGISTRY_VINTAGE,
      beneficiary: MISSING_DATA,
      type: RetirementType.TRANSFER_OUTFLOW,
      assets: data?.assets?.map((item) => ({
        assetId: item?.supplementaryAssetDetails?.id,
        sourceId: data?.transfer?.customerPortfolio?.id,
        amount: toNumber(item?.amount, { parserBlacklist }),
        rawPrice: toDecimal(0.0),
      })),
      registryAccount: data?.transfer?.registryAccount,
    },
    swrOptions: {
      onSuccess: () => {
        enqueueSuccess("Transfer created successfully.");
        popFromPath(1);
      },
      onError: (error: any): void => {
        enqueueError("Unable to create transfer.");
        logger.error(`Unable to create transfer: ${error?.message}`, {});
      },
    },
  });

  const allocations = useMemo(() => assets?.data, [assets?.data]);

  useEffect(() => {
    const customerPortfolioId = customerPortfolio?.id;
    if (!!customerPortfolioId && previousSelectedCustomerPortfolioId !== customerPortfolioId && !loadingAssets)
      setTimeout(async () => await getAssets());
  }, [customerPortfolio?.id, getAssets, loadingAssets, previousSelectedCustomerPortfolioId]);

  const customerPortfolioOptions = useAutoCompleteOptions<AdminBookResponse, AdminBookResponse>({
    data: customerPortfoliosResponse?.data || [],
    keys: ["id", "organization"],
    label: (entry: any) => entry?.organization?.name,
    value: (entry: any) => entry,
    preTransform: (data: AdminBookResponse[]) => data?.filter(({ isEnabled }) => isEnabled),
    postTransform: (data: UseAutoCompleteOptionsReturnEntry[]) =>
      data?.sort((a, b) => a?.label?.localeCompare(b?.label)),
  });

  const onSubmit = (): void => setCanConfirm(true);

  return (
    <Container sx={{ padding: "20px 0px" }}>
      <Stack
        component="form"
        justifyContent="center"
        gap={3}
        maxWidth={1200}
        minWidth={900}
        onSubmit={handleSubmit(onSubmit)}
      >
        <Controller
          name="transfer.customerPortfolio"
          control={control}
          render={({ field: { ref, value, onChange, ...otherProps } }): JSX.Element => {
            const selectedOption = customerPortfolioOptions.find((entry) => entry?.value?.id === value?.id) ?? null;
            return (
              <Autocomplete
                options={customerPortfolioOptions}
                value={selectedOption}
                onChange={(_, selection) => onChange(selection?.value)}
                id="organization"
                getOptionKey={(option: UseAutoCompleteOptionsReturnEntry<AdminBookResponse>) => option?.value?.id}
                getOptionLabel={(option: UseAutoCompleteOptionsReturnEntry<AdminBookResponse>) => option?.label}
                renderInput={({ InputProps, ...params }) => (
                  <TextField
                    {...params}
                    InputProps={{
                      ref,
                      ...InputProps,
                    }}
                    label="Organization"
                    {...otherProps}
                    error={!!errors?.transfer?.customerPortfolio}
                    helperText={errors?.transfer?.customerPortfolio?.message}
                    fullWidth
                  />
                )}
                fullWidth
              />
            );
          }}
        />
        <TransferProductDetails
          loadingAssets={loadingAssets}
          allocations={allocations}
          control={control}
          errors={errors}
          lineItems={lineItems}
          trigger={trigger}
          setValue={setValue}
          resetField={resetField}
          watch={watch}
          append={append}
          remove={remove}
        />
        <Controller
          name="transfer.registryAccount"
          control={control}
          render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
            <TextField
              label="Transfer Credits to Account"
              value={value ?? ""}
              InputProps={{ ref }}
              {...otherProps}
              fullWidth
              multiline
            />
          )}
        />
        <Stack direction="row" justifyContent="space-between">
          <Button className={classes.ActionButton} color="error" onClick={() => popFromPath(1)}>
            Cancel
          </Button>
          <Button
            className={classes.ActionButton}
            type="submit"
            variant="contained"
            endIcon={<KeyboardArrowRightRounded />}
            disabled={loadingAssets}
          >
            Continue: Confirmation
          </Button>
        </Stack>
      </Stack>
      <GenericDialog
        title="Review and Confirm Transfer"
        open={canConfirm}
        onClose={() => setCanConfirm(false)}
        positiveAction={{
          buttonText: "CONFIRM TRANSFER",
          loading: commitingRetirement,
          onClick: async () => await commitTransfer(),
        }}
        negativeAction={{
          buttonText: "CANCEL",
          onClick: () => setCanConfirm(false),
        }}
        classes={{
          root: classes.Dialog,
          title: classes.Title,
          content: classes.Content,
          actions: classes.Actions,
        }}
      >
        <TransferConfirmation data={data} />
      </GenericDialog>
    </Container>
  );
};

export default NewTransferForm;
