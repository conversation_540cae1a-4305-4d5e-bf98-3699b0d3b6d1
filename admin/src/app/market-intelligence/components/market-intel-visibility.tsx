import React from "react";
import { Box, Chip, FormControl, FormHelperText, InputLabel, MenuItem, OutlinedInput } from "@mui/material";
import { Theme, useTheme } from "@mui/material/styles";
import Select, { SelectChangeEvent } from "@mui/material/Select";
import { isEmpty } from "lodash";
import COLORS from "@components/ui/theme/colors";
import CancelIcon from "@mui/icons-material/Cancel";
import { DDMenuProps, VisibilityTags } from "../constants/tags";

function getStyles(name: string, personName: readonly string[], theme: Theme): any {
  return {
    fontWeight: personName.includes(name) ? theme.typography.fontWeightMedium : theme.typography.fontWeightRegular,
  };
}

interface MarketIntelVisibilityProps {
  selectedVisibility: string[];
  validationMsg: string;
  handleChange: (event: SelectChangeEvent<any>) => void;
  handleDeleteVisibility: (event: React.MouseEvent, value: string) => void;
}

export default function MarketIntelVisibility({
  selectedVisibility,
  validationMsg,
  handleChange,
  handleDeleteVisibility,
}: MarketIntelVisibilityProps): JSX.Element {
  const theme = useTheme();

  return (
    <FormControl sx={{ m: 1, width: "100%" }}>
      <InputLabel id="multiple-chip-label">Visibility</InputLabel>
      <Select
        labelId="multiple-chip-label"
        id="multiple-chip"
        multiple
        value={selectedVisibility}
        onChange={handleChange}
        error={!isEmpty(validationMsg)}
        input={<OutlinedInput id="select-multiple-chip" label="Visibility" />}
        renderValue={(selected) => (
          <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
            {selected.map((value: any) => (
              <Chip
                key={value}
                label={value}
                sx={{ height: "25px", paddingTop: "5px" }}
                deleteIcon={<CancelIcon onMouseDown={(event) => event.stopPropagation()} />}
                onDelete={(e) => handleDeleteVisibility(e, value)}
              />
            ))}
          </Box>
        )}
        MenuProps={DDMenuProps}
      >
        {Object.values(VisibilityTags).map((name) => (
          <MenuItem key={name} value={name} style={getStyles(name, selectedVisibility, theme)}>
            {name}
          </MenuItem>
        ))}
      </Select>
      <FormHelperText sx={{ color: COLORS.red }}>{validationMsg}</FormHelperText>
    </FormControl>
  );
}
