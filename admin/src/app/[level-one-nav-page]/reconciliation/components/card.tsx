import { <PERSON><PERSON> } from "@hcorta/react-echarts";
import { CSSProperties, useState } from "react";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import { Metadata } from "../types/general";
import BarChartIcon from "@mui/icons-material/BarChart";
import TableChartTwoToneIcon from "@mui/icons-material/TableChartTwoTone";
import HelpOutlineIcon from "@mui/icons-material/HelpOutline";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import AutoFixHighIcon from "@mui/icons-material/AutoFixHigh";
import { CircularProgress, Tooltip, Typography } from "@mui/material";
import { CsvBuilder } from "filefy";

import classes from "../styles/card.module.scss";

type CardDefaultVisualization = "chart" | "table";

interface CardProps {
  // data
  chart?: EChart | JSX.Element;
  table?: JSX.Element;
  default?: CardDefaultVisualization;
  // title
  title: string;
  subtitle?: string;
  // toolbox
  csv?: () => { columns: string[]; data: any[][]; filename: string };
  help?: JSX.Element;
  filters?: string[];
  metadata?: Metadata;
  reconcileAvailable?: boolean;
  onReconcile?: () => void; // Add this line
  // status
  loading: boolean;
  error?: any;
  // styles
  style?: Omit<CSSProperties, "height">;
  height?: CSSProperties["height"];
}

function downloadCSV(input: { columns: string[]; data: any[][]; filename: string }): void {
  const csvBuilder = new CsvBuilder(`${input.filename}.csv`);
  csvBuilder.setColumns(input.columns).addRows(input.data).exportFile();
}

export default function Card(props: CardProps): JSX.Element {
  const hasHelp = props.help !== undefined;
  const hasCSV = props.csv !== undefined;
  const hasChart = props.chart !== undefined;
  const hasTable = props.table !== undefined;
  const hasSubtitle = props.subtitle !== undefined;
  const hasMetadata = !!props.metadata?.asOf;
  const reconcileAvailable = props.reconcileAvailable ?? false;

  const [viz, setViz] = useState<CardDefaultVisualization>(props.default ?? (hasChart ? "chart" : "table"));

  if (props.error != undefined)
    return (
      <div style={{ ...props.style, height: props.height }} className={classes.outer}>
        <div className={classes.container} style={{ display: "flex", alignItems: "center", justifyContent: "center" }}>
          {props.error}
        </div>
      </div>
    );
  if (props.loading)
    return (
      <div style={{ ...props.style, height: props.height }} className={classes.outer}>
        <div className={classes.container} style={{ display: "flex", alignItems: "center", justifyContent: "center" }}>
          <CircularProgress color="secondary" size="4rem" />
        </div>
      </div>
    );

  return (
    <>
      <div style={{ ...props.style, height: props.height }} className={classes.outer}>
        {/* Download CSV */}
        <div className={classes.toolbox}>
          <Maybe condition={hasCSV}>
            <Tooltip title="Download as CSV">
              <FileDownloadIcon
                className={classes.toolboxButton}
                onClick={() => {
                  downloadCSV(props.csv());
                }}
              />
            </Tooltip>
          </Maybe>

          {/* Reconcile Button */}
          <Maybe condition={reconcileAvailable}>
            <Tooltip title="Reconcile">
              <span
                className={classes.toolboxButton}
                onClick={() => {
                  // Call onReconcile handler if available
                  if (props.onReconcile) {
                    props.onReconcile();
                  }
                }}
              >
                <AutoFixHighIcon />
              </span>
            </Tooltip>
          </Maybe>

          {/* Switch visualization type */}
          <Maybe condition={hasChart && viz != "chart"}>
            <Tooltip title={viz != "chart" ? "Switch to chart view" : ""}>
              <span
                className={classes.toolboxButton}
                onClick={() => {
                  setViz("chart");
                }}
              >
                <BarChartIcon></BarChartIcon>
              </span>
            </Tooltip>
          </Maybe>
          <Maybe condition={hasTable && viz != "table"}>
            <Tooltip title={viz != "table" ? "Switch to table view" : ""}>
              <span
                className={classes.toolboxButton}
                onClick={() => {
                  setViz("table");
                }}
              >
                <TableChartTwoToneIcon></TableChartTwoToneIcon>
              </span>
            </Tooltip>
          </Maybe>

          {/* Help */}
          <Maybe condition={hasHelp}>
            <Tooltip title={props.help}>
              <HelpOutlineIcon className={classes.toolboxButton} />
            </Tooltip>
          </Maybe>
        </div>
        <div className={classes.container}>
          <Typography variant="h5" className={classes.title}>
            {props.title}
          </Typography>
          <Maybe condition={hasSubtitle}>
            <Typography variant="caption" className={classes.subtitle}>
              {props.subtitle}
            </Typography>
          </Maybe>
          <div
            className="class.inner"
            style={{ height: `calc(100% - 60px ${hasSubtitle ? "- 29px" : ""} ${hasMetadata ? "- 12px" : ""})` }}
          >
            <Maybe condition={viz == "chart" && hasChart}>{props.chart as JSX.Element}</Maybe>
            <Maybe condition={viz == "table" && hasTable}>{props.table}</Maybe>
          </div>
        </div>
        <Maybe condition={hasMetadata}>
          <div className={classes.metadata}>
            <span>as of: {props.metadata?.asOf.toLocaleString()}</span>
          </div>
        </Maybe>
      </div>
    </>
  );
}
