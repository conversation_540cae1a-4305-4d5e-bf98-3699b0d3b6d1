import { useContext, useMemo, useState, useCallback } from "react";
import Box from "@mui/material/Box";
import Link from "next/link";
import { AxiosContext } from "@providers/axios-provider";
import { Data } from "../types/general";
import { useLogger } from "@providers/logging";
import { MaterialReactTable, type MRT_ColumnDef } from "material-react-table";
import Typography from "@mui/material/Typography";
import SyncAltIcon from "@mui/icons-material/SyncAlt";
import ReconciliationModal from "./reconciliation-modal";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { ReconTransactionsType } from "../types/transactions";
import { Delay, useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import Card from "./card";

import classes from "../styles/styles.module.scss";

export default function ReconTransactions({
  transactionsResponse: serverTransactionsResponse,
}: {
  transactionsResponse: Data<ReconTransactionsType[]>;
}): JSX.Element {
  const { reportingFetcher } = useContext(AxiosContext);
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const { logger } = useLogger();

  const {
    data: response,
    isMutating: isLoading,
    error: transactionError,
    trigger: refreshTransactions,
  } = useTriggerRequest<Data<ReconTransactionsType[]>>({
    url: "reporting/recon/transactions",
    customFetcher: reportingFetcher as any,
    optimisticData: serverTransactionsResponse,
    swrOptions: {
      onError: (error: any) => {
        enqueueError("Unable to fetch recon transactions.");
        logger.error(`Unable to fetch recon transactions. Error: ${error?.message}`, {});
      },
    },
  });

  const { api } = useContext(AxiosContext);
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<ReconTransactionsType | null>(null);

  // Handle opening the modal with a specific transaction
  const handleOpenModal = useCallback((transaction: ReconTransactionsType): void => {
    setSelectedTransaction(transaction);
    setModalOpen(true);
  }, []);

  // Handle reconciliation submission
  const handleReconciliationSubmit = async (reconciliationData: any): Promise<void> => {
    try {
      const formattedData = reconciliationData.map((item: any) => {
        const noteDate = new Date();
        const noteToSubmit = item.notes
          ? typeof item.notes === "string" && item.notes.trim() !== ""
            ? [{ date: Math.floor(noteDate.getTime() / 1000), reason: item.notes }]
            : Array.isArray(item.notes)
              ? item.notes
              : []
          : [];

        return {
          ...item,
          notes: noteToSubmit,
        };
      });

      const postReconItems = await api.post<any>(`reporting/recon/transactions/create`, formattedData);
      enqueueSuccess(`${postReconItems?.data?.records_created} reconciliation item(s) submitted successfully.`);
      refreshTransactions();
    } catch (error: any) {
      enqueueError("Error submitting reconciliation.");
      logger.error(`Error submitting reconciliation: ${error?.message}`, {});
    }
  };

  const columns = useMemo<MRT_ColumnDef<ReconTransactionsType>[]>(
    () => [
      { accessorKey: "transaction_type", header: "Transaction Type" },
      { accessorKey: "source", header: "Source" },
      { accessorKey: "registry", header: "Registry" },
      { accessorKey: "registry_project_id", header: "Project ID" },
      { accessorKey: "vintage", header: "Vintage" },
      {
        accessorKey: "credits",
        header: "Credits",
        aggregationFn: "sum",
        Cell: ({ cell }): JSX.Element => <>{cell.getValue<number>().toLocaleString()}</>,
        AggregatedCell: ({ cell }): JSX.Element => (
          <>
            <span>Total Credits: </span>
            <Box sx={{ color: "rgb(255,0,0, .7)", display: "inline", fontWeight: "bold" }}>
              {cell.getValue<number>().toLocaleString()}
            </Box>
          </>
        ),
      },
      {
        accessorFn: (row): string => row.transaction_key,
        header: "Transaction Key",
        Cell: ({ renderedCellValue, row }): JSX.Element => {
          const baseUrlPath = row.original.transaction_type === "Transfer" ? "/retirements" : "/trading/transactions";
          return (
            <Link href={`${baseUrlPath}/${row.original.transaction_id}`} className={classes.Link}>
              {renderedCellValue}
            </Link>
          );
        },
      },
      {
        accessorFn: (row): string => (row.date_acquired ? new Date(row.date_acquired).toDateString() : "-"),
        header: "Date Acquired",
      },
      {
        accessorFn: (row): string => row.transaction_id,
        header: "Reconcile",
        Cell: ({ row }): JSX.Element => {
          if (row.original.source !== "RubiconCarbon Platform") {
            return <></>;
          }
          return (
            <Box className={classes.Link} sx={{ cursor: "pointer" }} onClick={() => handleOpenModal(row.original)}>
              <SyncAltIcon fontSize="small" />
            </Box>
          );
        },
      },
    ],
    [handleOpenModal],
  );

  return (
    <>
      <Card
        title="Transactions Reconciliation"
        subtitle="Unreconciled items"
        loading={isLoading}
        error={transactionError}
        height={"600px"}
        table={
          /**
           * wrapped it in a delay to fix a bug with the table
           * not rendering correctly on initial load due to server side data fetching.
           */
          <Delay ms={100}>
            <MaterialReactTable
              columns={columns ?? []}
              data={response?.data ?? []}
              renderEmptyRowsFallback={() => (
                <Typography
                  sx={{
                    color: "text.secondary",
                    fontStyle: "italic",
                    py: "2rem",
                    textAlign: "center",
                    width: "100%",
                  }}
                >
                  No mismatched items.
                  <br />
                  All entries are reconciled.
                </Typography>
              )}
              enableColumnActions={false}
              enableColumnFilters={false}
              enablePagination={true}
              enableSorting={false}
              enableBottomToolbar={true}
              enableTopToolbar={false}
              muiTableContainerProps={{
                sx: { boxShadow: "none", maxHeight: "400px", position: "relative", zIndex: 1 },
              }}
              muiTableProps={{
                sx: {
                  padding: "10px",
                  boxShadow: "none",
                  border: "0px",
                  width: "100%",
                  zIndex: 1,
                },
              }}
              muiTableHeadRowProps={{ sx: { boxShadow: "none", border: "0px", zIndex: 1 } }}
              muiTableHeadProps={{ sx: { zIndex: 1 } }}
              muiTablePaperProps={{
                sx: {
                  boxShadow: "none",
                  width: "100%",
                  height: "100%",
                  padding: "0 5px",
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "space-between",
                  position: "relative",
                  zIndex: 1,
                },
              }}
              initialState={{
                density: "compact",
                grouping: ["transaction_type", "source"],
                expanded: { transaction_type: true },
                columnVisibility: { transaction_id: false },
                pagination: { pageSize: 100, pageIndex: 0 },
              }}
              enableGrouping
              enableColumnDragging={false}
              enableStickyHeader
              enableStickyFooter
            />
          </Delay>
        }
        help={
          <>
            <h4 style={{ color: "white", margin: "0 0 8px 0" }}>Transactions Reconciliation</h4>
            <div>Unreconciled line items across the Rubicon Carbon internal platform and the external registries. </div>
            <div>
              Every item in this table represents an internal record that is not matched to a registry entry or a
              registry entry with no internal matches.
            </div>
          </>
        }
        csv={() => ({
          columns: columns.map((x) => x.header),
          data: response?.data.map((x) =>
            columns.map((col) => {
              if (col.accessorKey) {
                return x[col.accessorKey as keyof ReconTransactionsType];
              }
              return col.accessorFn ? col.accessorFn(x) : '';
            }),
          ),
          filename: `purchases-reconciliation-${Date.now()}`,
        })}
        metadata={response?.metadata}
        reconcileAvailable={true}
        onReconcile={() => {
          setSelectedTransaction(null);
          setModalOpen(true);
        }}
      />
      <ReconciliationModal
        open={modalOpen}
        onClose={() => {
          setModalOpen(false);
          setSelectedTransaction(null);
        }}
        data={selectedTransaction ? [selectedTransaction] : []}
        reconType="transactions"
        onSubmit={handleReconciliationSubmit}
        autoReconcile={!selectedTransaction}
      />
    </>
  );
}
