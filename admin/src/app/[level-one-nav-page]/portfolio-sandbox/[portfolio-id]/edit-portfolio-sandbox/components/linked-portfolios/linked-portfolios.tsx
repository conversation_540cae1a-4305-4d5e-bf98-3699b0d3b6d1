import React, { useState, useEffect, useMemo, useCallback } from "react";
import {
  AdminGroupedPortfolioResponse,
  ModelPortfolioRelations,
  ModelPortfolioResponse,
  uuid,
} from "@rubiconcarbon/shared-types";
import Decimal from "decimal.js";
import { stringComparator } from "@utils/comparators/comparator";
import dateFormatterEST from "@/utils/formatters/est-date-formatter";
import { MISSING_DATA, SERVER_PAGINATION_LIMIT } from "@constants/constants";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import integerFormat from "@/utils/formatters/integer-format";
import { currencyFormat, useRequest } from "@rubiconcarbon/frontend-shared";
import { Box, Typography } from "@mui/material";
import PortfolioSandboxPDF from "../portfolio-sandbox-pdf/portfolio-sandbox-pdf";
import Link from "next/link";
import { useLogger } from "@providers/logging";
import EnhancedTable, { ColDef, SortOrder } from "@components/ui/table/enhanced-table";
import TableBox from "@components/ui/table-box/table-box";

export default function LinkedPortfolios(props: { portfolioId: uuid }): JSX.Element {
  const { portfolioId } = props;
  const { enqueueError } = useSnackbarVariants();
  const { logger } = useLogger();

  const [portfolios, setPortfolios] = useState<AdminGroupedPortfolioResponse[]>();

  const { data: linkedPortfolios } = useRequest<ModelPortfolioResponse>({
    url: `/admin/model-portfolios/${portfolioId}`,
    queryParams: {
      offset: 0,
      limit: SERVER_PAGINATION_LIMIT,
      includeTotalCount: true,
      includeRelations: [ModelPortfolioRelations.GROUPED_PORTFOLIOS],
    },
    swrOptions: {
      onError: (error: any): void => {
        enqueueError("Unable to load linked portfolios");
        logger.error(`Unable to load linked portfolios for id ${portfolioId}: ${error?.message}`, {});
      },
    },
  });

  const getPrice = useCallback((inputMap: Map<string, any>): string => {
    if (!inputMap.get("priceEstimate") || !inputMap.get("totalAmount")) return MISSING_DATA;
    const totalPriceEstimate = new Decimal(inputMap.get("priceEstimate"));
    const totalAmount = new Decimal(inputMap.get("totalAmount"));
    const price = totalPriceEstimate.dividedBy(totalAmount);
    return price ? currencyFormat(price.toFixed(2, Decimal.ROUND_HALF_UP)) : MISSING_DATA;
  }, []);

  const showLinkedPortfolioName = useCallback((inputMap: Map<string, any>): JSX.Element => {
    const portfolioId = inputMap?.get("id");
    const portfolioName = inputMap?.get("name");
    if (!portfolioId || !portfolioName) return <>{MISSING_DATA}</>;
    return (
      <>
        <Link href={`/inventory-management/portfolio-sandbox/${portfolioId}/edit-portfolio-sandbox`}>
          <Typography variant="body2" component="span" fontWeight="400" color="black">
            {portfolioName}
          </Typography>
        </Link>
      </>
    );
  }, []);

  const showPDFDownload = useCallback((inputMap: Map<string, any>): JSX.Element => {
    const portfolioId = inputMap?.get("id");
    const portfolioName = inputMap?.get("name");
    const priceEstimate = inputMap?.get("priceEstimate");
    if (!portfolioId || !portfolioName) return <></>;
    return (
      <PortfolioSandboxPDF
        isDisabled={priceEstimate ? false : true}
        portfolioId={uuid(portfolioId)}
        portfolioName={portfolioName}
        tooltip={priceEstimate ? "" : "Price is required"}
      />
    );
  }, []);

  const columnsDef = useMemo<ColDef[]>(
    () => [
      {
        columnName: "id",
        displayName: "Portfolio Id",
        hide: true,
      },
      {
        columnName: "name",
        displayName: "Portfolio Name",
        comparator: stringComparator as any,
        formatter: {
          func: showLinkedPortfolioName as any,
          inputFields: ["id", "name"],
        },
      },
      {
        columnName: "updatedAt",
        displayName: "Date",
        formatter: { func: dateFormatterEST as any },
        exportFormatter: { func: dateFormatterEST as any },
      },
      {
        columnName: "totalAmount",
        displayName: "Quantity",
        formatter: { func: (x: Decimal) => (x ? integerFormat(+x) : MISSING_DATA) } as any,
      },
      {
        columnName: "uiKey",
        displayName: "Price",
        formatter: {
          func: getPrice as any,
          inputFields: ["priceEstimate", "totalAmount"],
        },
      },
      {
        columnName: "priceEstimate",
        displayName: "Total",
        formatter: { func: (x: Decimal): string => (x ? currencyFormat(x.toFixed(2, Decimal.ROUND_HALF_UP)) : MISSING_DATA) },
      },
      {
        columnName: "id",
        displayName: "",
        formatter: {
          func: showPDFDownload,
          inputFields: ["id", "name", "priceEstimate"],
        },
      },
    ],
    [getPrice, showPDFDownload, showLinkedPortfolioName],
  );

  useEffect(() => {
    if (linkedPortfolios?.groupedPortfolios) {
      linkedPortfolios.groupedPortfolios.sort((a, b) => (a.createdAt < b.createdAt ? 1 : -1));
      setPortfolios(linkedPortfolios.groupedPortfolios);
    }
  }, [linkedPortfolios]);

  return (
    <>
      <Box mb={2}>
        <Typography variant="body1" component="h5" fontWeight="400" fontSize="24px">
          Historical Prices
        </Typography>
      </Box>
      <TableBox>
        {portfolios && (
          <EnhancedTable
            name={"linked_portfolios_info"}
            columnsDef={columnsDef}
            exportable={true}
            data={portfolios}
            rowsCountPerPage={100}
            defaultSort={{ columnName: "lastUpdated", order: SortOrder.DESC }}
          />
        )}
      </TableBox>
    </>
  );
}
