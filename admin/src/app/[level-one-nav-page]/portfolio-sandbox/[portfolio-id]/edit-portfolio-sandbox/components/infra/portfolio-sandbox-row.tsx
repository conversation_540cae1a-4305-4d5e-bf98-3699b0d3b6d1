import React from "react";
import { Stack, Typo<PERSON>, TableCell, TextField, Autocomplete, Tooltip } from "@mui/material";
import { ModelPortfolioComponentResponse } from "@rubiconcarbon/shared-types";
import { NumericFormat } from "react-number-format";
import { isEmpty, isFinite } from "lodash";
import { MISSING_DATA } from "@constants/constants";
import { isNothing, Maybe } from "@rubiconcarbon/frontend-shared";
import DetailsIcon from "@mui/icons-material/Info";
import { ColDef, formatCellValue, convertStringToNumber } from "../portfolio-sandbox-table/portfolio-sandbox-model";

import classes from "../../styles/styles.module.scss";

const itemStyle = {
  paddingTop: "10px",
};

export default function BaseRow(props: {
  rowDef: ColDef[];
  type: "existing" | "new";
  isEdit?: boolean;
  rowData?: ModelPortfolioComponentResponse;
}): JSX.Element {
  const { rowDef, type, isEdit = false, rowData } = props;

  return (
    <>
      {rowDef
        .filter((d) => d?.hidden !== true)
        .map((colDef, idx) => {
          //Show field text from data source
          if (type !== "new" && colDef.mode === "display") {
            return (
              <TableCell align="left" key={`${colDef.id}-${idx}`}>
                <Stack direction="row">
                  <Typography variant="body2" component="div" sx={itemStyle} className={classes[colDef?.class ?? ""]}>
                    {!!rowData &&
                    !!(rowData as unknown as Record<string, unknown>)[colDef.id] &&
                    (typeof ((rowData as unknown) as Record<string, string | number>)[colDef.id] === "string" || typeof ((rowData as unknown) as Record<string, string | number>)[colDef.id] === "number")
                      ? formatCellValue((rowData as unknown as Record<string, unknown>)[colDef.id] as string | number, colDef, rowData)
                      : MISSING_DATA}
                  </Typography>
                  <Maybe condition={!!colDef.tooltip}>
                    <Tooltip placement="top" title={colDef.tooltip}>
                      <DetailsIcon sx={{ height: "18px", marginLeft: "5px" }} />
                    </Tooltip>
                  </Maybe>
                </Stack>
              </TableCell>
            );
          }
          switch (colDef.type) {
            case "display":
              return (
                <TableCell align="left" key={colDef.id}>
                  <Stack direction="row">
                    <Typography variant="body2" component="div" sx={itemStyle} className={classes[colDef?.class ?? ""]}>
                      {isEmpty(colDef.value) ? (
                        MISSING_DATA
                      ) : (
                        <Stack direction="row">
                          {formatCellValue(colDef.value, colDef)}
                          <span>{colDef.suffix ?? ""}</span>
                        </Stack>
                      )}
                    </Typography>
                    <Maybe condition={!!colDef.tooltip}>
                      <Tooltip placement="top" title={colDef.tooltip}>
                        <DetailsIcon sx={{ height: "18px", marginLeft: "5px" }} />
                      </Tooltip>
                    </Maybe>
                  </Stack>
                </TableCell>
              );
            case "string":
              return (
                <TableCell align="left" key={colDef.id}>
                  {isEdit || type === "new" ? (
                    <TextField
                      id={colDef.id}
                      name={colDef.id}
                      size="small"
                      type="text"
                      value={colDef.value}
                      onChange={colDef.onChange}
                      inputProps={{
                        maxLength: 256,
                        style: { fontSize: 14, minWidth: "100px" },
                      }}
                      helperText={colDef.helperText}
                      error={colDef.error}
                    />
                  ) : (
                    <Typography variant="body2" component="div" sx={itemStyle}>
                      {formatCellValue(colDef.value, colDef)}
                    </Typography>
                  )}
                </TableCell>
              );
            case "autocomplete":
              return (
                <TableCell align="left" key={colDef.id}>
                  {isEdit || type === "new" ? (
                    <Stack direction="row">
                      <Autocomplete
                        options={(colDef.defaultProps?.options || [])}
                        {...colDef.defaultProps}
                        onChange={(event, value) => {
                          if (colDef.onChange) {
                            colDef.onChange(event as any, value);
                          }
                        }}
                        value={colDef.value}
                        loading
                        clearOnBlur
                        handleHomeEndKeys
                        freeSolo
                        loadingText="No Data"
                        disablePortal
                        componentsProps={{
                          popper: {
                            modifiers: [
                              {
                                name: "flip",
                                enabled: false,
                              },
                              {
                                name: "preventOverflow",
                                enabled: false,
                              },
                            ],
                          },
                        }}
                        className={classes[colDef.class ?? ""]}
                        id={colDef.id}
                        renderInput={(params) => (
                          <TextField
                            error={colDef.error}
                            helperText={colDef.helperText}
                            onChange={colDef?.onSearchChange}
                            onBlur={colDef?.onBlur}
                            required
                            placeholder={colDef?.placeHolder}
                            {...params}
                            size="small"
                            InputProps={{
                              ...params.InputProps,
                              style: { fontSize: 14, minWidth: "150px" },
                            }}
                          />
                        )}
                      ></Autocomplete>
                      <Maybe condition={!!colDef.tooltip}>
                        <Tooltip placement="top" title={colDef.tooltip}>
                          <DetailsIcon sx={{ height: "18px", marginLeft: "5px" }} />
                        </Tooltip>
                      </Maybe>
                    </Stack>
                  ) : (
                    <Typography variant="body2" component="div" sx={itemStyle}>
                      {colDef.value?.toString() ?? MISSING_DATA}
                    </Typography>
                  )}
                </TableCell>
              );
            case "number":
              return (
                <TableCell align="left" key={colDef.id}>
                  {isEdit || type === "new" ? (
                    <NumericFormat
                      size="small"
                      defaultValue={0}
                      value={
                        colDef?.valueFormatter
                          ? colDef.valueFormatter(convertStringToNumber(colDef?.value?.toString() ?? ""))
                          : convertStringToNumber(colDef?.value?.toString() ?? "")
                      }
                      fixedDecimalScale
                      name={colDef.id}
                      decimalScale={colDef.decimalScale ?? 0}
                      prefix={colDef.prefix ?? ""}
                      suffix={colDef.suffix ?? ""}
                      inputProps={{
                        maxLength: 11,
                        style: { fontSize: 14, width: "100px" },
                      }}
                      allowNegative={false}
                      customInput={TextField}
                      type="text"
                      thousandSeparator={","}
                      helperText={colDef.helperText}
                      error={colDef.error}
                      onChange={colDef.onChange}
                    />
                  ) : (
                    <Typography variant="body2" component="div" sx={itemStyle}>
                      {!isNothing(colDef.value) && isFinite(+colDef.value!)
                        ? `${formatCellValue(colDef.value, colDef)}${colDef.suffix ?? ""}`
                        : MISSING_DATA}
                    </Typography>
                  )}
                </TableCell>
              );
            default:
              throw new Error("unsupported field type");
          }
        })}
    </>
  );
}
