import React, { useState, useContext, useEffect, useMemo, use<PERSON><PERSON>back, SyntheticEvent } from "react";
import { AxiosContext } from "@providers/axios-provider";
import {
  Box,
  TextField,
  Button,
  Dialog,
  DialogContent,
  DialogActions,
  Typography,
  Stack,
  Autocomplete,
  FormLabel,
  DialogTitle,
} from "@mui/material";
import { ModelPortfolioResponse, OrganizationResponse, UpdateModelPortfolioRequest } from "@rubiconcarbon/shared-types";
import { Maybe, Nullable } from "@rubiconcarbon/frontend-shared";
import SummaryGroup, { itemGroup } from "@components/ui/summary-group/item-group";
import ActionButton from "@components/ui/action-button/action-button-enhanced";
import currencyFormat from "@/utils/formatters/currency-format";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import Decimal from "decimal.js";
import { MISSING_DATA } from "@constants/constants";
import integerFormat from "@/utils/formatters/integer-format";
import { BaseDialogProps } from "@models/dialogs";
import usePerformantEffect from "@/hooks/use-performant-effect";
import { RiskAdjIcon } from "@components/icons/risk-adj";

const labelStyle = {
  fontSize: "16px",
  fontWeight: "500",
  lineHeight: "22.88px",
  color: "rgba(0, 0, 0, 0.87)",
};

interface ShareQuoteModalProps extends BaseDialogProps {
  mockPortfolio: ModelPortfolioResponse;
  organizationsData?: OrganizationResponse[];
  totalQuantity: number;
  originalPricePerTonne?: Decimal;
  onSave: () => void;
}

export default function ShareQuoteModal({
  mockPortfolio,
  organizationsData,
  totalQuantity,
  originalPricePerTonne,
  isOpen,
  onClose,
  onSave,
}: ShareQuoteModalProps): JSX.Element | null {
  const { api } = useContext(AxiosContext);
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const [availableOrganizations, setAvailableOrganizations] = useState<OrganizationResponse[]>([]);
  const [selectedOrganization, setSelectedOrganization] = useState<Nullable<OrganizationResponse>>(null);

  useEffect(() => {
    if (organizationsData) {
      setAvailableOrganizations(organizationsData);
    }
  }, [organizationsData]);

  const organizationDefaultProps = {
    options:
      availableOrganizations
        .filter((organization) => organization.isEnabled)
        ?.sort((a, b) => a.name?.localeCompare(b.name)) ?? [],
    getOptionLabel: (option: OrganizationResponse): string => (option?.name ? option.name : ""),
    isOptionEqualToValue: (option: OrganizationResponse, value: OrganizationResponse): boolean => option.id === value.id,
  };

  const organizationSelectionHandler = (
    _: SyntheticEvent,
    newValue: Nullable<OrganizationResponse>,
  ): void => {
    setSelectedOrganization(newValue);
  };

  const totalPrice: Nullable<Decimal> = useMemo(
    () => (!!originalPricePerTonne && totalQuantity > 0 ? new Decimal(+originalPricePerTonne * totalQuantity) : null),
    [originalPricePerTonne, totalQuantity],
  );

  const [totalInputPrice, setTotalInputPrice] = useState<string>("");
  const [includeRiskAdj, setIncludeRiskAdj] = useState<string>("");
  const allowSharePortfolio: boolean = useMemo(() => !!selectedOrganization, [selectedOrganization]);

  usePerformantEffect(() => {
    if (mockPortfolio !== null) {
      if (mockPortfolio?.organization?.id) {
        const organization = availableOrganizations.filter((o) => o.id === mockPortfolio?.organization?.id);
        if (organization.length === 1) {
          setSelectedOrganization(organization[0]);
        }
      }

      setIncludeRiskAdj(mockPortfolio?.includeRiskAdjustment ? "true" : "false");

      if (totalPrice) {
        setTotalInputPrice(totalPrice?.toString());
      }
    }
  }, [mockPortfolio, availableOrganizations, originalPricePerTonne, isOpen]);

  const clearFields = useCallback((): void => {
    setTotalInputPrice("");
  }, [setTotalInputPrice]);

  const onCloseHandler = useCallback((): void => {
    clearFields();
    onClose();
  }, [onClose, clearFields]);

  const customerPortalPriceGroup: itemGroup = useMemo(
    () => ({
      groupTitle: "",
      items: [
        {
          label: "Quantity",
          value: integerFormat(totalQuantity?.toString()) ?? MISSING_DATA,
        },
        {
          label: "Per Tonne",
          value: originalPricePerTonne ? (currencyFormat(+originalPricePerTonne) ?? MISSING_DATA) : MISSING_DATA,
          valueStyle: { color: "red" },
        },
        {
          label: "Total",
          value: totalPrice ? (currencyFormat(+totalPrice) ?? MISSING_DATA) : MISSING_DATA,
        },
      ],
      groupStyle: {
        titleStyle: { fontSize: "16px" },
        itemStyle: { fontSize: "14px !important" },
        valueStyle: {
          fontSize:
            (totalInputPrice?.length ?? 0) +
              (totalQuantity?.toString()?.length ?? 0) +
              (originalPricePerTonne?.toString()?.length ?? 0) >
            20
              ? "20px !important"
              : "24px !important",
          wordBreak: "break-word",
        },
        containerStyle: { backgroundColor: "rgba(250, 250, 250, 1)" },
      },
    }),
    [totalQuantity, totalPrice, originalPricePerTonne, totalInputPrice?.length],
  );

  const onSubmitHandler = useCallback(async (): Promise<void> => {
    const payload: UpdateModelPortfolioRequest = {
      organizationId: !mockPortfolio?.organization?.id ? selectedOrganization?.id : undefined,
      includeRiskAdjustment: includeRiskAdj === "true" ? true : false,
      priceEstimate: totalPrice ?? undefined,
      showCustomer: true,
    };

    try {
      await api.patch<ModelPortfolioResponse>(`admin/model-portfolios/${mockPortfolio?.id}`, payload);
      enqueueSuccess("Portfolio was shared successfully");
      onSave();
    } catch (error: any) {
      if (error?.response?.data?.message) {
        enqueueError(
          Array.isArray(error.response.data.message)
            ? error.response.data.message.join(", ")
            : error.response.data.message,
        );
      } else enqueueError("Unable to share portfolio");
    }
  }, [
    mockPortfolio?.id,
    includeRiskAdj,
    selectedOrganization,
    totalPrice,
    api,
    mockPortfolio?.organization?.id,
    enqueueError,
    enqueueSuccess,
    onSave,
  ]);

  const dialogHead = useMemo(
    () => (
      <Box mt={1}>
        <Typography
          variant="body2"
          component="p"
          sx={{
            color: "rgba(0, 0, 0, 1)",
            backgroundColor: "white",
            fontWeight: 500,
            fontSize: "24px",
            width: "501px",
            border: "none",
          }}
        >
          {"Share with Customer"}
        </Typography>
      </Box>
    ),
    [],
  );

  const dialogActions = useMemo(
    () => (
      <>
        <Button variant="text" onClick={onCloseHandler} sx={{ fontWeight: 600, textTransform: "capitalize" }}>
          Cancel
        </Button>
        <Stack direction="row" sx={{ marginRight: "-10px" }}>
          <ActionButton
            isDisabled={!allowSharePortfolio}
            style={{ fontWeight: 500, width: "145px", textTransform: "capitalize" }}
            onClickHandler={onSubmitHandler}
          >
            Share Portfolio
          </ActionButton>
        </Stack>
      </>
    ),
    [allowSharePortfolio, onCloseHandler, onSubmitHandler],
  );

  return (
    <Dialog open={isOpen} onClose={onCloseHandler} fullWidth>
      <DialogTitle sx={{ backgroundColor: "white" }}>{dialogHead}</DialogTitle>

      <DialogContent
        sx={{
          height: !mockPortfolio?.organization?.id ? "350px" : "260px",
          overflowY: "auto",
          paddingBottom: "0px !important",
        }}
      >
        <Box mt={-3} sx={{ width: "100%" }}>
          <Maybe condition={!!mockPortfolio?.organization?.id}>
            <Box mt={-4}>
              <Typography mb={2.5}>
                The following portfolio will be shared with <b>{selectedOrganization?.name}</b> through the customer
                portal.
              </Typography>
            </Box>
          </Maybe>
          <Maybe condition={!mockPortfolio?.organization?.id}>
            <Box>
              <FormLabel component="legend" sx={labelStyle}>
                Select organization
              </FormLabel>
              <Autocomplete
                {...organizationDefaultProps}
                onChange={organizationSelectionHandler}
                disabled={!!mockPortfolio.organization?.id}
                value={selectedOrganization}
                sx={{ marginTop: "15px" }}
                loading
                disablePortal
                id="organization"
                renderInput={(params) => <TextField required {...params} label="Organization" />}
                ListboxProps={{
                  sx: {
                    maxHeight: "200px",
                  },
                }}
              />
            </Box>
          </Maybe>
          <Box mt={2} width="100%" sx={{ backgroundColor: "rgba(250, 250, 250, 1)" }}>
            <SummaryGroup group={customerPortalPriceGroup} />
            <Stack gap={1} direction="row" mt={1} sx={{ paddingLeft: "20px", paddingBottom: "20px" }}>
              <RiskAdjIcon width={"23px"} height={"auto"} />
              <Typography>
                Risk adjustment is <b>{includeRiskAdj === "true" ? "" : "NOT"} included.</b>
              </Typography>
            </Stack>
          </Box>
        </Box>
      </DialogContent>
      <DialogActions
        sx={{
          backgroundColor: "rgba(250, 250, 250, 1)",
          paddingRight: "24px",
          justifyContent: "space-between",
          border: "none",
        }}
      >
        {dialogActions}
      </DialogActions>
    </Dialog>
  );
}
