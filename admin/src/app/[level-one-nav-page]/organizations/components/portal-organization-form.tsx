import {
  Autocomplete,
  <PERSON><PERSON>,
  Container,
  Divider,
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import { classValidatorResolver } from "@hookform/resolvers/class-validator";
import { getNewPortalOrgModel } from "../utilities/get-model-type";
import { Controller, useForm } from "react-hook-form";
import useNavigation from "@/hooks/use-navigation";
import {
  classcat,
  deepEqual,
  isArray,
  Maybe,
  Nullable,
  pickFromRecord,
  toBoolean,
  useTriggerRequest,
} from "@rubiconcarbon/frontend-shared";
import {
  DocumentType,
  DocumentUpdateRequest,
  DocumentUploadUrlRequest,
  OrganizationCreateRequest,
  OrganizationResponse,
  OrganizationUpdateRequest,
  UserResponse,
  uuid,
} from "@rubiconcarbon/shared-types";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { useLogger } from "@providers/logging";
import useAutoCompleteOptions, { UseAutoCompleteOptionsReturnEntry } from "@hooks/use-auto-complete-options";
import COLORS from "@components/ui/theme/colors";
import InlineUploader from "@components/ui/uploader/components/InlineUploaderWidget";
import useDocumentsApi from "@hooks/use-documents-api";
import usePerformantEffect from "@/hooks/use-performant-effect";
import { useGetSetState, useToggle, useUnmount } from "react-use";
import { MouseEvent, useCallback, useMemo, useState } from "react";
import { FileUploadHandlers } from "@/types/headless-downloader";
import { OnFileUploadSuccessMetaData } from "@components/ui/uploader/types/hook";
import { toUnifiedOrganizationModel } from "../utilities/to-model";
import usePerformantState from "@/hooks/use-perfomant-state";
import { LoadingButton } from "@mui/lab";
import AttachmentList from "@components/attachment-list/attachment-list";
import { DocumentTypeUILabel } from "@constants/documents";
import { useStoreProvider } from "@providers/store-provider";
import { OrganizationType } from "@constants/organization-type.enum";
import { UnifiedOrganizationModel } from "@models/organization";

import classes from "../styles/form.module.scss";

const OrganizationModelResolver = classValidatorResolver(UnifiedOrganizationModel);

const PortalOrganizationForm = ({
  organizationResponse,
  managers,
}: {
  organizationResponse?: OrganizationResponse;
  managers: UserResponse[];
}): JSX.Element => {
  const { logger } = useLogger();
  const { popFromPath } = useNavigation();
  const { enqueueSuccess, enqueueError, enqueueWarning, closeSnackbar } = useSnackbarVariants();
  const { updateEphemeralState } = useStoreProvider();

  const [uploading, toggleUploading] = useToggle(false);
  const [hasDocUploadError, toggleHasDocUploadError] = useToggle(false);

  const [uploadHandlers, setFileUploadHandlers] = useState<FileUploadHandlers>({});
  const [organizationPayload, setOrganizationPayload] = useState<
    OrganizationCreateRequest | OrganizationUpdateRequest
  >();

  const [updatePayload, setUpdatePayload] = useGetSetState<DocumentUpdateRequest>();
  const [idFromCreation, setIdFromCreation] = useGetSetState<{ id: uuid }>();
  const [getUploadLinkPayload, setUploadLinkPayload] = useGetSetState<DocumentUploadUrlRequest>();
  const [localModel, setLocalModel] = usePerformantState<UnifiedOrganizationModel>();

  useUnmount(() => {
    updateEphemeralState("organizations.viewing", "portal");
  });

  const organizationId = organizationResponse?.id;

  const inEdit = useMemo(() => !!localModel?.id || !!organizationId, [localModel?.id, organizationId]);
  const hasFileToUpload = useMemo(() => Object.keys(uploadHandlers)?.length > 0, [uploadHandlers]);

  const model = useMemo(
    () =>
      inEdit
        ? localModel || { ...toUnifiedOrganizationModel(organizationResponse!), type: OrganizationType.Portal }
        : getNewPortalOrgModel(),
    [inEdit, localModel, organizationResponse],
  );

  const {
    control,
    formState: { errors },
    watch,
    reset,
    handleSubmit,
  } = useForm<UnifiedOrganizationModel>({
    resolver: OrganizationModelResolver,
    defaultValues: model,
    mode: "onSubmit",
  });

  const data = watch();

  const {
    documents = [],
    fetching,
    fetch,
    update,
    retrieveUploadLink,
  } = useDocumentsApi({
    query: {
      organizationId,
      types: [DocumentType.MASTER_AGREEMENT],
    },
    updatePayload: updatePayload(),
    uploadLinkPayload: getUploadLinkPayload(),

    onFetchError: (error: any) => {
      enqueueError(`Unable to fetch documents for ${data?.name}.`);
      logger.error(`Unable to fetch documents for ${data?.name}: ${error?.message}`, {});
    },
    onUploadLinkRetrievalError: (error: any) => {
      enqueueError("Unable to get upload link");
      logger.error(`Unable to get upload link: ${error.message}`, {});
      toggleHasDocUploadError(true);
    },
    onUpdateSuccess: () => {
      enqueueSuccess(
        `Successfully ${organizationId ? "updated" : "created"} orgaization${organizationId ? " details" : "."}`,
      );
      popFromPath(organizationId ? 2 : 1);
    },
    onUpdateError: (error: any) => {
      enqueueWarning("Partial upload; unable to update document association.");
      logger.error(`Partial upload; unable to update document association: ${error.message}`, {});
    },
  });

  const pure = useMemo(
    () => pickFromRecord(model, ["name", "salesforceIdentifier", "rubiconManager.name", "isOnboarded"]),
    [model],
  );

  const impure = useMemo(
    () => pickFromRecord(data, ["name", "salesforceIdentifier", "rubiconManager.name", "isOnboarded"]),
    [data],
  );

  const isDirty = useMemo(
    () =>
      !deepEqual(pure, impure, {
        transform: {
          isOnboarded: (value) => toBoolean(value),
        },
      }),
    [impure, pure],
  );

  const submittable = useMemo(() => ({ model: isDirty, document: hasFileToUpload }), [hasFileToUpload, isDirty]);

  const { trigger: commitOrganization, isMutating: committingOrganization } = useTriggerRequest<
    OrganizationResponse,
    OrganizationCreateRequest | OrganizationUpdateRequest
  >({
    url: `/admin/organizations${inEdit ? `/${organizationId}` : ""}`,
    method: inEdit ? "patch" : "post",
    requestBody: organizationPayload,
    swrOptions: {
      onSuccess: async (data): Promise<void> => {
        setLocalModel(toUnifiedOrganizationModel(data));
        setIdFromCreation({ id: data?.id });

        if (hasFileToUpload) await submitDocument();
        else {
          enqueueSuccess(`Successfully ${inEdit ? "updated" : "created"} orgaization${inEdit ? " details" : "."}`);
          popFromPath(organizationId ? 2 : 1);
        }
      },
      onError: (error: { data: { message: string | string[] } }): void => {
        enqueueError(
          <Stack gap={1}>
            <Stack>
              <Typography>Unable to {inEdit ? "update" : "create"} organization details.</Typography>
              {isArray(error?.data?.message) ? (
                (error?.data?.message as string[])?.map((error) => <Typography key={error}>{error}</Typography>)
              ) : (
                <Typography key={error?.data?.message as string}>{error?.data?.message}</Typography>
              )}
            </Stack>
          </Stack>,
          { persist: true, className: classes.ErrorAlert },
        );
        logger.error(`Unable to ${inEdit ? "update" : "create"} organization details: ${error?.data?.message}`, {});
      },
    },
  });

  const managerOptions = useAutoCompleteOptions<UserResponse, UserResponse>({
    data: managers,
    keys: ["id", "name"],
    label: (entry: Partial<UserResponse>) => entry?.name ?? '',
    value: (entry: Partial<UserResponse>): UserResponse => entry as UserResponse,
    postTransform: (data: UseAutoCompleteOptionsReturnEntry[]) =>
      data?.sort((a, b) => a?.label?.localeCompare(b?.label)),
  });

  const disableForm = committingOrganization || uploading;

  usePerformantEffect(() => {
    if (!!organizationId && !fetching) setTimeout(async () => await fetch());
  }, [organizationId]);

  usePerformantEffect(() => {
    if (!!organizationId && !!model) reset(model);
  }, [organizationId, model]);

  const getS3UploadApiLink = async (file: File): Promise<string> => {
    setUploadLinkPayload({
      organizationId,
      filename: file.name,
      type: DocumentType.MASTER_AGREEMENT,
      isPublic: true,
    });

    const { uploadUrl } = await retrieveUploadLink();

    return uploadUrl;
  };

  const handleDocumentsSubmission = async (): Promise<void> => {
    const documentUploadPromises = Object.entries(uploadHandlers).reduce<Promise<void>[]>(
      (accum, [inputId, handler]) => [
        ...accum,
        (handler?.({ preventDefault: () => {} } as MouseEvent<HTMLButtonElement>) || Promise.resolve())?.then(() =>
          handleExposeUploadHandler(inputId, null),
        ),
      ],
      [] as Promise<void>[],
    );

    await Promise.all(documentUploadPromises);
  };

  const handleExposeUploadHandler = (
    inputId: string,
    // eslint-disable-next-line no-unused-vars
    handler: Nullable<(event: MouseEvent<HTMLButtonElement>) => Promise<void>>,
  ): void => {
    if (handler) {
      setFileUploadHandlers((previous) => ({
        ...previous,
        [inputId]: handler,
      }));
    } else {
      setFileUploadHandlers((previous) =>
        Object.entries(previous).reduce((accum, [key, value]) => {
          if (key !== inputId)
            accum = {
              ...accum,
              [key]: value,
            };
          return accum;
        }, {}),
      );
    }
  };

  const handleFileUploadSuccess = useCallback(
    (file: File, metadata?: OnFileUploadSuccessMetaData): void => {
      const id = metadata?.s3FileId as uuid;
      const filename = file?.name;

      setUpdatePayload({
        id,
        filename,
        organizationId: !organizationId ? idFromCreation()?.id : model?.id,
        type: DocumentType.MASTER_AGREEMENT,
        isPublic: true,
      });

      setTimeout(async () => {
        await update();
      });
    },
    [idFromCreation, model?.id, organizationId, setUpdatePayload, update],
  );

  const handleFileUploadError = (): void => {
    enqueueError("Unable to upload document");
    toggleHasDocUploadError(true);
  };

  const handleDeletionSuccess = async (): Promise<void> => {
    enqueueSuccess(`Successfully deleted document for ${model?.name}`);
    await fetch();
  };

  const handleDeletionError = async (error: any): Promise<void> => {
    enqueueError(`Failed to delete document for ${model?.name}`);
    logger.error(`Failed to delete document for ${model?.name}: ${error?.message}`, {});
  };

  const submitDocument = async (): Promise<void> => {
    await handleDocumentsSubmission();
  };

  const onSubmit = (formData: UnifiedOrganizationModel): void => {
    closeSnackbar();
    toggleHasDocUploadError(false);

    if (submittable.model) {
      const payload: OrganizationCreateRequest | OrganizationUpdateRequest = {
        ...(inEdit ? { id: model?.id } : {}),
        name: formData.name,
        salesforceIdentifier: formData.salesforceIdentifier,
        rubiconManagerId: formData?.rubiconManager?.id as uuid,
        isEnabled: inEdit ? toBoolean(formData.isEnabled) : true,
        isOnboarded: toBoolean(formData.isOnboarded),
      };

      setOrganizationPayload(payload);
      setTimeout(async () => await commitOrganization());
    } else if (submittable.document) setTimeout(async () => await submitDocument());
  };

  return (
    <Container sx={{ padding: "20px 0px" }}>
      <Stack
        component="form"
        justifyContent="center"
        gap={3}
        maxWidth={1200}
        minWidth={900}
        onSubmit={handleSubmit(onSubmit)}
      >
        <Controller
          name="name"
          control={control}
          render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
            <TextField
              label="Organization Name"
              value={value ?? ""}
              InputProps={{ ref }}
              {...otherProps}
              error={!!errors?.name}
              helperText={errors?.name?.message}
              fullWidth
              disabled={disableForm}
            />
          )}
        />
        <Controller
          name="salesforceIdentifier"
          control={control}
          render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
            <TextField
              label="Salesforce Account ID"
              value={value ?? ""}
              InputProps={{ ref }}
              {...otherProps}
              error={!!errors?.salesforceIdentifier}
              helperText={errors?.salesforceIdentifier?.message}
              fullWidth
              disabled={disableForm}
            />
          )}
        />
        <Controller
          name="rubiconManager"
          control={control}
          render={({ field: { value, onChange, ...otherProps } }): JSX.Element => {
            const selectedOption = managerOptions.find((entry) => entry?.value?.id === value?.id) ?? null;
            return (
              <Autocomplete
                options={managerOptions}
                value={selectedOption}
                onChange={(_, selection) => onChange(selection?.value)}
                id="rubiconManager"
                getOptionKey={(option: UseAutoCompleteOptionsReturnEntry<UserResponse>) => option?.value?.id}
                getOptionLabel={(option: UseAutoCompleteOptionsReturnEntry<UserResponse>) => option?.label}
                renderInput={({ InputProps, ...params }) => (
                  <TextField
                    {...params}
                    InputProps={{
                      ...InputProps,
                    }}
                    label="Account Manager"
                    {...otherProps}
                    error={!!errors?.rubiconManager}
                    helperText={errors?.rubiconManager?.message}
                    fullWidth
                    disabled={disableForm}
                  />
                )}
                fullWidth
              />
            );
          }}
        />
        <Stack>
          <Stack
            className={classcat([classes.NonInputFieldSection, { [classes.Error]: !!errors?.isOnboarded }])}
            direction="row"
            sx={{ width: "100%" }}
          >
            <Typography className={classes.GroupLabel} variant="body1">
              Onboarding Status:
            </Typography>
            <Controller
              name="isOnboarded"
              control={control}
              defaultValue={data?.isOnboarded}
              render={({ field: { ref, value, ...otherProps }, formState: { errors } }): JSX.Element => (
                <FormControl error={!!errors?.isOnboarded}>
                  <RadioGroup className={classes.RadioGroup} ref={ref} value={value ?? null} {...otherProps} row>
                    <FormControlLabel
                      label={<Typography variant="body1">Onboarded</Typography>}
                      value={true}
                      control={<Radio size="medium" />}
                      disabled={disableForm}
                    />
                    <FormControlLabel
                      label={<Typography variant="body1">Not Onboarded</Typography>}
                      value={false}
                      control={<Radio size="medium" />}
                      disabled={disableForm}
                    />
                  </RadioGroup>
                </FormControl>
              )}
            />
          </Stack>
          <Typography className={classes.NonInputFieldHelperText}>{errors?.isOnboarded?.message}</Typography>
        </Stack>

        <Stack
          className={classcat([classes.NonInputFieldSection, { [classes.Error]: hasDocUploadError }])}
          sx={{ width: "100%" }}
          gap={2}
        >
          <Typography color={COLORS.rubiconGreen} fontSize={15} fontWeight="bold">
            Signed agreement{documents?.length > 1 ? "s" : ""}:
          </Typography>

          <Stack gap={0.5}>
            <Maybe condition={documents.length > 0}>
              <AttachmentList
                attachments={documents}
                deleteConfirmation={{
                  title: "Confirm document deletion",
                  content: ({ document }) => (
                    <span>
                      Are you sure you want to delete <strong>{DocumentTypeUILabel[document.type]}</strong> for{" "}
                      <strong>{model.name}</strong>?
                    </span>
                  ),
                }}
                onRemoveSuccess={handleDeletionSuccess}
                onRemoveError={handleDeletionError}
              />
              <Divider sx={{ marginBottom: 1.5 }} />
            </Maybe>
            <InlineUploader
              inputId={`choose ${documents?.length ? "additional " : ""}signed agreement`}
              uploadLink={getS3UploadApiLink}
              uploadText={`choose ${documents?.length ? "additional " : ""}signed agreement`}
              clearOnFileUploadSuccess
              allowedExtensions={["application/pdf"]}
              externallyUpload
              onUploadingStatusChange={(status) => toggleUploading(status)}
              onExposeUploadHandler={handleExposeUploadHandler}
              onFileUploadSuccess={handleFileUploadSuccess}
              onFileUploadError={handleFileUploadError}
              disabled={disableForm}
            />
          </Stack>

          <Typography variant="caption" fontWeight="100">
            * Accepted file type: PDF | Max file size: 15MB
          </Typography>
        </Stack>

        <Stack direction="row" justifyContent="space-between">
          <Button
            className={classes.ActionButton}
            color="error"
            disabled={committingOrganization || uploading}
            onClick={() => popFromPath(organizationId ? 2 : 1)}
          >
            Cancel
          </Button>
          <LoadingButton
            className={classes.ActionButton}
            type="submit"
            variant="contained"
            loading={committingOrganization || uploading}
            disabled={uploading || (inEdit && !isDirty && !hasFileToUpload)}
          >
            {organizationId ? "Update" : "Create"}
          </LoadingButton>
        </Stack>
      </Stack>
    </Container>
  );
};

export default PortalOrganizationForm;
