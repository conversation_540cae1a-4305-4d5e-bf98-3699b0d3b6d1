import React, { useState, BaseSyntheticEvent, useContext, useEffect, useMemo, useCallback } from "react";
import { AxiosContext } from "@providers/axios-provider";
import {
  Box,
  Button,
  Dialog,
  DialogContent,
  DialogActions,
  Typography,
  Stack,
  DialogTitle,
  FormControlLabel,
  Radio,
  FormControl,
  RadioGroup,
} from "@mui/material";
import { CounterpartyResponse, OrganizationResponse } from "@rubiconcarbon/shared-types";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import ActionButton from "@components/ui/action-button/action-button-enhanced";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { useLogger } from "@providers/logging";
import { BaseDialogProps } from "@models/dialogs";
import { OrganizationType } from "@constants/organization-type.enum";
import { UnifiedOrganizationModel } from "@models/organization";

const cancelBtnStyle = {
  height: 35,
  color: "rgba(0, 0, 0, 0.87)",
  borderRadius: "5px",
  textTransform: "capitalize",
  fontWeight: 500,
  backgroundColor: "rgba(224, 224, 224, 1)",
  "&.Mui-disabled": {
    color: "gray !important",
  },
  "&:hover": {
    backgroundColor: "rgba(224, 224, 224, 1)",
    boxShadow: "rgb(0 0 0 / 10%) 0px 1px 1px",
  },
};

const radioLabelStyle = {
  marginTop: 1,
  marginBottom: 1,
};

interface OnboardingModalProps extends BaseDialogProps {
  type: OrganizationType;
  model?: UnifiedOrganizationModel;
  onSave: () => void;
}

export default function OnboardingModal({
  type,
  model,
  isOpen,
  onClose,
  onSave,
}: OnboardingModalProps): JSX.Element | null {
  const [isConfirmation, setIsConfirmation] = useState<boolean>(false);
  const [isOnboarded, setIsOnboarded] = useState<string>("");
  const { api } = useContext(AxiosContext);
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const { logger } = useLogger();

  const isACounterparty = useMemo(() => type === OrganizationType.Counterparty, [type]);

  useEffect(() => {
    if (model) {
      setIsOnboarded(model?.isOnboarded === true ? "true" : "false");
    }
  }, [model]);

  const onSubmitHandler = (event: BaseSyntheticEvent): void => {
    event.preventDefault();
    setIsConfirmation(true);
  };

  const onCloseHandler = useCallback((): void => {
    setIsConfirmation(false);
    onClose();
  }, [onClose]);

  const submitOnboardStatus = useCallback(async (): Promise<void> => {
    const payload = {
      isOnboarded: isOnboarded === "true" ? true : false,
    };

    try {
      await api.patch<OrganizationResponse | CounterpartyResponse>(
        `admin/${isACounterparty ? "counterparties" : "organizations"}/${model?.id}`,
        payload,
      );
      enqueueSuccess(`${isACounterparty ? "Counterparty" : "Organization"} status was updated succsessfully`);
      setIsConfirmation(false);
      onSave();
    } catch (error: any) {
      if (error?.response?.data?.message) {
        logger.error(error.response.data.message, {});
        enqueueError(
          Array.isArray(error.response.data.message)
            ? error.response.data.message.join(", ")
            : error.response.data.message,
        );
      } else enqueueError(`Unable update ${isACounterparty ? "counterparty" : "organization"}  status`);
    }
  }, [isOnboarded, api, isACounterparty, model?.id, enqueueSuccess, onSave, enqueueError, logger]);

  const isInputChanged = useMemo(
    () => (model?.isOnboarded === true ? "true" : "false") !== isOnboarded,
    [isOnboarded, model?.isOnboarded],
  );
  const dialogHead = useMemo(
    () => (
      <Box mt={1}>
        <Typography
          variant="body2"
          component="p"
          sx={{
            color: "rgba(0, 0, 0, 1)",
            backgroundColor: "white",
            fontWeight: 500,
            fontSize: "24px",
            width: "501px",
            border: "none",
          }}
        >
          {isConfirmation ? "Please Confirm" : "Change Onboard Status"}
        </Typography>
      </Box>
    ),
    [isConfirmation],
  );

  const dialogActions = useMemo(
    () => (
      <Stack direction="row" gap={3}>
        <Button variant="text" onClick={onCloseHandler} sx={cancelBtnStyle}>
          Cancel
        </Button>

        <Maybe condition={!isConfirmation}>
          <ActionButton
            type="submit"
            form="onboarding-form"
            style={{ fontWeight: 500, width: "100px", textTransform: "capitalize" }}
            isDisabled={!isInputChanged}
          >
            save
          </ActionButton>
        </Maybe>

        <Maybe condition={isConfirmation}>
          <Stack direction="row" sx={{ marginRight: "-10px" }}>
            <ActionButton
              style={{
                fontWeight: 500,
                width: "140px",
                textTransform: "capitalize",
              }}
              onClickHandler={submitOnboardStatus}
            >
              Yes, Continue
            </ActionButton>
          </Stack>
        </Maybe>
      </Stack>
    ),
    [isConfirmation, isInputChanged, onCloseHandler, submitOnboardStatus],
  );

  const onBoardChangeHandler = (event: React.ChangeEvent<HTMLInputElement>): void => {
    setIsOnboarded(event.target.value);
  };

  return (
    <Dialog open={isOpen} onClose={onCloseHandler} fullWidth>
      <DialogTitle sx={{ backgroundColor: "white" }}>{dialogHead}</DialogTitle>

      <DialogContent sx={{ height: isConfirmation ? "170px" : "110px", overflow: "hidden" }}>
        <Box mt={-1} sx={{ width: "100%" }}>
          <Maybe condition={!isConfirmation}>
            <form id="onboarding-form" onSubmit={onSubmitHandler}>
              <Stack direction="row" sx={{ width: "100%" }}>
                <FormControl fullWidth sx={{ marginTop: -3 }}>
                  <RadioGroup
                    aria-labelledby="onboarding-group"
                    name="controlled-radio-buttons-group"
                    value={isOnboarded}
                    onChange={onBoardChangeHandler}
                    sx={{ paddingTop: "7px" }}
                    row
                  >
                    <FormControlLabel
                      sx={radioLabelStyle}
                      value={"true"}
                      control={<Radio />}
                      label={<Typography variant="body1">Onboarded</Typography>}
                    />
                    <FormControlLabel
                      sx={radioLabelStyle}
                      value={"false"}
                      control={<Radio />}
                      label={<Typography variant="body1">Not Onboarded</Typography>}
                    />
                  </RadioGroup>
                </FormControl>
              </Stack>
            </form>
          </Maybe>
          <Maybe condition={isConfirmation}>
            <Stack gap={2}>
              <Typography>
                You are about to change the onboard status of <b>{model?.name}</b> to{" "}
                <b>{isOnboarded === "true" ? "Onboarded" : "Not Onboarded"} </b>
              </Typography>
              <Typography>Do you still wish to continue?</Typography>
            </Stack>
          </Maybe>
        </Box>
      </DialogContent>
      <DialogActions
        sx={{
          backgroundColor: "rgba(250, 250, 250, 1)",
          paddingRight: "24px",
          justifyContent: "right",
          border: "none",
        }}
      >
        {dialogActions}
      </DialogActions>
    </Dialog>
  );
}
