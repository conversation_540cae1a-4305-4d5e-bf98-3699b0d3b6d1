import { CounterpartyFeeType, CounterpartyResponse, OrganizationResponse } from "@rubiconcarbon/shared-types";
import dateFormatter from "@/utils/formatters/date-formatter";
import { OrganizationType } from "@constants/organization-type.enum";
import { UnifiedOrganizationModel } from "@models/organization";
import { calculator } from "@rubiconcarbon/frontend-shared";

export const toUnifiedOrganizationModel = (
  row: OrganizationResponse | CounterpartyResponse,
): UnifiedOrganizationModel => {
  const isACounterParty = 'tradeConfirmEmails' in row;

  return {
    ...row,
    type: OrganizationType[isACounterParty ? OrganizationType.Counterparty : OrganizationType.Portal],
    counterparty: {
      confirmationEmails: isACounterParty
        ? (row as CounterpartyResponse)?.tradeConfirmEmails?.map((value) => ({ value }))
        : [],
      defaultFees: isACounterParty
        ? (row as CounterpartyResponse)?.defaultFees?.map((fee) => ({
            type: fee.type,
            feeType: fee.feeType,
            fee: calculator(fee[fee.feeType.replace(/[-_]+(.)/g, (_, c: string) => c.toUpperCase()).concat("Fee")])
              .multiply(fee.feeType === CounterpartyFeeType.PERCENTAGE ? 100 : 1)
              .calculate()
              .toString(),
          }))
        : [],
    },
    statusF: row.isEnabled ? "Enabled" : "Disabled",
    onboardingStatusF: row.isOnboarded ? "Onboarded" : "Not Onboarded",
    createdAtF: dateFormatter(row?.createdAt?.toString(), "MM/dd/yyyy hh:mm:ss a"),
  };
};
