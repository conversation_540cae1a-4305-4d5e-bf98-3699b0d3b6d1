import { MISSING_DATA } from "@constants/constants";
import useBreadcrumbs from "@providers/breadcrumb-provider";
import { useLogger } from "@providers/logging";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import usePerformantEffect from "@/hooks/use-performant-effect";
import { Box, Button, capitalize, Grid, Paper, Stack, SxProps, Typography, useMediaQuery } from "@mui/material";
import {
  calculator,
  GenericTabItem,
  GenericTabKey,
  GenericTabs,
  Match,
  numberFormat,
} from "@rubiconcarbon/frontend-shared";
import {
  AdminBookQueryResponse,
  AllocationResponse,
  AssetType,
  DocumentResponse,
  ModelPortfolioRelations,
  ModelPortfolioResponse,
  OrganizationResponse,
  PermissionEnum,
  TransactionType,
  TrimmedProjectVintageResponse,
  UserResponse,
  UserStatus,
} from "@rubiconcarbon/shared-types";
import { PropsWithChildren, ReactNode, useContext, useMemo } from "react";
import { UnifiedOrgStatusChip } from "@components/ui/unified-org-status-chip/unified-org-status-chip";
import useNavigation from "@/hooks/use-navigation";
import COLORS from "@components/ui/theme/colors";
import { useStoreProvider } from "@providers/store-provider";
import { toTrimmedTransactionModel } from "@utils/helpers/transaction/to-transaction-models";
import { UserRoleLabel } from "@constants/user-role";
import { DocumentTypeUILabel } from "@constants/documents";
import dateFormatterEST from "@/utils/formatters/est-date-formatter";
import { AuthContext } from "@providers/auth-provider";
import { toUnifiedOrganizationModel } from "../../utilities/to-model";
import CustomerTable from "./customer-table";
import { HOLDING_COLUMNS, HoldingModel } from "../constants/holding-columns";
import { TRANSACTION_COLUMNS } from "../constants/transaction-columns";
import { DOCUMENT_COLUMNS, DocumentModel } from "../constants/document-columns";
import { QUOTE_COLUMNS, QuoteModel } from "../constants/quote-columns";
import { USER_COLUMNS, UserModel } from "../constants/user-columns";

import organizationClasses from "../../styles/unified-organizations.module.scss";

export type CustomerTableTabsTypes = "holdings" | "transactions" | "quotes" | "documents" | "users";

type LabelValuePairProps = {
  label: ReactNode;
  orientation?: "horizontal" | "vertical";
  sx?: SxProps;
};

const LabelValuePair = ({
  label,
  orientation = "horizontal",
  children,
  sx,
}: PropsWithChildren<LabelValuePairProps>): JSX.Element => (
  <Stack
    direction={orientation === "horizontal" ? "row" : "column"}
    justifyContent={orientation === "horizontal" ? "flex-start" : "center"}
    alignItems="flex-start"
    gap={3}
    sx={sx}
  >
    <Box>{label}</Box>
    <Box> {children}</Box>
  </Stack>
);

const PortalOrganization = ({
  organizationResponse,
  customerPortfolioResponse,
}: {
  organizationResponse: OrganizationResponse;
  customerPortfolioResponse: AdminBookQueryResponse;
}): JSX.Element => {
  const { user } = useContext(AuthContext);
  const { logger } = useLogger();
  const { enqueueError } = useSnackbarVariants();
  const { updateBreadcrumbName } = useBreadcrumbs();
  const { popFromPath } = useNavigation();
  const { ephemeralState, updateEphemeralState } = useStoreProvider();
  const isNotLargeScreen = useMediaQuery("(max-width: 1290px)");

  const { customer: customerTabState } = ephemeralState;
  const { viewing: tab } = customerTabState;

  const customer = useMemo(() => customerPortfolioResponse?.data?.at(0), [customerPortfolioResponse?.data]);
  const organization = toUnifiedOrganizationModel(organizationResponse as any);

  const hasTransactionsPermission = useMemo(() => user?.hasPermission(PermissionEnum.TRANSACTIONS_READ), [user]);
  const hasCustomerQuotesPermission = useMemo(() => user?.hasPermission(PermissionEnum.MODEL_PORTFOLIOS_READ), [user]);
  const hasDocumentsPermission = useMemo(() => user?.hasPermission(PermissionEnum.DOCUMENTS_READ), [user]);
  const hasUsersPermission = useMemo(() => user?.hasPermission(PermissionEnum.USERS_READ), [user]);

  const tabs = useMemo(
    () =>
      [
        {
          key: "holdings",
          data: "Holdings",
        },
        hasTransactionsPermission
          ? {
              key: "transactions",
              data: "Transactions",
            }
          : null,
        hasCustomerQuotesPermission
          ? {
              key: "quotes",
              data: "Customer Quotes",
            }
          : null,
        hasDocumentsPermission
          ? {
              key: "documents",
              data: "Documents",
            }
          : null,
        hasUsersPermission
          ? {
              key: "users",
              data: "Users",
            }
          : null,
      ].filter((entry) => !!entry),
    [hasTransactionsPermission, hasCustomerQuotesPermission, hasDocumentsPermission, hasUsersPermission],
  ) as GenericTabItem<CustomerTableTabsTypes, string>[];

  usePerformantEffect(() => {
    if (customer?.name) updateBreadcrumbName("Organizations", customer?.name);
  }, [customer?.name, updateBreadcrumbName]);

  const renderTab = (tab: string): JSX.Element => (
    <Typography className={organizationClasses.TabText} color={COLORS.rubiconGreen} variant="body2">
      {tab}
    </Typography>
  );

  return (
    <Stack gap={2}>
      <Stack component={Paper} padding={2} gap={4}>
        <Grid container gap={0.5} rowGap={2}>
          <Grid item xs={12} md={isNotLargeScreen ? 5 : 3}>
            <LabelValuePair
              label={
                <Typography variant="body2" width={150} fontWeight="bold">
                  Account Manager:{" "}
                </Typography>
              }
            >
              <Typography variant="body2" color="primary">
                {organization?.rubiconManager?.name ?? MISSING_DATA}
              </Typography>
            </LabelValuePair>
          </Grid>

          <Grid item xs={12} md={isNotLargeScreen ? 5 : 3}>
            <LabelValuePair
              label={
                <Typography variant="body2" width={150} fontWeight="bold">
                  Salesforce Account ID:{" "}
                </Typography>
              }
            >
              <Typography variant="body2" color="primary">
                {organization?.salesforceIdentifier ?? MISSING_DATA}
              </Typography>
            </LabelValuePair>
          </Grid>

          <Grid item xs={12} md={isNotLargeScreen ? 5 : 3}>
            <LabelValuePair
              label={
                <Typography variant="body2" width={150} fontWeight="bold">
                  Created Date:{" "}
                </Typography>
              }
            >
              <Typography variant="body2" color="primary">
                {organization?.createdAtF ?? MISSING_DATA}
              </Typography>
            </LabelValuePair>
          </Grid>
        </Grid>

        <Grid container gap={0.5} rowGap={2}>
          <Grid item xs={12} md={isNotLargeScreen ? 5 : 3}>
            <LabelValuePair
              label={
                <Typography variant="body2" width={150} fontWeight="bold">
                  Status:{" "}
                </Typography>
              }
            >
              <UnifiedOrgStatusChip row={organization} path={{ value: "isEnabled", label: "statusF" }} />
            </LabelValuePair>
          </Grid>

          <Grid item xs={12} md={isNotLargeScreen ? 5 : 3}>
            <LabelValuePair
              label={
                <Typography variant="body2" width={150} fontWeight="bold">
                  Onboard Status:{" "}
                </Typography>
              }
            >
              <UnifiedOrgStatusChip row={organization} path={{ value: "isOnboarded", label: "onboardingStatusF" }} />
            </LabelValuePair>
          </Grid>
        </Grid>

        <Button variant="contained" sx={{ width: 80, height: 30 }} onClick={() => popFromPath(1)}>
          Back
        </Button>
      </Stack>
      <Stack>
        <GenericTabs
          tabs={tabs}
          value={tab}
          renderTab={renderTab}
          onTabChange={(key: GenericTabKey): void => updateEphemeralState("customer.viewing", key)}
          classes={{
            root: organizationClasses.Tabs,
            tab: organizationClasses.Tab,
            active: organizationClasses.Active,
          }}
        />
        <Match
          value={tab}
          cases={[
            {
              case: "holdings",
              component: (
                <CustomerTable
                  tab={tab}
                  url="admin/books/{id}/assets/holdings"
                  pathParams={{ id: customer?.id }}
                  queryParams={{
                    assetTypes: [AssetType.RCT, AssetType.REGISTRY_VINTAGE],
                  }}
                  columns={HOLDING_COLUMNS}
                  searchKeys={["productF", "type", "amountAllocated", "fop"]}
                  sorts={{
                    amountAllocated: "desc",
                  }}
                  onError={(error: any) => {
                    enqueueError("Unable to fetch customer portfolio holdings.");
                    logger.error(`Unable to fetch customer portfolio holdings: ${error?.message}`, {});
                  }}
                  toRowModel={(row: AllocationResponse, allRows: AllocationResponse[]): HoldingModel => {
                    let vintageName = "";
                    let registryProjectId = "";
                    let projectId = "";
                    let projectName = "";
                    let riskBufferPercentage = null;
                    let rctStandard = null;
                    let isScienceTeamApproved = null;
                    let suspended = null;
                    let productF = "";

                    const sum = allRows?.reduce((sum, { amountAllocated }) => sum + amountAllocated, 0);

                    const isRctVintage = 'project' in row?.detailedAsset;

                    if (isRctVintage) {
                      vintageName = row?.detailedAsset?.name;
                      registryProjectId = row?.detailedAsset?.["project"]?.registryProjectId;
                      projectId = row?.detailedAsset?.["project"]?.id;
                      projectName = row?.detailedAsset?.["project"]?.name;
                      riskBufferPercentage = (row?.detailedAsset as TrimmedProjectVintageResponse)
                        ?.riskBufferPercentage;
                      rctStandard = row?.detailedAsset?.["project"]?.rctStandard;
                      isScienceTeamApproved = row?.detailedAsset?.["project"]?.isScienceTeamApproved;
                      suspended = row?.detailedAsset?.["project"]?.suspended;

                      productF = `${vintageName} ${registryProjectId} ${projectName}`;
                    } else productF = row?.detailedAsset?.name;

                    return {
                      id: row?.detailedAsset?.id,
                      productF,
                      type: isRctVintage ? "Project" : "Portfolio",
                      amountAllocated: row?.amountAllocated,
                      fop: row?.amountAllocated / sum,
                      portfolioName: row?.detailedAsset?.name,
                      vintageName,
                      registryProjectId,
                      projectId: projectId as any,
                      projectName,
                      riskBufferPercentage,
                      rctStandard,
                      isScienceTeamApproved,
                      suspended,
                    };
                  }}
                  toolbarItems={(allRows: AllocationResponse[]) => (
                    <div>
                      Total Holdings:{" "}
                      <strong>
                        {numberFormat(
                          allRows?.reduce((sum, { amountAllocated }) => sum + amountAllocated, 0),
                          { fallback: MISSING_DATA },
                        )}
                      </strong>
                    </div>
                  )}
                  styles={{
                    root: {
                      maxHeight: "calc(100vh - 435px)",
                    },
                    header: {
                      "div [class^='MuiStack-root']": {
                        flexDirection: "row-reverse",
                        gap: 2,
                      },
                    },
                  }}
                  triggerCondition={!!customer?.id}
                />
              ),
            },
            {
              case: "transactions",
              component: (
                <CustomerTable
                  tab={tab}
                  url="admin/transactions"
                  queryParams={{
                    counterpartyId: organization?.id,
                    types: [
                      TransactionType.TRADE,
                      TransactionType.PURCHASE,
                      TransactionType.RETIREMENT,
                      TransactionType.FORWARD_LINE_ITEM,
                    ],
                  }}
                  columns={TRANSACTION_COLUMNS}
                  searchKeys={[
                    "uiKey",
                    "typeF",
                    "counterpartyName",
                    "productF",
                    "totalQuantity",
                    "totalPrice",
                    "status",
                    "updatedAtF",
                  ]}
                  sorts={{
                    updatedAtF: "desc",
                  }}
                  onError={(error: any) => {
                    enqueueError("Unable to fetch transactions.");
                    logger.error(`Unable to fetch transactions: ${error?.message}`, {});
                  }}
                  toRowModel={toTrimmedTransactionModel}
                  styles={{
                    root: {
                      maxHeight: "calc(100vh - 435px)",
                    },
                  }}
                />
              ),
            },
            {
              case: "quotes",
              component: (
                <CustomerTable
                  tab={tab}
                  url="admin/model-portfolios"
                  queryParams={{
                    organizationId: organization?.id,
                    includeRelations: [ModelPortfolioRelations.MODEL_PORTFOLIO_COMPONENTS],
                  }}
                  columns={QUOTE_COLUMNS}
                  searchKeys={["uiKey", "name", "status", "unitPrice", "priceEstimate", "totalAmount", "updatedAtF"]}
                  sorts={
                    {
                      updatedAtF: "desc",
                    } as any
                  }
                  onError={(error: any) => {
                    enqueueError("Unable to fetch customer quotes.");
                    logger.error(`Unable to fetch customer quotes: ${error?.message}`, {});
                  }}
                  toRowModel={(row: ModelPortfolioResponse): QuoteModel => {
                    const totalAmount = row?.modelPortfolioComponents?.reduce(
                      (sum, { amountAllocated }) => sum + amountAllocated,
                      0,
                    );

                    return {
                      ...row,
                      unitPrice: calculator(row?.priceEstimate, { treatNothingAsNaN: true })
                        .divide(totalAmount)
                        .calculate()
                        ?.toNumber(),
                      totalAmount,
                      updatedAtF: dateFormatterEST(row?.updatedAt?.toLocaleString()),
                    };
                  }}
                  styles={{
                    root: {
                      maxHeight: "calc(100vh - 435px)",
                    },
                  }}
                />
              ),
            },
            {
              case: "documents",
              component: (
                <CustomerTable
                  tab={tab}
                  url="admin/documents"
                  queryParams={{
                    organizationId: organization?.id,
                  }}
                  columns={DOCUMENT_COLUMNS}
                  searchKeys={["filename", "typeF", "createdAtF"]}
                  sorts={{
                    createdAtF: "asc",
                  }}
                  onError={(error: any) => {
                    enqueueError("Unable to fetch customer documents.");
                    logger.error(`Unable to fetch customer documents: ${error?.message}`, {});
                  }}
                  toRowModel={(row: DocumentResponse): DocumentModel => {
                    return {
                      ...row,
                      typeF: DocumentTypeUILabel[row?.type],
                      createdAtF: dateFormatterEST(row?.createdAt?.toLocaleString()),
                    };
                  }}
                  styles={{
                    root: {
                      maxHeight: "calc(100vh - 435px)",
                    },
                  }}
                />
              ),
            },
            {
              case: "users",
              component: (
                <CustomerTable
                  tab={tab}
                  url="admin/organizations/{id}/users"
                  pathParams={{ id: organization?.id }}
                  columns={USER_COLUMNS}
                  searchKeys={["name", "rolesF", "email", "createdAt", "statusF"]}
                  toResponse={(data: UserResponse[]) => ({
                    data,
                    page: {
                      offset: 0,
                      limit: data?.length || 0,
                      size: data?.length || 0,
                    },
                  })}
                  onError={(error: any) => {
                    enqueueError("Unable to fetch customer users.");
                    logger.error(`Unable to fetch customer users: ${error?.message}`, {});
                  }}
                  toRowModel={(row: UserResponse): UserModel => {
                    return {
                      ...row,
                      rolesF: row?.organization?.userRoles?.reduce(
                        (accum, role, index) => accum?.concat(index === 0 ? "" : ", ", UserRoleLabel[role]),
                        "",
                      ),
                      createdAt: row?.createdAt,
                      statusF: capitalize(row?.status),
                      statusBool: row?.status === UserStatus.ENABLED,
                    };
                  }}
                  styles={{
                    root: {
                      maxHeight: "calc(100vh - 435px)",
                    },
                  }}
                />
              ),
            },
          ]}
        />
      </Stack>
    </Stack>
  );
};

export default PortalOrganization;
