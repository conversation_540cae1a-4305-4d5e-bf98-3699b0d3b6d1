import { GenericTableFieldSizeEnum } from "@components/ui/generic-table/constants/generic-table-field-size.enum";
import { GenericTableColumn } from "@components/ui/generic-table/types/generic-table-column";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import ProductName from "@components/ui/product-name/product-name";
import COLORS from "@components/ui/theme/colors";
import { AssetOrder, TrimmedTransactionModel } from "@models/transaction";
import { Typography } from "@mui/material";
import { AssetType, TransactionType } from "@rubiconcarbon/shared-types";
import Link from "next/link";
import StatusChip from "@components/ui/status-chip/StatusChip";
import { TransactionStatusToLabel } from "@constants/transaction-status";
import { generatePath, px, toNumber } from "@rubiconcarbon/frontend-shared";
import { MISSING_DATA } from "@constants/constants";

import statusClasses from "../../styles/status-column-label.module.scss";

export const TRANSACTION_COLUMNS: GenericTableColumn<TrimmedTransactionModel>[] = [
  {
    field: "updatedAtF",
    label: "Updated Date",
    type: "datetime",
    width: GenericTableFieldSizeEnum.flexmedium,
    fixedWidth: true,
  },
  {
    field: "uiKey",
    label: "Transaction Key",
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
    renderDataCell: (row: GenericTableRowModel<TrimmedTransactionModel>): JSX.Element => {
      const baseUrlPath = ""
        .concat(
          row?.type === TransactionType.RETIREMENT
            ? "/retirements"
            : row?.type === TransactionType.FORWARD_LINE_ITEM
              ? "/trading/forward-delivery"
              : "/trading/transactions",
        )
        ?.concat(row?.type === TransactionType.FORWARD_LINE_ITEM ? "" : "/{id}");

      const path = generatePath(
        baseUrlPath,
        px({ id: TransactionType.FORWARD_LINE_ITEM === row?.type ? null : row?.id }, [null, undefined]),
        px(
          {
            type:
              TransactionType.RETIREMENT === row?.type
                ? null
                : [TransactionType.TRADE, TransactionType.FORWARD_LINE_ITEM].includes(row?.type)
                  ? row?.subtype
                  : row?.type,
            search: row?.type === TransactionType.FORWARD_LINE_ITEM ? row?.uiKey : null,
          },
          [null, undefined],
        ),
      );

      return (
        <Link href={`${path}`} style={{ textUnderlineOffset: 3, textDecorationColor: COLORS.rubiconGreen }}>
          <Typography color={COLORS.rubiconGreen} fontSize={14} fontWeight={300}>
            {row?.uiKey}
          </Typography>
        </Link>
      );
    },
  },
  {
    field: "productF",
    label: "Product",
    width: GenericTableFieldSizeEnum.large,
    maxWidth: GenericTableFieldSizeEnum.xxlarge,
    renderDataCell: (row: TrimmedTransactionModel): JSX.Element => {
      const assets = row?.assetFlows?.map((flow) => ({
        ...px({ rct: flow?.asset?.type === AssetType.RCT && { id: flow?.asset?.id, name: flow?.asset?.name } }, [
          undefined,
          null,
          false,
        ]),
        projectVintage: {
          name: flow?.asset?.label,
          project: {
            id: flow?.asset?.projectId,
            name: flow?.asset?.name,
            registryProjectId: flow?.asset?.registryProjectId,
          },
        },
      })) as AssetOrder[];

      return <ProductName assets={assets} addTags={{ rct: false, suspended: false }} style={{ fontSize: 14 }} />;
    },
  },
  {
    field: "typeF",
    label: "Type",
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
  },
  {
    field: "totalQuantity",
    label: "Volume",
    type: "number",
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
  },
  {
    field: "totalPrice",
    label: "Total Value",
    type: "money",
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
    deriveDataValue: (row) => (row?.type === TransactionType.RETIREMENT ? MISSING_DATA : toNumber(row?.totalPrice)),
  },
  {
    field: "status",
    label: "Status",
    width: GenericTableFieldSizeEnum.flexsmall,
    fixedWidth: true,
    headerCellClass: statusClasses.StatusHeaderLabel,
    deriveDataValue: (row): string => {
      if (!row?.status) return '';
      return TransactionStatusToLabel[row.status as keyof typeof TransactionStatusToLabel] ?? '';
    },
    renderDataCell: (row) => <StatusChip status={row?.status} />,
  },
];
