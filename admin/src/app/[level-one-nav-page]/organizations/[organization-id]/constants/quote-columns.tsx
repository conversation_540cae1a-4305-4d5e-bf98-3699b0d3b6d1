import { GenericTableFieldSizeEnum } from "@components/ui/generic-table/constants/generic-table-field-size.enum";
import { GenericTableColumn } from "@components/ui/generic-table/types/generic-table-column";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import COLORS from "@components/ui/theme/colors";
import { IconButton } from "@mui/material";
import { ModelPortfolioResponse } from "@rubiconcarbon/shared-types";
import { PortfolioStatusMapping } from "@/mappers/portfolio-status-mapper";
import useFileDownloader, { DownloadResult } from "@hooks/use-file-downloader";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { useCallback } from "react";

import PDFIcon from "@components/icons/pdf-icon";

export type QuoteModel = {
  unitPrice: number;
  totalAmount: number;
  updatedAtF: string;
} & ModelPortfolioResponse;

const ActionsCell = ({ row }: { row: GenericTableRowModel<QuoteModel> }): JSX.Element => {
  const { download } = useFileDownloader();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();

  const downloadPDFHandler = useCallback(async () => {
    const result: DownloadResult = await download(
      `Quote_Summary_${row?.name}`,
      `reporting/pdf/model-portfolio?id=${row?.id}`,
    );
    if (result?.isSuccess) {
      enqueueSuccess("Quote summary exported successfully");
    } else {
      enqueueError("Unable to download quote summary");
    }
  }, [download, row?.name, row?.id, enqueueSuccess, enqueueError]);

  return (
    <IconButton
      sx={{ color: COLORS.rubiconGreen, width: 36, height: 36 }}
      disabled={!row?.priceEstimate}
      onClick={downloadPDFHandler}
    >
      <PDFIcon disabled={!row?.priceEstimate} />
    </IconButton>
  );
};

export const QUOTE_COLUMNS: GenericTableColumn<QuoteModel>[] = [
  {
    field: "uiKey",
    label: "Portfolio ID",
    width: GenericTableFieldSizeEnum.flexmedium,
    fixedWidth: true,
  },
  {
    field: "name",
    label: "Portfolio Name",
    minWidth: GenericTableFieldSizeEnum.flexmedium,
    maxWidth: GenericTableFieldSizeEnum.large,
  },
  {
    field: "status",
    label: "Status",
    width: GenericTableFieldSizeEnum.flexmedium,
    fixedWidth: true,
    transformDataValue: (value: string) => PortfolioStatusMapping[(!value ? "None" : value) as keyof typeof PortfolioStatusMapping],
  },
  {
    field: "unitPrice",
    label: "Price",
    type: "money",
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
  },
  {
    field: "totalAmount",
    label: "Quantity",
    type: "number",
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
  },
  {
    field: "priceEstimate",
    label: "Total Value",
    type: "money",
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
  },
  {
    field: "updatedAtF",
    label: "Last Update",
    type: "datetime",
    width: GenericTableFieldSizeEnum.flexmedium,
    fixedWidth: true,
  },
  {
    field: "actions" as any,
    label: "Actions",
    type: "action",
    sortable: false,
    exportable: false,
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
    renderDataCell: (row): JSX.Element => <ActionsCell row={row} />,
  },
];
