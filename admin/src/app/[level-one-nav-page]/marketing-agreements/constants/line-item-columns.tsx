import { GenericTableColumn } from "@components/ui/generic-table/types/generic-table-column";
import {
  Box,
  Button,
  capitalize,
  Chip,
  Divider,
  IconButton,
  Menu,
  MenuItem,
  Stack,
  Tooltip,
  Typography,
} from "@mui/material";
import { MarketingAgreementStatus, PermissionEnum } from "@rubiconcarbon/shared-types";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import { MouseEvent, MutableRefObject, useMemo } from "react";
import useGenericTableRowActions from "@components/ui/generic-table/hooks/use-generic-table-row-actions";
import useGenericTableRowState from "@components/ui/generic-table/hooks/use-generic-table-row-state";
import { Maybe, NO_OP, Nullable, usePopperState } from "@rubiconcarbon/frontend-shared";
import {
  AddRounded,
  ArrowDropDownRounded,
  CancelRounded,
  CheckRounded,
  DeleteRounded,
  EditRounded,
} from "@mui/icons-material";
import { useAtomValue, useStore } from "jotai";
import { GenericTableExternal } from "@components/ui/generic-table/types/generic-table-external";
import useAuth from "@providers/auth-provider";
import { GenericTableFieldSizeEnum } from "@components/ui/generic-table/constants/generic-table-field-size.enum";
import { MarketingAgreementLineItem } from "../models/marketing-agreement";
import { MarketingAgreementLineItemExtensions } from "../types/marketing-agreement";
import { externalAtom } from "@components/ui/generic-table/state";

import renderedCellClasses from "../styles/rendered-cell.module.scss";

type RowOnlyProps = {
  row: GenericTableRowModel<MarketingAgreementLineItem>;
};

const AddOption = (): JSX.Element => (
  <Box component={Stack} direction="row" justifyContent="space-between" width="100%">
    <Typography variant="body2" textTransform="none" color="primary">
      Add manually
    </Typography>
    <AddRounded color="primary" />
  </Box>
);

const ActionsCell = ({ row }: RowOnlyProps): JSX.Element => {
  const { user } = useAuth();
  const store = useStore();

  const { extensions } = useAtomValue(externalAtom, { store }) as GenericTableExternal<
    MarketingAgreementLineItem,
    MarketingAgreementLineItemExtensions
  >;
  const { toggleEdit, submitRow, cancelAmendment } = useGenericTableRowActions<MarketingAgreementLineItem>();
  const { creating, editing, dirty, submitting, disabled, isRowActive } =
    useGenericTableRowState<MarketingAgreementLineItem>(row);

  const permissions = useMemo(() => user?.permissions ?? [], [user?.permissions]);
  const { readonlyParent, cancelLineItem } = useMemo(
    () => (typeof extensions === "function" ? extensions(row) : extensions) || ({ readonlyParent: false, cancelLineItem: NO_OP }),
    [extensions, row],
  );

  const hasPermissionToEdit = permissions.includes(PermissionEnum.MARKETING_AGREEMENTS_UPDATE_LINE_ITEMS);
  const hasPermissionToCancel = permissions.includes(PermissionEnum.MARKETING_AGREEMENTS_CANCEL_LINE_ITEMS);

  const { popperId, ref: popperRef, popout, close, toggle } = usePopperState<HTMLButtonElement>({ id: "more-actions" });

  return (
    <Stack className={renderedCellClasses.Actions} direction="row" gap={1}>
      <Maybe condition={!isRowActive && row?.status === MarketingAgreementStatus.PENDING}>
        <>
          <Button
            ref={(el) => {
              if (popperRef) {
                (popperRef as MutableRefObject<Nullable<HTMLButtonElement>>).current = el;
              }
            }}
            className={`${renderedCellClasses.Button} ${renderedCellClasses.GrayTextColor}`}
            color="inherit"
            variant="outlined"
            disableElevation
            disabled={!readonlyParent || disabled}
            endIcon={<ArrowDropDownRounded className={renderedCellClasses.Icon} fontSize="inherit" />}
            onClick={toggle}
          >
            More Actions
          </Button>
          <Menu
            id={popperId}
            disablePortal
            open={popout}
            anchorEl={popperRef?.current}
            anchorOrigin={{
              vertical: 45,
              horizontal: -30,
            }}
            onClose={close}
          >
            <Tooltip title={!hasPermissionToEdit ? "Insufficient permissions" : ""}>
              <Box>
                <MenuItem
                  disableRipple
                  disabled={!hasPermissionToEdit}
                  onClick={(event: MouseEvent<HTMLLIElement>) => {
                    event?.preventDefault();
                    event?.stopPropagation();

                    close();
                    toggleEdit(row);
                  }}
                >
                  <Button
                    className={`${renderedCellClasses.Button} ${renderedCellClasses.MenuListButton}`}
                    startIcon={
                      <EditRounded
                        className={`${renderedCellClasses.Icon} ${renderedCellClasses.GrayTextColor}`}
                        fontSize="inherit"
                      />
                    }
                    color="inherit"
                  >
                    Edit Line Item
                  </Button>
                </MenuItem>
              </Box>
            </Tooltip>
            <Tooltip title={!hasPermissionToCancel ? "Insufficient permissions" : ""}>
              <Box>
                <MenuItem disableRipple disabled={!hasPermissionToCancel} onClick={close}>
                  <Button
                    className={`${renderedCellClasses.Button} ${renderedCellClasses.MenuListButton}`}
                    color="error"
                    startIcon={<DeleteRounded className={renderedCellClasses.Icon} fontSize="inherit" />}
                    onClick={async (event: MouseEvent<HTMLButtonElement>) => {
                      event?.preventDefault();
                      event?.stopPropagation();

                      close();
                      await cancelLineItem(row);
                    }}
                  >
                    Cancel Line Item
                  </Button>
                </MenuItem>
              </Box>
            </Tooltip>
          </Menu>
        </>
      </Maybe>
      <Maybe condition={isRowActive && (creating || editing)}>
        <IconButton
          color="primary"
          onClick={(event: MouseEvent<HTMLButtonElement>) => {
            event?.preventDefault();
            event?.stopPropagation();
            submitRow(row);
          }}
          type="button"
          disabled={submitting || (editing && !dirty)}
        >
          <CheckRounded />
        </IconButton>
        <Divider sx={{ height: 28.5, m: 0.5 }} orientation="vertical" />
        <IconButton
          color="error"
          onClick={(event: MouseEvent<HTMLButtonElement>) => {
            event?.preventDefault();
            event?.stopPropagation();
            cancelAmendment(row);
          }}
        >
          <CancelRounded />
        </IconButton>
      </Maybe>
    </Stack>
  );
};

export const LINE_ITEM_COLUMNS: GenericTableColumn<MarketingAgreementLineItem>[] = [
  {
    field: "projectVintage.id",
    label: "Vintage",
    type: "autocomplete",
    exportable: true,
    editable: false,
    width: GenericTableFieldSizeEnum.flexmedium,
    fixedWidth: true,
    fixedAutoCompleteOptions: {
      options: [
        {
          label: "_add_vintage_",
          displayLabel: <AddOption />,
          value: "_add_vintage_",
        },
        {
          internal: true,
          label: "_divider_",
          displayLabel: <Divider />,
          value: null,
        },
      ],
      renderOptionOn: (option, defaultOptions) => option.label === "_add_vintage_" || !!defaultOptions?.length,
      renderOptionAt: "before",
    },
    deriveDataValue: (row) => row?.projectVintage?.name ?? '',
  },
  {
    field: "amount",
    label: "Quantity",
    type: "number",
    validator: {
      max: 1000000000,
    },
    width: GenericTableFieldSizeEnum.small,
  },
  {
    field: "amountIssued",
    label: "Qty. Issued",
    type: "number",
    validator: {
      max: 1000000000,
    },
    width: GenericTableFieldSizeEnum.small,
  },
  {
    field: "originalDeliveryDate",
    label: "Orig. Delivery Date",
    type: "date",
    creatable: true,
    editable: false,
    width: GenericTableFieldSizeEnum.flexsmall,
  },
  {
    field: "lastUpdatedDeliveryDate",
    label: "Updated Delivery Date",
    type: "date",
    validator: {
      disablePast: true,
    },
    creatable: false,
    editable: true,
    hide: (rows) => rows?.some((row) => row?.creating),
    width: GenericTableFieldSizeEnum.flexsmall,
  },
  {
    field: "expirationDate",
    label: "Expiration Date",
    type: "date",
    validator: {
      disablePast: true,
    },
    creatable: true,
    editable: true,
    width: GenericTableFieldSizeEnum.flexsmall,
  },
  {
    field: "status",
    label: "Issuance Status",
    creatable: false,
    editable: false,
    hide: (rows) => !!rows?.at(0)?.creating,
    transformDataValue: (value: MarketingAgreementStatus) => (value ? capitalize(value) : ""),
    renderDataCell: ({ status }): JSX.Element => {
      const [color, background] =
        status === MarketingAgreementStatus.CANCELED ? ["#D32F2F", "#D32F2F1A"] : ["#000000DE", "#00000014"];

      return <Chip label={status ? capitalize(status) : ""} sx={{ color, background }} />;
    },
    width: GenericTableFieldSizeEnum.flexsmall,
    fixedWidth: true,
  },
  {
    field: "actions" as any,
    label: "Actions",
    type: "action",
    sortable: false,
    exportable: false,
    creatable: false,
    editable: false,
    hide: (rows) => rows?.every((row) => MarketingAgreementStatus.CANCELED === row?.status),
    width: GenericTableFieldSizeEnum.xxlarge,
    renderDataCell: (row): JSX.Element => <ActionsCell row={row} />,
  },
];
