import {
  DocumentType,
  MarketingAgreementCreateRequest,
  MarketingAgreementFeePercentageStructure,
  MarketingAgreementQuery,
  MarketingAgreementQueryResponse,
  MarketingAgreementResponse,
  MarketingAgreementStatus,
  MarketingAgreementUpdateRequest,
  PermissionEnum,
  AdminProjectVintageQueryResponse,
  ProjectVintageRelations,
  AdminProjectVintageResponse,
  uuid,
} from "@rubiconcarbon/shared-types";
import { useCallback, useContext, useEffect, useMemo, useState } from "react";
import { classValidatorResolver } from "@hookform/resolvers/class-validator";
import { Button, Chip, IconButton, Stack, Typography } from "@mui/material";
import {
  calculator,
  classcat,
  generateQueryParams,
  isNothing,
  NO_OP,
  px,
  toDecimal,
  toNumber,
  Undefinable,
  useTriggerRequest,
} from "@rubiconcarbon/frontend-shared";
import { MISSING_DATA, SERVER_PAGINATION_LIMIT } from "@constants/constants";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { useLogger } from "@providers/logging";
import { useGetSet, useToggle } from "react-use";
import { useFieldArray } from "react-hook-form";
import { DocumentTypeUILabel } from "@constants/documents";
import usePerformantEffect from "@/hooks/use-performant-effect";
import usePerformantState from "@/hooks/use-perfomant-state";
import { AxiosContext } from "@providers/axios-provider";
import DocumentsModal, { FileType } from "@components/documents-upload/documents-modal";
import GenericDialog from "@components/ui/generic-dialog/generic-dialog";
import GenericTable from "@components/ui/generic-table";
import { GenericTableNoDataContent } from "@components/ui/generic-table/components/generic-table-row";
import { DEFAULT_EXPORT_STYLE } from "@components/ui/generic-table/constants/generic-table-styles";
import useGenericTableUtility, { Values } from "@components/ui/generic-table/hooks/use-generic-table-utility";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import MatIcon from "@components/ui/mat-icon/mat-icon";
import { LINE_ITEM_COLUMNS } from "../constants/line-item-columns";
import { MARKETING_AGREEMENT_COLUMNS } from "../constants/marketing-agreement-columns";
import { MarketingAgreementFormModel, MarketingAgreementModel, FeeStructure } from "../models/marketing-agreement";
import { ProjectToVintages } from "../types/expanded-row-types";
import { MarketingAgreementExtensions } from "../types/marketing-agreement";
import LineItems from "./line-tems";
import MarketingFeesModal from "./marketing-fees-modal";

import classes from "../styles/marketing-agreement.module.scss";
import feesModalClasses from "../styles/marketing-fees-modal.module.scss";
import dialogClasses from "../styles/modal.module.scss";
import { NestedColumnExportRecord } from "@/components/ui/generic-table/types/generic-table-export";

const FileTypesOptions: FileType[] = [
  {
    label: DocumentTypeUILabel[DocumentType.CONTRACT],
    value: DocumentType.CONTRACT,
  },
];

const MarketingAgreementFormResolver = classValidatorResolver(MarketingAgreementFormModel);

const MarketingAgreements = ({
  marketingAgreementsResponse: serverMarketingAgreementsResponse,
}: {
  marketingAgreementsResponse: MarketingAgreementQueryResponse;
}): JSX.Element => {
  const { logger } = useLogger();
  const { api } = useContext(AxiosContext);
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();

  const [openMarketingFeesModal, toggleMarketingFeesModal] = useToggle(false);
  const [openDocumentsModal, toggleDocumentsModal] = useToggle(false);
  const [openCancelAgreementModal, toggleCancelAgreementModal] = useToggle(false);
  const [amendingNestedRows, toggleAmendingNestedRows] = useGetSet<boolean>(false);

  const [viewingRow, setViewingRow] = useState<Undefinable<GenericTableRowModel<MarketingAgreementModel>>>();

  const [projectToVintages, setProjectToVintages] = usePerformantState<ProjectToVintages>({});
  const [priors, setPriors] = useState<FeeStructure[]>();
  const [payload, setPayload] = useState<MarketingAgreementCreateRequest | MarketingAgreementUpdateRequest>();
  const { table, form } = useGenericTableUtility<MarketingAgreementModel>({
    form: {
      mode: "onSubmit",
      resolver: MarketingAgreementFormResolver,
      defaultValues: {
        amends: [],
      },
    },
  });
  const { fields, append, remove } = useFieldArray({
    control: form?.control,
    name: "amends.0.feeStructure",
  });

  const { control, formState, handleSubmit, watch, trigger, clearErrors, smartSetValue } = form || { control: NO_OP };
  const { errors } = formState || {};

  const firstValue = useMemo(() => watch?.("amends.0") || {} as MarketingAgreementModel, [watch]);

  const marketingFees = firstValue?.feeStructure || [];

  const creating = useMemo(() => !!(firstValue as { creating?: boolean })?.creating, [firstValue]);
  const editing = useMemo(() => !!(firstValue as { editing?: boolean })?.editing, [firstValue]);
  const readOnly = useMemo(() => !creating && !editing, [creating, editing]);
  const hasEntry = useMemo(() => !!fields?.length, [fields?.length]);

  const size = marketingFees?.length;
  const lastEntry = marketingFees?.at?.(size - 1);
  const canAddMore =
    !lastEntry?.isAbove &&
    !isNothing(lastEntry?.minBracket, ["string"]) &&
    !isNothing(lastEntry?.maxBracket, ["string"]);
  const feeStructureErrors = useMemo(() => errors?.amends?.at?.(0)?.feeStructure || {}, [errors?.amends]);

  const toRowModel = useCallback(
    (row: MarketingAgreementResponse): GenericTableRowModel<MarketingAgreementModel> =>
      ({
        ...row,
        feeStructure: row?.feeStructure?.map((fee) => ({
          ...fee,
          _id: uuid(),
          maxBracket: fee?.maxBracket === MISSING_DATA ? "" : fee?.maxBracket,
          marketingFee: calculator(fee?.marketingFee).multiply(100).calculate()?.toString(),
          isAbove: fee?.maxBracket === MISSING_DATA,
        })),
      }) as unknown as MarketingAgreementModel,
    [],
  );

  const {
    data: agreementsResponse,
    isMutating: loadingAgreements,
    trigger: refreshMarketingAgreements,
  } = useTriggerRequest<MarketingAgreementQueryResponse, object, object, MarketingAgreementQuery>({
    url: "admin/marketing-agreements",
    queryParams: {
      includeTotalCount: true,
      offset: 0,
      limit: SERVER_PAGINATION_LIMIT,
    },
    optimisticData: serverMarketingAgreementsResponse,
    swrOptions: {
      onError: (error: any) => {
        enqueueError("Unable to fetch marketing agreements.");
        logger.error(`Unable to fetch marketing agreements: ${error?.message}`, {});
      },
    },
  });

  const { trigger: commitMA, isMutating: committingMA } = useTriggerRequest<
    MarketingAgreementResponse,
    MarketingAgreementCreateRequest | MarketingAgreementUpdateRequest,
    { id?: uuid },
    object
  >({
    url: `admin/marketing-agreements${creating ? "" : "/{id}"}`,
    method: creating ? "post" : "patch",
    pathParams: {
      id: firstValue?.id as uuid,
    },
    requestBody: payload,
    swrOptions: {
      onError: (error: any) => {
        enqueueError(`Unable to ${creating ? "create" : "update"} marketing agreement.`);
        logger.error(`Unable ${creating ? "create" : "update"} marketing agreement: ${error?.message}`, {});
      },
      onSuccess: async (data) => {
        enqueueSuccess(`Successfully  ${creating ? "created" : "updated"} marketing agreement.`);

        setPriors(undefined);

        await table?.handleKeepRowAfterAmendment({
          ...toRowModel(data),
          creating,
          editing: !creating,
        } as unknown as GenericTableRowModel<MarketingAgreementModel>);

        if (!creating) {
          await refreshMarketingAgreements();
        }
      },
    },
  });

  const { trigger: cancelMA, isMutating: cancelingMA } = useTriggerRequest<
    MarketingAgreementResponse,
    MarketingAgreementUpdateRequest,
    { id: uuid }
  >({
    url: `admin/marketing-agreements/{id}/cancel`,
    method: "patch",
    swrOptions: {
      onError: (error: any) => {
        enqueueError("Unable to cancel marketing agreement");
        logger.error(`Unable to cancel marketing agreemet: ${error?.message}`, {});
      },
      onSuccess: async () => {
        enqueueSuccess("Successfully  canceled marketing agreement.");
        toggleCancelAgreementModal(false);
        await refreshMarketingAgreements();
      },
    },
  });

  const getVintagesByProject = async (id: uuid): Promise<void> => {
    try {
      const { data } = await api.get<AdminProjectVintageQueryResponse>(
        `admin/project-vintages?${generateQueryParams({
          projectIds: [id],
          limit: SERVER_PAGINATION_LIMIT,
          includeRelations: [ProjectVintageRelations.PROJECT],
        })}`,
      );

      setProjectToVintages({
        ...projectToVintages,
        [id]: {
          data: data?.data,
          fetching: false,
          fetched: true,
        },
      });
    } catch (error: any) {
      enqueueError("Unable to fetch project's vintages data.");
      logger.error(`Unable to fetch project's vintages data. Error: ${error?.message}`, {});
    }
  };

  usePerformantEffect(() => {
    if (!hasEntry && !readOnly && openMarketingFeesModal)
      append({
        _id: uuid(),
        order: 0,
        minBracket: "",
        maxBracket: "",
        marketingFee: "",
        isAbove: false,
      });
    else if (readOnly) remove();
  }, [hasEntry, readOnly, openMarketingFeesModal]);

  usePerformantEffect(() => {
    if (size > 0) {
      const lastIndex = size - 1;

      for (let i = 0; i < lastIndex; i++) smartSetValue?.(`amends.0.feeStructure.${i}.isAbove`, false as any);

      smartSetValue?.(`amends.0.feeStructure.${lastIndex}.isAbove`, isNothing(lastEntry?.maxBracket, ["string"]) as any);
    }
  }, [lastEntry?.maxBracket, size]);

  usePerformantEffect(() => {
    if (openMarketingFeesModal && !priors) setPriors(marketingFees as FeeStructure[]);
  }, [openMarketingFeesModal, priors]);

  useEffect(() => {
    const subscription = watch?.(async (_, { type }) => {
      if (type === "change" && !!feeStructureErrors) await trigger?.("amends.0.feeStructure");
    });
    return (): void => subscription?.unsubscribe();
  }, [feeStructureErrors, trigger, watch]);

  const cleanupOnDocumentsDialogClose = (): void => {
    toggleDocumentsModal(false);
  };

  const handleProjectVintageAddition = (projectId: uuid, vintage: AdminProjectVintageResponse): void => {
    setProjectToVintages({
      ...projectToVintages,
      [projectId]: {
        ...(projectToVintages?.[projectId] || {}),
        data: [...(projectToVintages?.[projectId]?.data || []), vintage],
      },
    });
  };

  const handleFeesClose = (type: "submit" | "cancel" | "close" = "close"): void => {
    if (type === "cancel") {
      clearErrors?.("amends.0.feeStructure");
      smartSetValue?.("amends.0.feeStructure", priors);
    } else if (type === "submit") {
      setPriors(marketingFees as FeeStructure[]);
    }

    if (readOnly) setViewingRow(undefined);

    toggleMarketingFeesModal();
  };

  const handleFeeSubmit = async (): Promise<void> => {
    const isValid = await trigger?.("amends.0.feeStructure");
    if (isValid) handleFeesClose("submit");
  };

  const onSubmit = async (formData: Values<MarketingAgreementModel>): Promise<void> => {
    const {
      amends,
    } = formData;

    const firstValue = amends?.[0] as GenericTableRowModel<MarketingAgreementModel>;

    const { creating, project, developer, floorPrice, status, feeStructure = [] } = firstValue || {};

    const toSerializedFeeStructure = ({
      order,
      minBracket,
      maxBracket,
      marketingFee,
      isAbove,
    }: FeeStructure): MarketingAgreementFeePercentageStructure => ({
      order,
      minBracket: `${minBracket}`,
      maxBracket: isAbove ? MISSING_DATA : `${maxBracket}`,
      marketingFee: toDecimal(toNumber(marketingFee) / 100),
    });

    if (creating) {
      const payload: MarketingAgreementCreateRequest = {
        projectId: project?.id as uuid,
        developer,
        floorPrice: toDecimal(floorPrice),
        ...px({ status }, [undefined, null, false]),
        feeStructure: feeStructure.filter((fee): fee is FeeStructure => fee !== undefined).map(toSerializedFeeStructure),
      };

      setPayload(payload);
      setTimeout(async () => await commitMA());
    } else {
      const payload: MarketingAgreementUpdateRequest = {
        developer,
        floorPrice: toDecimal(floorPrice),
        ...px({ status }, [undefined, null, false]),
        feeStructure: feeStructure.map(toSerializedFeeStructure),
      };

      setPayload(payload);
      setTimeout(async () => await commitMA());
    }
  };

  return (
    <>
      <GenericTable
        id="marketing-agreements"
        loading={loadingAgreements}
        reloadingRow={loadingAgreements}
        isExpandable={(row) => !!(readOnly || (creating && row?.creating) || (editing && row?.editing))}
        dismissFormOnSubmit={false}
        eager={{
          expand: true,
        }}
        expandOn={{
          create: true,
        }}
        collapseOn={{
          create: false,
        }}
        columns={MARKETING_AGREEMENT_COLUMNS}
        pageableData={agreementsResponse}
        toRowModel={toRowModel}
        styles={{
          root: {
            maxHeight: "calc(100vh - 190px)",
          },
        }}
        globalSearch={{
          searchKeys: ["uiKey", "project.registryProjectId", "project.name", "developer", "floorPrice", "status"],
        }}
        export={{
          filename: `MarketingAgreements-${new Date()}`,
          nested: {
            lineItems: {
              uniquenessLabel: "Line Item",
              columns: LINE_ITEM_COLUMNS,
            },
          } as NestedColumnExportRecord<MarketingAgreementModel>,
          setClientCanExport: table?.setClientCanExport,
          bindClientExport: table?.bindClientExport,
        }}
        toolbarActionButtons={[
          {
            children: "Export",
            startIcon: <MatIcon value="file_download" variant="round" size={18} color="action" />,
            requiredPermission: PermissionEnum.MARKETING_AGREEMENTS_READ,
            style: DEFAULT_EXPORT_STYLE,
            isDisabled: loadingAgreements || committingMA || cancelingMA || !table?.clientCanExport,
            onClickHandler: table?.handleClientExport,
          },
          {
            children: "Create",
            requiredPermission: PermissionEnum.MARKETING_AGREEMENTS_CREATE,
            isDisabled: loadingAgreements || committingMA || cancelingMA || creating || editing,
            onClickHandler: table?.handleAddRow,
          },
        ]}
        extensions={
          {
            amendingNestedRows,
            toggleMarketingFeesModal,
            toggleDocumentsModal,
            toggleCancelAgreementModal,
            setViewingRow,
          } as MarketingAgreementExtensions
        }
        renderNoDataContent={
          <GenericTableNoDataContent>
            <Stack className={classes.NoData} justifyContent="center" alignItems="center">
              <Chip
                className={classes.Chip}
                icon={<MatIcon value="error_outline" variant="round" size={18} className={classes.InfoIcon} />}
                label={<Typography className={classes.Text}>Please create a marketing agreement to start</Typography>}
              />
            </Stack>
          </GenericTableNoDataContent>
        }
        resetForm={table?.resetForm}
        useForm={table?.useForm}
        bindAddRow={table?.bindAddRow}
        bindKeepRowAfterAmendment={table?.bindKeepRowAfterAmendment}
        renderExpandContent={(row): JSX.Element => {
          const projectId = row?.project?.id;

          const pvs = projectToVintages?.[projectId];

          if (!!projectId && !pvs?.data && !pvs?.fetching) {
            setProjectToVintages({
              ...projectToVintages,
              [projectId]: {
                fetching: true,
                fetched: false,
              },
            });

            setTimeout(async () => await getVintagesByProject(projectId));
          }

          return (
            <Stack className={classes.ExpandedContent} gap={2}>
              <Typography className={classes.Header}>Delivery Schedule</Typography>
              <LineItems
                hasParent={!row?.creating && row?.status !== MarketingAgreementStatus.CANCELED}
                editingParent={!readOnly}
                maId={row?.creating ? undefined : (row?.id as uuid)}
                items={row?.lineItems}
                projectId={projectId}
                projectVintages={pvs ?? { data: [], fetching: false, fetched: false }}
                toggleAmendingNestedRows={toggleAmendingNestedRows as (nextValue?: boolean) => void}
                refreshMarketingAgreements={refreshMarketingAgreements}
                onProjectVintageAddition={handleProjectVintageAddition}
              />
            </Stack>
          );
        }}
        onFormSubmit={handleSubmit?.(onSubmit)}
      />
      <GenericDialog
        open={openMarketingFeesModal}
        dismissIcon={<MatIcon value="close" variant="round" size={24} color="action" />}
        title="Marketing Fees"
        onClose={() => handleFeesClose()}
        customActions={
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Button
              className={classcat([feesModalClasses.ActionButton, feesModalClasses.CancelButton])}
              onClick={() => handleFeesClose("cancel")}
            >
              Cancel
            </Button>
            <Stack direction="row" alignItems="center" width="fit-content !important" gap={1}>
              <IconButton
                disabled={readOnly || !canAddMore}
                onClick={() =>
                  append({
                    order: (lastEntry?.order ?? -1) + 1,
                    minBracket: lastEntry?.maxBracket,
                    maxBracket: "",
                    marketingFee: "",
                  })
                }
              >
                <MatIcon value="add_circle" variant="round" size={40} color={!canAddMore ? "disabled" : "primary"} />
              </IconButton>
              <Button
                className={feesModalClasses.ActionButton}
                variant="contained"
                disabled={readOnly}
                onClick={handleFeeSubmit}
              >
                {editing ? "Update" : "Create"}
              </Button>
            </Stack>
          </Stack>
        }
        classes={{
          root: classcat([feesModalClasses.Dialog, { [feesModalClasses.DialogSmall]: readOnly }]),
          content: feesModalClasses.Content,
          actions: feesModalClasses.Actions,
        }}
      >
        <MarketingFeesModal
          fees={(readOnly ? viewingRow?.feeStructure : marketingFees) as FeeStructure[]}
          control={control as any}
          fields={fields}
          feeStructureErrors={feeStructureErrors as any}
          readOnly={readOnly}
          editing={editing}
          remove={remove}
        />
      </GenericDialog>
      <GenericDialog
        open={openCancelAgreementModal}
        title="Cancel Agreement"
        onClose={() => toggleCancelAgreementModal(false)}
        positiveAction={{
          buttonText: "YES, CANCEL",
          onClick: () => cancelMA({ pathParams: { id: viewingRow?.id as uuid } }),
          className: dialogClasses.DestructivePositiveAction,
        }}
        negativeAction={{
          buttonText: "NO",
          onClick: () => toggleCancelAgreementModal(false),
        }}
        classes={{
          root: dialogClasses.Dialog,
          title: dialogClasses.DestructiveTitle,
          content: dialogClasses.Content,
          actions: dialogClasses.Actions,
        }}
      >
        <Typography>Are you sure you want to cancel the agreement?</Typography>
      </GenericDialog>
      <DocumentsModal
        open={openDocumentsModal}
        relatedKey={viewingRow?.uiKey || ''}
        saveTypeOptions={FileTypesOptions}
        canUpload={viewingRow?.status !== MarketingAgreementStatus.CANCELED}
        onPositiveClick={(callback) => callback()}
        onNegativeClick={(callback) => {
          callback();
          cleanupOnDocumentsDialogClose();

          if (readOnly) setViewingRow(undefined);
        }}
        onClose={async (callback) => {
          callback();
          cleanupOnDocumentsDialogClose();

          if (readOnly) setViewingRow(undefined);
        }}
      />
    </>
  );
};

export default MarketingAgreements;
