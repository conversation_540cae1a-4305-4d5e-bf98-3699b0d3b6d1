import AddNewVintageForm from "@components/project-vintages/components/add-new-vintage-form";
import GenericDialog from "@components/ui/generic-dialog/generic-dialog";
import GenericTable from "@components/ui/generic-table";
import useGenericTableUtility from "@components/ui/generic-table/hooks/use-generic-table-utility";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import useAuth from "@providers/auth-provider";
import useAutoCompleteOptions from "@hooks/use-auto-complete-options";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { Stack, Typography, Tooltip, Chip, Fab, Zoom, Box } from "@mui/material";
import { MaybeNothing, isNothing, pickFromRecord, toNumber, Undefinable, Maybe } from "@rubiconcarbon/frontend-shared";
import {
  uuid,
  AdminProjectVintageResponse,
  PermissionEnum,
  MarketingAreementLineItemCreateRequest,
  MarketingAgreementLineItemUpdateRequest,
  MarketingAgreementQueryResponse,
  MarketingAgreementLineItemResponse,
} from "@rubiconcarbon/shared-types";
import { AxiosResponse } from "axios";
import { useContext, useMemo, useState, useCallback, useEffect } from "react";
import { useToggle } from "react-use";
import { LINE_ITEM_COLUMNS } from "../constants/line-item-columns";
import { useLogger } from "@providers/logging";
import { ProjectVintageEntry } from "../types/expanded-row-types";
import { classValidatorResolver } from "@hookform/resolvers/class-validator";
import { GenericTableNoDataContent } from "@components/ui/generic-table/components/generic-table-row";
import { MarketingAgreementLineItem, MultiMarketingAgreementLineItemFormModel } from "../models/marketing-agreement";
import { MarketingAgreementLineItemExtensions } from "../types/marketing-agreement";
import MatIcon from "@components/ui/mat-icon/mat-icon";
import usePerformantEffect from "@/hooks/use-performant-effect";
import { AxiosContext } from "@/providers/axios-provider";

import dialogClasses from "../styles/line-item-modal.module.scss";
import classes from "../styles/line-items.module.scss";
import parentClasses from "../styles/marketing-agreement.module.scss";
import { SERVER_PAGINATION_LIMIT } from "@/constants/constants";

type LineItemsProps = {
  hasParent: boolean;
  editingParent: boolean;
  maId?: uuid;
  items: MarketingAgreementLineItem[];
  projectId: uuid;
  projectVintages: ProjectVintageEntry;

  toggleAmendingNestedRows: (nextValue?: boolean) => void;
  refreshMarketingAgreements: () => Promise<MarketingAgreementQueryResponse>;
  onProjectVintageAddition: (projectId: uuid, vintage: AdminProjectVintageResponse) => void;
};

const MultiMALineItemFormResolver = classValidatorResolver(MultiMarketingAgreementLineItemFormModel);

const LineItems = ({
  hasParent,
  editingParent,
  maId,
  items,
  projectId,
  projectVintages,
  toggleAmendingNestedRows,
  refreshMarketingAgreements,
  onProjectVintageAddition,
}: LineItemsProps): JSX.Element => {
  const { user } = useAuth();
  const { logger } = useLogger();
  const { api } = useContext(AxiosContext);
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const permissions = useMemo(() => user?.permissions || [], [user?.permissions]);

  const hasPermissionToCreate = permissions.includes(PermissionEnum.FORWARDS_CREATE_LINE_ITEMS);

  const [openAddVintageModal, toggleAddVintageModal] = useToggle(false);
  const [openCancelLineItemModal, toggleCancelLineItemModal] = useToggle(false);
  const [canSubmitNewVintage, toggleCanSubmitNewVintage] = useToggle(false);
  const [submittingDialogContent, toggleSubmittingDialogContent] = useToggle(false);

  const [lastAmendedIndex, setAmendedIndex] = useState<MaybeNothing<number>>();
  const [viewingLineItem, setViewingLineItem] =
    useState<Undefinable<GenericTableRowModel<MarketingAgreementLineItem>>>();

  const { table, form, currentFormValue } = useGenericTableUtility<MarketingAgreementLineItem>({
    form: {
      mode: "onSubmit",
      resolver: MultiMALineItemFormResolver,
      defaultValues: {
        amends: [],
      },
    },
  });

  const { getValues, smartSetValue, watch, trigger, handleSubmit } = form || {};

  const inCreateMode = useMemo(
    () => currentFormValue?.amends?.some((amend) => amend?.creating === true),
    [currentFormValue?.amends],
  );

  const inEditMode = useMemo(
    () => currentFormValue?.amends?.some((amend) => amend?.editing === true),
    [currentFormValue?.amends],
  );

  const vintageOptions = useAutoCompleteOptions({
    data: projectVintages?.data || [],
    keys: ["id", "name"],
    label: (entry) => entry?.name,
    value: (entry) => entry?.id,
    postTransform: (options) => options.sort((a, b) => a.label.localeCompare(b.label)),
  });

  const columns = useMemo(
    () =>
      LINE_ITEM_COLUMNS.map((column) => {
        if (column.field === "projectVintage.id") {
          column.valueOptions = vintageOptions;
          column.autoCompleteLoading = projectVintages?.fetching;
        }

        return column;
      }),
    [projectVintages?.fetching, vintageOptions],
  );

  usePerformantEffect(() => {
    toggleAmendingNestedRows(inCreateMode || inEditMode);
  }, [inCreateMode, inEditMode]);

  useEffect(() => {
    const subscription = watch?.(({ amends = [] }, { name, type }) => {
      if (type === "change") {
        const [, index, ...path] = ((name as any) ?? "").split(".");
        const joinedPath = path.join(".") as any;

        if (!isNothing(index)) {
          const numIndex = Number(index);

          setAmendedIndex(numIndex);

          const formRow = amends?.at(numIndex);

          switch (path?.at(0)) {
            case "projectVintage": {
              const id = formRow && joinedPath ? (pickFromRecord(formRow, [joinedPath], true) as Record<string, uuid>)?.[joinedPath] : undefined;

              if (id === "_add_vintage_") {
                toggleAddVintageModal?.(true);
                smartSetValue?.(name as any, undefined);
              }
              break;
            }
            default:
              break;
          }
        }
      }
    });
    return (): void => subscription?.unsubscribe();
  }, [smartSetValue, toggleAddVintageModal, watch]);

  const toRowModel = useCallback(
    (row: MarketingAgreementLineItem): GenericTableRowModel<MarketingAgreementLineItem> => {
      return {
        ...row,
      };
    },
    [],
  );

  const createLineItem = async (row: GenericTableRowModel<MarketingAgreementLineItem>): Promise<void> => {
    try {
      const payload: MarketingAreementLineItemCreateRequest = {
        amount: toNumber(row?.amount),
        amountIssued: !isNothing(row?.amountIssued) ? toNumber(row?.amountIssued) : undefined,
        deliveryDate: new Date(row.originalDeliveryDate),
        expirationDate: !isNothing(row?.expirationDate) ? new Date(row.expirationDate) : undefined,
        projectVintageId: row?.projectVintage?.id,
      };

      await api.post<MarketingAreementLineItemCreateRequest, AxiosResponse<MarketingAgreementLineItemResponse>>(
        `admin/marketing-agreements/${maId}/line-items`,
        payload,
      );

      enqueueSuccess("Successfully created line item");

      table?.handleCancelRowAmendment(row);

      await refreshMarketingAgreements();
    } catch (error: any) {
      enqueueError(`Unable to save line item: ${error?.message}.`);
      logger.error(`Unable to save line item: ${error?.message}.`, {});
    }
  };

  const updateLineItem = async (row: GenericTableRowModel<MarketingAgreementLineItem>): Promise<void> => {
    try {
      const payload: MarketingAgreementLineItemUpdateRequest = {
        amount: toNumber(row?.amount),
        amountIssued: !isNothing(row?.amountIssued) ? toNumber(row?.amountIssued) : undefined,
        deliveryDate: new Date(row.lastUpdatedDeliveryDate),
        expirationDate: !isNothing(row?.expirationDate) ? new Date(row.expirationDate) : undefined,
        projectVintageId: row?.projectVintage?.id,
      };

      await api.patch<MarketingAgreementLineItemUpdateRequest, AxiosResponse<MarketingAgreementLineItemResponse>>(
        `admin/marketing-agreements/${maId}/line-items/${row?.id}`,
        payload,
      );

      enqueueSuccess("Successfully updated line item");

      table?.handleCancelRowAmendment(row);

      await refreshMarketingAgreements();
    } catch (error: any) {
      enqueueError(`Unable to update line item: ${error?.message}.`);
      logger.error(`Unable to update line item: ${error?.message}.`, {});
    }
  };

  const updateLineItemStatus = async (
    row: GenericTableRowModel<MarketingAgreementLineItem>,
    status: "settle" | "cancel",
  ): Promise<void> => {
    setViewingLineItem(row);
    if (status === "cancel") toggleCancelLineItemModal(true);
  };

  const cancelLineItem = async (row: MarketingAgreementLineItem): Promise<void> => {
    try {
      await api.patch<MarketingAgreementLineItemUpdateRequest, AxiosResponse<MarketingAgreementLineItemResponse>>(
        `admin/marketing-agreements/${maId}/line-items/${row?.id}/cancel`,
      );

      enqueueSuccess("Successfully canceled line item");

      await refreshMarketingAgreements();
      toggleCancelLineItemModal(false);
    } catch (error: any) {
      enqueueError(`Unable to cancel line item.`);
      logger.error(`Unable to cancel line item: ${error?.message}.`, {});
    }
  };

  const commitLineItem = async (row: GenericTableRowModel<MarketingAgreementLineItem>): Promise<void> => {
    const index = getValues?.()?.amends?.findIndex(({ id }) => id === row?.id);

    const canSubmit = index !== undefined ? await trigger?.(`amends.${index}`) : false;

    if (canSubmit) {
      if (row?.creating) await createLineItem(row);
      else await updateLineItem(row);
    }
  };

  const onSubmit = async (): Promise<void> => {};

  return (
    <Stack className={classes.LineItems} alignItems="flex-end" gap={1}>
      <GenericTable
        id="line-items"
        columns={columns}
        pageableData={{
          data: items,
          page: {
            offset: 0,
            limit: SERVER_PAGINATION_LIMIT,
            size: items?.length,
            totalCount: items?.length,
          },
        }}
        styles={{
          headerRow: {
            backgroundColor: "#EEEEEE",
            zIndex: 500,
          },
        }}
        toRowModel={toRowModel}
        isMultiEditable
        appendNewRows
        resetForm={table?.resetForm}
        useForm={table?.useForm}
        bindAddRow={table?.bindAddRow}
        bindCancelRowAmendment={table?.bindCancelRowAmendment}
        extensions={
          {
            readonlyParent: !editingParent,
            cancelLineItem: (row: GenericTableRowModel<MarketingAgreementLineItem>) =>
              updateLineItemStatus(row, "cancel"),
          } as MarketingAgreementLineItemExtensions
        }
        onFormSubmit={handleSubmit?.(onSubmit)}
        onRowSubmit={commitLineItem}
        renderNoDataContent={
          <GenericTableNoDataContent>
            <Stack className={parentClasses.NoData} justifyContent="center" alignItems="center">
              <Maybe condition={!hasParent}>
                <Chip
                  className={parentClasses.Chip}
                  icon={<MatIcon value="error_outline" variant="round" size={18} className={parentClasses.InfoIcon} />}
                  label={
                    <Typography className={parentClasses.Text}>Please create a marketing agreement first</Typography>
                  }
                />
              </Maybe>
              <Maybe condition={hasParent && !items?.length}>
                <Box>
                  <Typography>
                    Please click{" "}
                    <Typography component="span" variant="h6" fontWeight="bold">
                      +
                    </Typography>{" "}
                    icon to add line items
                  </Typography>
                </Box>
              </Maybe>
            </Stack>
          </GenericTableNoDataContent>
        }
      />
      <Zoom in={hasParent} unmountOnExit>
        <Tooltip title={!hasPermissionToCreate ? "Insufficient permissions" : ""}>
          <Fab
            className={classes.AddLineItem}
            size="medium"
            onClick={table?.handleAddRow}
            disabled={!hasPermissionToCreate || !hasParent || editingParent || inEditMode}
            sx={{ zIndex: 500 }}
          >
            <MatIcon
              value="add_circle"
              variant="round"
              size={48}
              color={hasPermissionToCreate && hasParent && !editingParent && !inEditMode ? "primary" : "disabled"}
            />
          </Fab>
        </Tooltip>
      </Zoom>
      <GenericDialog
        open={openAddVintageModal}
        title="Add New Vintage"
        onClose={() => toggleAddVintageModal(false)}
        positiveAction={{
          buttonText: "CREATE",
          disabled: !canSubmitNewVintage,
          onClick: () => toggleSubmittingDialogContent(true),
        }}
        negativeAction
        onNegativeClick={() => toggleAddVintageModal(false)}
        classes={{
          root: dialogClasses.AddOption,
          content: dialogClasses.AddOptionContent,
          actions: dialogClasses.Actions,
        }}
      >
        <AddNewVintageForm
          projectId={projectId}
          submitting={submittingDialogContent}
          setCanSumbit={toggleCanSubmitNewVintage}
          setSubmitting={toggleSubmittingDialogContent}
          onSubmitSuccess={(projectVintage) => {
            setTimeout(() => {
              toggleAddVintageModal(false);

              if (projectVintage?.project?.id) {
                onProjectVintageAddition(projectVintage.project.id, projectVintage);
              }

              smartSetValue?.(`amends.${lastAmendedIndex}.projectVintage.id` as any, projectVintage?.id, {
                shouldValidate: !!projectId,
              });
            });
          }}
        />
      </GenericDialog>
      <GenericDialog
        open={openCancelLineItemModal}
        title="Cancel Line Item"
        onClose={() => toggleCancelLineItemModal(false)}
        positiveAction={{
          buttonText: "YES, CANCEL",
          onClick: () => viewingLineItem && cancelLineItem(viewingLineItem as MarketingAgreementLineItem),
          className: dialogClasses.DestructivePositiveAction,
        }}
        negativeAction={{
          buttonText: "NO",
          onClick: () => toggleCancelLineItemModal(false),
        }}
        classes={{
          root: dialogClasses.Dialog,
          title: dialogClasses.DestructiveTitle,
          content: dialogClasses.Content,
          actions: dialogClasses.Actions,
        }}
      >
        <Typography>Are you sure you want to cancel the line item?</Typography>
      </GenericDialog>
    </Stack>
  );
};

export default LineItems;
