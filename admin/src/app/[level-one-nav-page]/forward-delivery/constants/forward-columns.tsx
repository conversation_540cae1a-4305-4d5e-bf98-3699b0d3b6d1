import { GenericTableColumn } from "@components/ui/generic-table/types/generic-table-column";
import { Box, capitalize, Chip, Grid, IconButton, Stack, Tooltip, Typography } from "@mui/material";
import { ForwardStatus, ForwardType, PermissionEnum } from "@rubiconcarbon/shared-types";
import { ForwardModel } from "../models/forward";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import { MouseEvent, useMemo } from "react";
import useAuth from "@providers/auth-provider";
import useGenericTableRowActions from "@components/ui/generic-table/hooks/use-generic-table-row-actions";
import useGenericTableRowState from "@components/ui/generic-table/hooks/use-generic-table-row-state";
import { Maybe, numberFormat, toNumber } from "@rubiconcarbon/frontend-shared";
import { InfoRounded, ModeEditRounded } from "@mui/icons-material";
import SuspendedChip from "@components/ui/suspended-chip/suspended-chip";
import CancelIcon from "@mui/icons-material/Cancel";
import CheckIcon from "@mui/icons-material/Check";
import COLORS from "@components/ui/theme/colors";
import { GenericTableFieldSizeEnum } from "@components/ui/generic-table/constants/generic-table-field-size.enum";
import GenericTableDocumentsButton from "@components/ui/generic-table/components/generic-row-document-button";
import Link from "next/link";
import CustomerPortfolio from "@components/ui/customer-portfolio/customer-portfolio";

import actionClasses from "../styles/actions-cell.module.scss";

type RowOnlyProps = {
  row: GenericTableRowModel<ForwardModel>;
};

const ActionsCell = ({ row }: RowOnlyProps): JSX.Element => {
  const { user } = useAuth();
  const { toggleEdit, cancelAmendment } = useGenericTableRowActions<ForwardModel>();
  const { creating, editing, dirty, submitting, disabled, isRowActive } = useGenericTableRowState<ForwardModel>(row);

  const permissions = useMemo(() => user?.permissions ?? [], [user?.permissions]);

  const hasPermissionToEdit = permissions.includes(PermissionEnum.FORWARDS_UPDATE);

  return (
    <Stack className={actionClasses.Actions} direction="row" gap={1}>
      <Maybe condition={!isRowActive}>
        <Tooltip
          title={
            !hasPermissionToEdit
              ? "Insufficient permissions"
              : row?.type === ForwardType.SELL
                ? "Edit FWD Sell is not allowed"
                : ""
          }
        >
          <Box>
            <IconButton
              color="primary"
              disabled={
                !hasPermissionToEdit ||
                disabled ||
                row?.status !== ForwardStatus.PENDING ||
                row?.type === ForwardType.SELL
              }
              onClick={(event: MouseEvent<HTMLButtonElement>) => {
                event?.preventDefault();
                event?.stopPropagation();
                toggleEdit(row);
              }}
            >
              <ModeEditRounded />
            </IconButton>
          </Box>
        </Tooltip>
      </Maybe>
      <Maybe condition={isRowActive}>
        <Tooltip title={`${creating ? "Create" : "Update"} Forward Order`}>
          <span>
            <IconButton
              sx={{ color: COLORS.rubiconGreen }}
              edge="start"
              onClick={(event: MouseEvent<HTMLButtonElement>) => event.stopPropagation()}
              type="submit"
              disabled={submitting || (editing && !dirty)}
            >
              <CheckIcon />
            </IconButton>
          </span>
        </Tooltip>
        <Tooltip title={"Cancel"}>
          <span>
            <IconButton
              sx={{ color: COLORS.red, paddingLeft: "10px" }}
              edge="start"
              onClick={(event: MouseEvent<HTMLButtonElement>) => {
                event?.preventDefault();
                event?.stopPropagation();

                if (creating) cancelAmendment();
                else toggleEdit(row);
              }}
            >
              <CancelIcon />
            </IconButton>
          </span>
        </Tooltip>
      </Maybe>
    </Stack>
  );
};

export const FORWARD_COLUMNS: GenericTableColumn<ForwardModel>[] = [
  {
    field: "uiKey",
    label: "Transaction Key",
    creatable: false,
    editable: false,
    hide: (rows) => !!rows?.at(0)?.creating,
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
  },
  {
    field: "project.id",
    label: "Project",
    type: "async-autocomplete",
    editable: false,
    placeholder: "Type the project ID or name",
    asyncAutocompleteOptions: {
      keys: ["id", "name", "registryProjectId"],
      request: {
        url: "admin/projects/search",
        queryParams: {
          name: true,
          id: true,
          fuzzy: true,
          limit: 50,
        },
      },
      label: (entry) => `${entry?.registryProjectId} - ${entry?.name}`,
      value: (entry) => entry?.id,
    },
    width: GenericTableFieldSizeEnum.large,
    maxWidth: GenericTableFieldSizeEnum.xxxlarge,
    deriveDataValue: (row) => `${row?.project?.registryProjectId} - ${row?.project?.name}`,
    renderDataCell: (row: ForwardModel) => (
      <Stack direction="row" justifyContent="space-between" alignItems="center">
        <Stack gap={1}>
          <Link
            href={`/projects/${row?.project?.id}`}
            style={{ color: COLORS.rubiconGreen, textUnderlineOffset: 3 }}
            onClick={(event) => event.stopPropagation()}
          >
            <Typography variant="body2" fontWeight={300}>
              {row?.project?.registryProjectId} - {row?.project?.name}
            </Typography>
          </Link>

          <Maybe condition={!!row?.project?.suspended}>
            <Grid container gap={1} alignItems="center">
              <SuspendedChip />
            </Grid>
          </Maybe>
        </Stack>
      </Stack>
    ),
  },
  {
    field: "type",
    label: "Type",
    type: "select",
    valueOptions: [
      {
        label: "Buy",
        value: ForwardType.BUY,
      },
      {
        label: "Sell",
        value: ForwardType.SELL,
      },
    ],
    width: GenericTableFieldSizeEnum.xsmall,
    fixedWidth: true,
    creatable: true,
    editable: false,
  },
  {
    field: "counterparty",
    label: "Counterparty",
    width: GenericTableFieldSizeEnum.medium,
    maxWidth: GenericTableFieldSizeEnum.xlarge,
    renderDataCell: (row): JSX.Element => {
      return (
        <>
          <Maybe condition={'customerPortfolio' in row}>
            <CustomerPortfolio portfolio={row?.customerPortfolio} style={{ fontSize: 14, fontWeight: 300 }} />
          </Maybe>
          <Maybe condition={!('customerPortfolio' in row)}>
            <Typography fontSize={14} fontWeight={300}>
              {row?.counterparty}
            </Typography>
          </Maybe>
        </>
      );
    },
  },
  {
    field: "amount",
    label: "Quantity",
    type: "number",
    creatable: false,
    editable: false,
    hide: (rows) => !!rows?.at(0)?.creating,
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
    renderDataCell: (row): JSX.Element => {
      const sum =
        row?.status === ForwardStatus.CANCELED
          ? numberFormat(row?.lineItems?.reduce((sum, { expectedAmount }) => sum + toNumber(expectedAmount), 0))
          : numberFormat(row?.amount);

      return (
        <Stack direction="row" alignItems="center" gap={0.5}>
          <Typography textAlign="left" fontSize={14} fontWeight={300}>
            {sum}
          </Typography>
          <Maybe condition={row?.status === ForwardStatus.CANCELED}>
            <Tooltip title={row?.status === ForwardStatus.CANCELED ? "Total Expected Quantity" : null}>
              <InfoRounded sx={{ fontSize: 18, color: "gray" }} />
            </Tooltip>
          </Maybe>
        </Stack>
      );
    },
  },

  {
    field: "documents",
    label: "Documents",
    creatable: false,
    editable: false,
    fixedWidth: true,
    exportable: false,
    hide: (rows) => !!rows?.at(0)?.creating,
    width: GenericTableFieldSizeEnum.small,
    renderDataCell: (row): JSX.Element => <GenericTableDocumentsButton row={{ ...row, alwaysEnabled: true }} />,
  },

  {
    field: "status",
    label: "Status",
    type: "select",
    creatable: false,
    editable: false,
    hide: (rows) => !!rows?.at(0)?.creating,
    transformDataValue: (value: ForwardStatus) => (value ? capitalize(value) : ""),
    renderDataCell: ({ status }): JSX.Element => {
      const [color, background] =
        status === ForwardStatus.SETTLED
          ? ["#077D55", "#EDF7ED"]
          : status === ForwardStatus.CANCELED
            ? ["#D32F2F", "#D32F2F1A"]
            : ["#000000DE", "#00000014"];

      return <Chip label={status ? capitalize(status) : ""} sx={{ color, background }} />;
    },
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
  },
  {
    field: "lineItems",
    hide: true,
    exportable: true,
  },
  {
    field: "actions" as any,
    label: "Actions",
    type: "action",
    sortable: false,
    exportable: false,
    creatable: false,
    editable: false,
    width: GenericTableFieldSizeEnum.small,
    maxWidth: GenericTableFieldSizeEnum.xxxlarge,
    hide: (rows) => !rows?.at(0)?.creating,
    renderDataCell: (row): JSX.Element => <ActionsCell row={row} />,
  },
];
