import { Controller, useFieldArray } from "react-hook-form";
import { classValidatorResolver } from "@hookform/resolvers/class-validator";
import {
  Alert,
  Autocomplete,
  AutocompleteInputChangeReason,
  Box,
  Button,
  capitalize,
  Container,
  Divider,
  FilterOptionsState,
  MenuItem,
  Stack,
  TextField,
  <PERSON>lt<PERSON>,
  Typography,
} from "@mui/material";
import {
  BookType,
  PermissionEnum,
  ProjectSearchResponse,
  AdminProjectVintageQueryResponse,
  ProjectVintageRelations,
  AdminProjectVintageResponse,
  TradeCreateRequest,
  TradeResponse,
  TradeUpdateRequest,
  uuid,
  CounterpartyQueryResponse,
  TradeCounterpartyRequest,
  GroupingParentQueryResponse,
  AdminBookResponse,
} from "@rubiconcarbon/shared-types";
import useAutoCompleteOptions, { UseAutoCompleteOptionsReturnEntry } from "@hooks/use-auto-complete-options";
import useAutoCompleteOptionsAsync from "@hooks/use-auto-complete-options-async";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { useLogger } from "@providers/logging";
import {
  KeyboardEvent,
  PropsWithChildren,
  ReactNode,
  SyntheticEvent,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { useToggle, useUnmount } from "react-use";
import usePerformantEffect from "@/hooks/use-performant-effect";
import usePerformantState from "@/hooks/use-perfomant-state";
import { AxiosContext } from "@providers/axios-provider";
import {
  calculator,
  classcat,
  currencyFormat,
  deepClone,
  deepEqual,
  deepMerge,
  generateQueryParams,
  isNothing,
  Maybe,
  NO_OP,
  numberFormat,
  pickFromRecord,
  px,
  toBoolean,
  toDecimal,
  toNumber,
  useTriggerRequest,
} from "@rubiconcarbon/frontend-shared";
import { COMPLIANCE_PERMIT, MISSING_DATA } from "@constants/constants";
import {
  combineByParentBookType,
  groupParentBookTypesByRegistryVintage,
} from "@/utils/helpers/portfolio/grouping-parent-book-transformations";
import { NumericFormat } from "react-number-format";
import MatIcon from "@components/ui/mat-icon/mat-icon";
import useNavigation from "@/hooks/use-navigation";
import GenericDialog from "@components/ui/generic-dialog/generic-dialog";
import AddNewVintageForm from "@components/project-vintages/components/add-new-vintage-form";
import { bookIsBreached } from "@utils/helpers/portfolio/book-is-breached";
import BookBreachedModal from "./book-breached-modal";
import { LoadingButton } from "@mui/lab";
import COLORS from "@components/ui/theme/colors";
import Link from "next/link";
import { TransactionStatus } from "@constants/transaction-status";
import { ProjectCell } from "../../constants/trades-columns";
import { AllTransactionType, TradeCounterparty, TransactionModel } from "@models/transaction";
import { TIFEnum } from "@constants/tif.enum";
import { toTradeModel } from "@utils/helpers/transaction/to-transaction-models";
import { AuthContext } from "@providers/auth-provider";
import { useStoreProvider } from "@providers/store-provider";
import { getNewTradeModel } from "@utils/helpers/transaction/get-new-transaction-models";
import RCTEligibilityChip from "@components/ui/rct-eligibility-chip/rct-eligibility-chip";
import SuspendedChip from "@components/ui/suspended-chip/suspended-chip";
import Counterparties from "./counterparties";
import useNavigationInterceptor from "@hooks/use-navigation-interceptor";
import { useEnhancedForm } from "@/hooks/use-enhanced-form";
import { PrimitiveTypeVintage } from "@/models/primitive-type-vintage";

import classes from "../../styles/trade-form.module.scss";
import dialogClasses from "../../styles/dialog.module.scss";

const parserBlacklist = ["$", ","];

const extractProjectLabel = (project: Partial<ProjectSearchResponse> = {} as any): string =>
  `${project?.registryProjectId} - ${project?.name}`;

const ProjectVintageLabel = ({ vintage }: { vintage: Partial<AdminProjectVintageResponse> }): JSX.Element => {
  return (
    <Stack direction="row" gap={1} width="100%" justifyContent="space-between" alignItems="center">
      <Typography>{vintage?.name}</Typography>
      <Stack direction="row" gap={1}>
        <Maybe condition={!!vintage?.project?.suspended}>
          <SuspendedChip />
        </Maybe>
        <RCTEligibilityChip iconOnly={false} vintage={vintage as any} />
      </Stack>
    </Stack>
  );
};

const NRTFormResolver = classValidatorResolver(TransactionModel);

const AddOption = (): JSX.Element => (
  <Box component={Stack} direction="row" justifyContent="space-between" width="100%" height="100%">
    <Typography variant="body2" textTransform="none" color="primary">
      Add manually
    </Typography>
    <MatIcon value="add" variant="round" size={25} />
  </Box>
);

type ReadOnlyFieldProps = {
  label?: ReactNode;
  endAdornment?: ReactNode;
};

const ReadOnlyField = ({ label, endAdornment, children }: PropsWithChildren<ReadOnlyFieldProps>): JSX.Element => (
  <Stack position="relative" width="100%" border="solid 1px lightgray" padding={1.9} borderRadius={1}>
    <Maybe condition={!!label}>
      <Box position="absolute" top={-8} fontSize={12} padding="0 2px" bgcolor="white" color={COLORS.rubiconGreen}>
        {label}
      </Box>
    </Maybe>
    <Stack direction="row" justifyContent="space-between">
      {children}
      <Maybe condition={!!endAdornment}>{endAdornment}</Maybe>
    </Stack>
  </Stack>
);

const TradeForm = ({
  tradeResponse,
  counterpartiesResponse,
  booksByParentResponse,
}: {
  tradeResponse?: TradeResponse;
  counterpartiesResponse?: CounterpartyQueryResponse;
  booksByParentResponse: GroupingParentQueryResponse;
}): JSX.Element => {
  const { logger } = useLogger();
  const { popFromPath } = useNavigation();
  const { api } = useContext(AxiosContext);
  const { user } = useContext(AuthContext);
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const { updateEphemeralState } = useStoreProvider();

  const [canInterrupt, setCanInterrupt] = useToggle(true);
  const [openProjectDropdown, toggleProjectDropdown] = useToggle(false);
  const [openAddVintage, toggleAddVintageModal] = useToggle(false);
  const [canSumbitNewVintage, toggleCanSumbitNewVintage] = useToggle(false);
  const [submittingDialogContent, toggleSubmittingDialogContent] = useToggle(false);
  const [openLimitBreached, toggleOpenLimitBreached] = useToggle(false);
  const [projectSearchTerm, setProjectSearchTerm] = useState<string>("");
  const [vintageMap, updateVintageMap] = usePerformantState<
    Record<
      uuid,
      {
        data: AdminProjectVintageResponse[];
        fetching: boolean;
        fetched: boolean;
        updated: boolean;
      }
    >
  >({});
  const [tradeDataRequestPayload, setTradeDataRequestPayload] = usePerformantState<
    TradeCreateRequest | TradeUpdateRequest
  >();

  const creating = !tradeResponse;

  useUnmount(() => {
    updateEphemeralState("transactions.viewing", "trades");
  });

  const model = creating ? getNewTradeModel() : toTradeModel(tradeResponse);

  const { control, reset, resetField, watch, smartSetValue, handleSubmit, formState } = useEnhancedForm<TransactionModel>({
    mode: "onSubmit",
    defaultValues: model,
    resolver: NRTFormResolver,
  });

  const {
    fields: lineItems,
    prepend,
    append,
    remove,
  } = useFieldArray({
    control,
    name: "trade.counterparties",
  });

  const { errors } = formState;

  const formData = watch();
  const asset = formData?.assets?.at(0);
  const selectedBookId = asset?.source?.id;
  const selectedProjectId = asset?.projectVintage?.project?.id;
  const selectedProjectVintageId = asset?.projectVintage?.id;
  const selectedType = formData?.type;
  const selectedProjectType = asset?.projectVintage?.project?.projectType;
  const rawPrice = asset?.rawPrice;
  const feeTotal = asset?.feeTotal;

  const { trigger: commitTrade, isMutating: committingTrade } = useTriggerRequest({
    url: `admin/trades${!creating ? "/{id}" : ""}`,
    method: creating ? "post" : "patch",
    pathParams: {
      id: tradeResponse?.id,
    },
    requestBody: tradeDataRequestPayload,
    swrOptions: {
      onError: (error: any) => {
        enqueueError("Unable to update trade status.");
        logger.error(`Unable to update trade status: ${error?.message}`, {});
      },
      onSuccess: async () => {
        popFromPath(creating ? 1 : 2);
        enqueueSuccess(`Successfully ${creating ? "created" : "updated"} trade${!creating ? " data." : "."}`);
      },
    },
  });

  const parentBookRelatedInfoByType = combineByParentBookType(booksByParentResponse?.data, [
    "parent_book",
    "registry_vintage_allocations",
    "owner_registry_vintage_nested_allocations:available",
  ]);

  const canEditPrices = useMemo(
    () => !creating && !model?.isPaid && user?.hasPermission(PermissionEnum.TRADES_UPDATE_AMOUNTS),
    [creating, model?.isPaid, user],
  );

  const vintageToBookTypes = useMemo(
    () => groupParentBookTypesByRegistryVintage(booksByParentResponse?.data),
    [booksByParentResponse?.data],
  );

  const buyOnly = useMemo(
    () => !!selectedProjectVintageId && !vintageToBookTypes?.[selectedProjectVintageId]?.length,
    [selectedProjectVintageId, vintageToBookTypes],
  );

  const complianceOnly = useMemo(
    () => selectedType === AllTransactionType.BUY && selectedProjectType?.category === COMPLIANCE_PERMIT,
    [selectedProjectType?.category, selectedType],
  );

  const filteredOptionBooks = useMemo(() => {
    if (selectedType !== AllTransactionType.SELL) {
      const compliance = deepClone(parentBookRelatedInfoByType[BookType.COMPLIANCE_DEFAULT]?.parentBook);

      const other = deepClone(parentBookRelatedInfoByType);

      if (selectedType) {
        delete other[BookType.REHABILITATION_DEFAULT];
        delete other[BookType.COMPLIANCE_DEFAULT];
      }

      return complianceOnly ? [compliance] : Object.values(other).map(({ parentBook }) => parentBook);
    } else if (selectedProjectVintageId && selectedType === AllTransactionType.SELL)
      return (vintageToBookTypes?.[selectedProjectVintageId] || [])?.reduce(
        (books: AdminBookResponse[], type: BookType) => {
          const parentBook = parentBookRelatedInfoByType[type]?.parentBook;
          return parentBook ? [...books, parentBook] : books;
        },
        [],
      );
    return [];
  }, [parentBookRelatedInfoByType, complianceOnly, selectedProjectVintageId, selectedType, vintageToBookTypes]);

  const selectedBook = useMemo(
    () => filteredOptionBooks?.find((book) => book?.id === selectedBookId) || asset?.source,
    [asset?.source, filteredOptionBooks, selectedBookId],
  );

  const currentBookVintage = useMemo(
    () =>
      selectedBook?.type ? parentBookRelatedInfoByType[selectedBook.type]?.ownerRegistryVintageNestedAllocations?.[
        selectedProjectVintageId as string
      ] : undefined,
    [parentBookRelatedInfoByType, selectedBook?.type, selectedProjectVintageId],
  );

  const amountAvailable = useMemo(
    () =>
      selectedType === AllTransactionType.SELL
        ? (currentBookVintage?.amountAvailable ?? 0) + (!creating ? toNumber(asset?.amount) : 0)
        : null,
    [asset?.amount, creating, currentBookVintage?.amountAvailable, selectedType],
  );

  const bookBreached = useMemo(() => selectedBook ? bookIsBreached(selectedBook, rawPrice || '') : false, [rawPrice, selectedBook]);

  const pureEditTrade = useMemo(
    () =>
      !creating
        ? deepMerge(
            pickFromRecord(model, ["trade.counterparties", "assets.0.serviceFee", "assets.0.otherFee", "memo"]),
            canEditPrices ? pickFromRecord(model, ["assets.0.unitPrice", "assets.0.rawPrice"]) : {},
          )
        : {},
    [canEditPrices, creating, model],
  );
  const inPureEditTrade = useMemo(
    () =>
      !creating
        ? deepMerge(
            pickFromRecord(formData, ["trade.counterparties", "assets.0.serviceFee", "assets.0.otherFee", "memo"]),
            canEditPrices ? pickFromRecord(formData, ["assets.0.unitPrice", "assets.0.rawPrice"]) : {},
          )
        : {},
    [canEditPrices, creating, formData],
  );

  const hasCreateChanges = useMemo(
    () =>
      !deepEqual(model, formData, {
        transform: {
          "trade.counterparties": (value: TradeCounterparty[]) =>
            value?.map((party) => ({
              ...party,
              comments: !!party?.comments?.trim() || undefined,
            })),
          "assets.0.unitPrice": (value) => toNumber(value, { parserBlacklist }),
          "assets.0.rawPrice": (value) => toNumber(value, { parserBlacklist }),
          "assets.0.serviceFee": (value) => toNumber(value, { parserBlacklist }),
          "assets.0.otherFee": (value) => toNumber(value, { parserBlacklist }),
        },
      }),
    [formData, model],
  );
  const hasEditChanges = useMemo(
    () =>
      !deepEqual(pureEditTrade, inPureEditTrade, {
        transform: {
          "trade.counterparties": (value: TradeCounterparty[]) =>
            value?.map((party) => ({
              ...party,
              comments: !!party?.comments?.trim() || undefined,
            })),
          "assets.0.unitPrice": (value) => toNumber(value, { parserBlacklist }),
          "assets.0.rawPrice": (value) => toNumber(value, { parserBlacklist }),
          "assets.0.serviceFee": (value) => toNumber(value, { parserBlacklist }),
          "assets.0.otherFee": (value) => toNumber(value, { parserBlacklist }),
        },
      }),
    [inPureEditTrade, pureEditTrade],
  );

  const {
    navigating: popoutInterrupt,
    continueNavigation,
    cancelNavigation,
  } = useNavigationInterceptor({
    shouldIntercept: () => canInterrupt && (creating ? hasCreateChanges : hasEditChanges),
  });

  usePerformantEffect(() => {
    if (!creating && !!model) reset(model);
  }, [creating, model]);

  usePerformantEffect(() => {
    if (selectedType !== AllTransactionType.SELL) {
      if (!!selectedType && creating) {
        smartSetValue("assets.0.source.id", undefined as any);
      }
    }
  }, [creating, selectedType]);

  usePerformantEffect(() => {
    if (!creating && !model?.trade?.counterparties?.some(({ isPrimary }) => isPrimary)) {
      const primaryCp = new TradeCounterparty();
      primaryCp.isPrimary = true;

      prepend(primaryCp);
    }
  }, [creating, model?.trade?.counterparties]);

  usePerformantEffect(() => {
    if (creating && selectedType === AllTransactionType.SELL && buyOnly) {
      smartSetValue("type", undefined as any);
      smartSetValue("assets.0.source.id", undefined as any);
    }
  }, [buyOnly, creating, selectedType]);

  usePerformantEffect(() => {
    if (!projectSearchTerm) toggleProjectDropdown(false);
    else
      setTimeout(() => {
        toggleProjectDropdown(true);
      }, 500);
  }, [projectSearchTerm]);

  usePerformantEffect(() => {
    if (selectedProjectId) {
      const { fetching, fetched, updated, data } = vintageMap?.[selectedProjectId] || {};
      const shouldFetch = !data?.length && !fetching && !fetched && !updated;

      if (shouldFetch) {
        updateVintageMap({
          ...vintageMap,
          [selectedProjectId]: {
            data: null,
            fetching: true,
            fetched: false,
            updated: false,
          },
        });

        setTimeout(async () => await getVintagesByProject(selectedProjectId));
      }
    }
  }, [selectedProjectId, vintageMap]);

  usePerformantEffect(() => {
    if (!isNothing(rawPrice, ["string"]) || !isNothing(feeTotal, ["string"]))
      smartSetValue(
        "assets.0.grandTotal",
        calculator(rawPrice, { parserBlacklist })
          ?.[[model?.type ?? "", selectedType].includes(AllTransactionType.SELL) ? "subtract" : "add"](feeTotal)
          .calculate()
          .toString(),
      );
  }, [rawPrice, feeTotal, selectedType, model?.type]);

  useEffect(() => {
    const subscription = watch((value, { name, type }) => {
      if (type === "change") {
        const unitPrice = toDecimal(value?.assets?.at(0)?.unitPrice, { treatNothingAsNaN: true, parserBlacklist });
        const amount = toDecimal(value?.assets?.at(0)?.amount, { treatNothingAsNaN: true, parserBlacklist });
        const rawPrice = toDecimal(value?.assets?.at(0)?.rawPrice, { treatNothingAsNaN: true, parserBlacklist });
        const serviceFee = toDecimal(value?.assets?.at(0)?.serviceFee, { parserBlacklist });
        const otherFee = toDecimal(value?.assets?.at(0)?.otherFee, { parserBlacklist });

        switch (name) {
          case "assets.0.projectVintage.project.id":
            smartSetValue("assets.0.projectVintage.id", undefined as any);
            break;
          case "assets.0.projectVintage.id":
            if (
              value?.assets?.[0]?.projectVintage?.id ===
              "_add_vintage_"
            ) {
              toggleAddVintageModal(true);
              smartSetValue(name, undefined as any);
            }
            if (!value?.assets?.at(0)?.projectVintage?.id) smartSetValue("assets.0.amount", undefined as any);
            break;
          case "assets.0.unitPrice":
            if (!amount.isNaN() && !amount.isZero())
              smartSetValue(
                "assets.0.rawPrice",
                calculator(unitPrice, { parserBlacklist }).multiply(amount).calculate().toString(),
                {
                  shouldValidate: true,
                },
              );
            else smartSetValue("assets.0.rawPrice" as any, "" as any);
            break;
          case "assets.0.amount":
            if (!amount.isNaN() && !amount.isZero()) {
              if (!unitPrice?.isNaN())
                smartSetValue(
                  "assets.0.rawPrice",
                  calculator(unitPrice, { parserBlacklist }).multiply(amount).calculate().toString(),
                  {
                    shouldValidate: true,
                  },
                );
              // this will only run once on the first input in amount
              else if (unitPrice?.isNaN() && !rawPrice?.isNaN())
                smartSetValue(
                  "assets.0.unitPrice",
                  calculator(rawPrice, { treatNothingAsNaN: true, parserBlacklist })
                    .divide(value?.assets?.at(0)?.amount)
                    .calculate()
                    .toString(),
                  {
                    shouldValidate: true,
                  },
                );
            } else smartSetValue("assets.0.rawPrice" as any, "" as any);
            break;
          case "assets.0.rawPrice":
            if (!amount.isNaN() && !amount.isZero())
              smartSetValue(
                "assets.0.unitPrice",
                calculator(value?.assets?.at(0)?.rawPrice, { treatNothingAsNaN: true, parserBlacklist })
                  .divide(value?.assets?.at(0)?.amount)
                  .calculate()
                  .toString(),
                {
                  shouldValidate: true,
                },
              );
            break;
          case "assets.0.serviceFee":
          case "assets.0.otherFee":
            smartSetValue(
              "assets.0.feeTotal",
              calculator(serviceFee, { parserBlacklist }).add(otherFee).calculate()?.toString(),
            );
            break;
          case "trade.tif":
            if (value?.trade?.tif === TIFEnum.EOD) {
              const date = new Date();
              const timeZone = "America/Los_Angeles";
              const options: Intl.DateTimeFormatOptions = {
                timeZone,
                year: "numeric",
                month: "numeric",
                day: "numeric",
              };
              const localDateString = new Intl.DateTimeFormat("en-US", options).format(date);
              const [month, day, year] = localDateString.split("/").map(Number);
              const eod = new Date(Date.UTC(year, month - 1, day, 23, 59, 59));

              smartSetValue("trade.goodUntilDate", eod?.toISOString());
            } else smartSetValue("trade.goodUntilDate", undefined);
            break;
          case "memo":
            if (isNothing(value?.memo)) smartSetValue("memo", undefined as any);
            break;
          default:
            break;
        }
      }
    });

    return (): void => subscription.unsubscribe();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [watch]);

  usePerformantEffect(() => {
    smartSetValue("assets.0.amountAvailable", selectedType === AllTransactionType.BUY ? Infinity : amountAvailable ?? undefined);
  }, [amountAvailable, selectedType]);

  const projectOptions = useAutoCompleteOptionsAsync<ProjectSearchResponse, ProjectSearchResponse>({
    q: projectSearchTerm,
    keys: ["id", "name", "registryProjectId"],
    label: (entry) => extractProjectLabel(entry),
    value: (entry) => entry?.id,
    request: {
      url: "admin/projects/search",
      queryParams: {
        name: true,
        id: true,
        fuzzy: true,
        limit: 50,
      },
      swrOptions: {
        onError: (error: any): void => {
          setProjectSearchTerm("");
          enqueueError(`Unable to search projects for search term ${projectSearchTerm}.`);
          logger.error(`Unable to search projects for search term ${projectSearchTerm}. Error: ${error?.message}`, {});
        },
      },
    },
    postTransform: (data: UseAutoCompleteOptionsReturnEntry[]) =>
      data?.sort((a, b) => a?.label?.localeCompare(b?.label)),
  });

  const vintageOptions = useAutoCompleteOptions<AdminProjectVintageResponse>({
    data: selectedProjectId ? ((vintageMap || {})[selectedProjectId]?.data || []) : [],
    keys: ["id", "name", "riskBufferPercentage", "project"],
    label: (entry) => entry?.name || '',
    displayLabel: (entry) => <ProjectVintageLabel vintage={entry} />,
    value: (entry) => entry?.id,
    postTransform: (options) => options.sort((a, b) => a.label.localeCompare(b.label)),
  });

  const bookOptions = useAutoCompleteOptions({
    data: filteredOptionBooks.filter((book): book is AdminBookResponse => book !== undefined),
    keys: ["id", "name", "type"] /* todo : @kofi to confirm if we need retirement or customerTransferOutflow here */,
    label: (entry) => entry?.name,
    displayLabel: (entry) => {
      const bookPrices = parentBookRelatedInfoByType?.[entry?.type]?.registryVintageAllocations?.groupedPrices;
      return (
        <Stack width="100%" direction="row" justifyContent="space-between" alignItems="center">
          <span>{entry?.name}</span>
          <Typography variant="caption" color="GrayText">
            {currencyFormat(
              calculator(bookPrices?.totalPriceAllocated)
                .add(bookPrices?.totalPricePendingBuy)
                .subtract(bookPrices?.totalPricePendingSell)
                .calculate()
                .toNumber(),
            )}
          </Typography>
        </Stack>
      );
    },
    value: (entry) => entry?.id,
    postTransform: (options) => options.sort((a, b) => a?.label?.localeCompare(b?.label)),
  });

  const getVintagesByProject = useCallback(
    async (id: uuid): Promise<void> => {
      try {
        const response = await api.get<AdminProjectVintageQueryResponse>(
          `admin/project-vintages?${generateQueryParams({
            projectIds: [id],
            includeRelations: [ProjectVintageRelations.PROJECT],
          })}`,
        );

        updateVintageMap({
          ...vintageMap,
          [id]: {
            data: response?.data?.data,
            fetching: false,
            fetched: true,
            updated: false,
          },
        });
      } catch (error: any) {
        updateVintageMap({
          ...vintageMap,
          [id]: {
            ...vintageMap?.[id],
            data: null,
            fetching: false,
            fetched: false,
            updated: false,
          },
        });

        enqueueError("Unable to fetch project vintage data.");
        logger.error(`Unable to fetch project vintage data. Error: ${error?.message ?? ""}`, {});
      }
    },
    [api, enqueueError, logger, updateVintageMap, vintageMap],
  );

  const handleProjectSearch = (event: SyntheticEvent, value: string, reason: AutocompleteInputChangeReason): void => {
    event?.preventDefault();

    if (reason === "input") {
      setProjectSearchTerm(value);
    }
    if (reason === "clear") {
      setProjectSearchTerm("");
      smartSetValue("assets.0.projectVintage.project.id", undefined as any);
    }

    toggleProjectDropdown(false);
  };

  const handleKeyDown = (event: KeyboardEvent<HTMLDivElement>): void => {
    if (event.key === "Escape") toggleProjectDropdown(false);
  };

  const handleClose = (): void => toggleProjectDropdown(false);

  const onSubmit = async (formData: TransactionModel): Promise<void> => {
    const { trade, assets, type, memo } = formData;
    const asset = assets?.at(0);
    const serviceFee = !isNothing(asset?.serviceFee) ? toDecimal(asset?.serviceFee, { parserBlacklist }) : null;
    const otherFee = !isNothing(asset?.otherFee) ? toDecimal(asset?.otherFee, { parserBlacklist }) : null;

    setCanInterrupt(false);

    if (creating) {
      const payload: TradeCreateRequest = {
        bookId: asset?.source?.id as uuid,
        counterparties: (trade?.counterparties || [])?.map(
          (party) =>
            ({
              role: party?.role,
              isPrimary: toBoolean(party?.isPrimary),
              comments: party?.comments || "",
              counterpartyId: party?.counterparty?.id,
            }) as TradeCounterpartyRequest,
        ),
        type: type as any,
        rawPrice: toDecimal(rawPrice, { parserBlacklist }),
        poid: trade?.poid || MISSING_DATA,
        projectVintages: [
          {
            amount: toNumber(asset?.amount, { parserBlacklist }),
            projectVintageId: asset?.projectVintage?.id as uuid,
          },
        ],
        ...px({
          counterparties: trade?.counterparties?.map(
            (party) =>
              ({
                role: party?.role,
                isPrimary: toBoolean(party?.isPrimary),
                comments: party?.comments || "",
                counterpartyId: party?.counterparty?.id,
              }) as TradeCounterpartyRequest,
          ),
          memo: memo?.trim() || null,
          serviceFee,
          otherFee,
          goodUntilDate: trade?.goodUntilDate || null,
        }),
      };

      setTradeDataRequestPayload(payload);

      if (bookBreached) toggleOpenLimitBreached(true);
      else setTimeout(() => commitTrade());
    } else {
      const payload: TradeUpdateRequest = {
        ...px({
          rawPrice: toDecimal(rawPrice, { parserBlacklist }),
          counterparties: trade?.counterparties?.map(
            (party) =>
              ({
                role: party?.role,
                isPrimary: toBoolean(party?.isPrimary),
                comments: party?.comments || "",
                counterpartyId: party?.counterparty?.id,
              }) as TradeCounterpartyRequest,
          ),
          memo: memo?.trim() || null,
          serviceFee,
          otherFee,
        }),
      };

      setTradeDataRequestPayload(payload);

      setTimeout(() => commitTrade());
    }
  };

  return (
    <Container sx={{ padding: "20px 0px" }}>
      <Maybe
        condition={!creating && [TransactionStatus.SETTLED, TransactionStatus.CANCELED].includes(model?.status as any)}
      >
        <Stack alignItems="center" gap={2}>
          <Typography variant="h6">
            Cannot edit a {model?.status === TransactionStatus.SETTLED ? "settled" : "canceled"} trade.
          </Typography>
          <Stack direction="row" gap={2}>
            <Button variant="contained" onClick={() => popFromPath(2)}>
              Back to trades
            </Button>
            <Link href="/">
              <Button variant="contained">Go Home</Button>
            </Link>
          </Stack>
        </Stack>
      </Maybe>

      <Maybe
        condition={creating || ![TransactionStatus.SETTLED, TransactionStatus.CANCELED].includes(model?.status as any)}
      >
        {() => (
          <Stack
            component="form"
            justifyContent="center"
            gap={3}
            maxWidth={1200}
            minWidth={900}
            onSubmit={handleSubmit(onSubmit)}
          >
            <Maybe condition={creating}>
              {() => (
                <>
                  <Controller
                    name="assets.0.projectVintage.project.id"
                    control={control}
                    render={({ field: { value, onChange, ...otherProps } }): JSX.Element => {
                      const selectedOption = projectOptions.find((entry) => entry?.value === value) ?? null;

                      return (
                        <Autocomplete
                          id="project"
                          open={openProjectDropdown}
                          options={projectOptions}
                          value={selectedOption}
                          onInputChange={handleProjectSearch}
                          onChange={(_, selection) => onChange(selection?.value)}
                          onKeyDown={handleKeyDown}
                          onClose={handleClose}
                          getOptionKey={(option: UseAutoCompleteOptionsReturnEntry) => option?.value}
                          getOptionLabel={(option: UseAutoCompleteOptionsReturnEntry) => option?.label}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="Project"
                              placeholder="Type the project ID or name"
                              {...otherProps}
                              error={!!errors?.assets?.[0]?.projectVintage?.project?.id}
                              helperText={errors?.assets?.[0]?.projectVintage?.project?.id?.message ?? ''}
                              fullWidth
                            />
                          )}
                          fullWidth
                          classes={{
                            popupIndicator: classes.AsyncPopoutIndicator,
                          }}
                        />
                      );
                    }}
                  />
                  <Controller
                    name="assets.0.projectVintage.id"
                    control={control}
                    render={({ field: { value, onChange, ...otherProps } }): JSX.Element => {
                      const selectedOption = vintageOptions.find((entry) => entry?.value === value) ?? null;
                      const vintage = (selectedOption?.displayLabel as { props: { vintage: PrimitiveTypeVintage } })?.["props"]?.["vintage"];

                      return (
                        <Autocomplete
                          id="vintage"
                          options={vintageOptions}
                          value={selectedOption}
                          disabled={!selectedProjectId}
                          loading={selectedProjectId ? vintageMap?.[selectedProjectId]?.fetching : false}
                          onChange={(_, selection) => onChange(selection?.value)}
                          getOptionKey={(option: UseAutoCompleteOptionsReturnEntry) => option?.value}
                          getOptionLabel={(option: UseAutoCompleteOptionsReturnEntry) => option?.label}
                          filterOptions={(
                            options: UseAutoCompleteOptionsReturnEntry[],
                            state: FilterOptionsState<UseAutoCompleteOptionsReturnEntry>,
                          ): UseAutoCompleteOptionsReturnEntry[] => {
                            if (selectedProjectId && vintageMap?.[selectedProjectId]?.fetching) return [];

                            const filteredOptions = options.filter((option) =>
                              option?.label?.toLowerCase().includes(state?.inputValue?.toLowerCase()),
                            );

                            return [
                              {
                                label: "_add_vintage_",
                                displayLabel: <AddOption />,
                                value: "_add_vintage_",
                              },
                              {
                                internal: true,
                                label: "_divider_",
                                displayLabel: <Divider />,
                                value: null,
                              },
                              ...filteredOptions,
                            ];
                          }}
                          renderInput={({ InputProps, ...params }) => (
                            <TextField
                              {...params}
                              InputProps={{
                                ...InputProps,
                                endAdornment: (
                                  <>
                                    <Maybe condition={!!vintage}>
                                      <Stack direction="row" gap={1}>
                                        <Maybe condition={!!vintage?.project?.suspended}>
                                          <SuspendedChip />
                                        </Maybe>
                                        <RCTEligibilityChip iconOnly={false} vintage={vintage} />
                                      </Stack>
                                    </Maybe>
                                    {InputProps.endAdornment}
                                  </>
                                ),
                              }}
                              label="Vintage"
                              {...otherProps}
                              error={!!errors?.assets?.[0]?.projectVintage?.id}
                              helperText={
                                !selectedProjectId
                                  ? "Please search & select Project."
                                  : errors?.assets?.[0]?.projectVintage?.id?.message ?? undefined
                              }
                              fullWidth
                            />
                          )}
                          renderOption={(props, option) => (
                            <li
                              {...props}
                              {...px(
                                {
                                  className: option?.internal ? "" : null,
                                  style: option?.internal ? {} : null,
                                  onClick: option?.internal ? NO_OP : null,
                                  onMouseMove: option?.internal ? NO_OP : null,
                                  onTouchStart: option?.internal ? NO_OP : null,
                                },
                                [false, null, undefined],
                              )}
                            >
                              {option?.displayLabel || option?.label}
                            </li>
                          )}
                          fullWidth
                        />
                      );
                    }}
                  />

                  <Stack direction="row" gap={2}>
                    <Controller
                      name="type"
                      control={control}
                      render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                        <TextField
                          select
                          label="Type"
                          value={value ?? ""}
                          InputProps={{ ref }}
                          {...otherProps}
                          error={!!errors?.type}
                          helperText={errors?.type?.message}
                          fullWidth
                        >
                          {[
                            {
                              label: "Buy",
                              value: AllTransactionType.BUY,
                            },
                            {
                              label: "Sell",
                              value: AllTransactionType.SELL,
                              disabled: buyOnly,
                            },
                          ].map((option) => (
                            <MenuItem key={option.value} value={option.value} disabled={option?.disabled}>
                              {option.label}
                            </MenuItem>
                          ))}
                        </TextField>
                      )}
                    />
                    <Controller
                      name="assets.0.source.id"
                      control={control}
                      render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                        <TextField
                          select
                          label="Book"
                          value={value ?? ""}
                          InputProps={{ ref }}
                          {...otherProps}
                          error={!!errors?.assets?.[0]?.source?.id}
                          helperText={
                            !selectedType || !selectedProjectVintageId
                              ? `Please select ${!selectedProjectVintageId && !selectedType ? "Vintage & Type." : !selectedProjectVintageId ? "Vintage." : "Type."}`
                              : errors?.assets?.[0]?.source?.message
                          }
                          disabled={!selectedType || !selectedProjectVintageId}
                          fullWidth
                        >
                          {bookOptions.map((option) => (
                            <MenuItem key={option.value} value={option.value} disabled={option.disabled}>
                              {option?.displayLabel}
                            </MenuItem>
                          ))}
                        </TextField>
                      )}
                    />
                  </Stack>
                </>
              )}
            </Maybe>
            <Maybe condition={!creating}>
              {() => (
                <>
                  <ReadOnlyField label={"Project"}>
                    <ProjectCell row={model as any} />
                  </ReadOnlyField>
                  <Stack direction="row" gap={2}>
                    <ReadOnlyField label={"Type"}>
                      <Typography>{capitalize(model?.type?.toString() ?? "")}</Typography>
                    </ReadOnlyField>
                    <ReadOnlyField label={"Book"}>
                      <Typography>{capitalize(model?.assets?.[0]?.source?.name ?? "")}</Typography>
                    </ReadOnlyField>
                  </Stack>
                </>
              )}
            </Maybe>
            <Counterparties
              counterpartyOptions={counterpartiesResponse?.data || []}
              control={control}
              errors={errors}
              lineItems={lineItems}
              smartSetValue={smartSetValue}
              append={append}
              remove={remove}
              resetField={resetField}
            />
            <Stack gap={2}>
              <Typography className={classes.SubHeader} variant="body2">
                Order Entry
              </Typography>
              <Maybe condition={bookBreached}>
                <Alert
                  severity="error"
                  variant="standard"
                  style={{
                    display: "flex",
                    alignItems: "center",
                    padding: 8,
                    border: "none",
                    backgroundColor: "#FFE0E0",
                  }}
                >
                  This trade exceeds the allowed limit for the {selectedBook?.name} book of{" "}
                  {currencyFormat(selectedBook?.limit?.holdingPriceMax?.toString() || '')}
                </Alert>
              </Maybe>
              <Stack direction="row" gap={2}>
                <Maybe condition={!!(creating || canEditPrices)}>
                  {() => (
                    <>
                      <Controller
                        control={control}
                        name="assets.0.unitPrice"
                        render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                          <NumericFormat
                            allowNegative={false}
                            thousandSeparator
                            decimalScale={2}
                            fixedDecimalScale
                            prefix="$"
                            label="Price"
                            value={value}
                            customInput={TextField}
                            InputProps={{
                              ref,
                              endAdornment: (
                                <Tooltip title="Price (net of fees)">
                                  <Box className={classes.Tooltip}>
                                    <MatIcon value="info" variant="round" size={25} color="action" />
                                  </Box>
                                </Tooltip>
                              ),
                            }}
                            error={!!errors?.assets?.[0]?.unitPrice}
                            helperText={errors?.assets?.[0]?.unitPrice?.message ?? undefined}
                            {...otherProps}
                            fullWidth
                          />
                        )}
                      />
                      <Maybe condition={!!canEditPrices}>
                        <ReadOnlyField label={"Quantity"}>
                          <Typography>{numberFormat(model?.assets?.at(0)?.amount || 0)}</Typography>
                        </ReadOnlyField>
                      </Maybe>
                      <Maybe condition={!canEditPrices}>
                        <Controller
                          control={control}
                          name="assets.0.amount"
                          render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                            <NumericFormat
                              allowNegative={false}
                              thousandSeparator
                              decimalScale={0}
                              fixedDecimalScale
                              label="Quantity"
                              value={value}
                              customInput={TextField}
                              InputProps={{
                                ref,
                              }}
                              error={!!errors?.assets?.[0]?.amount}
                              helperText={
                                <Stack component="span">
                                  <Maybe
                                    condition={
                                      !!selectedProjectVintageId && !!selectedBookId && !isNothing(amountAvailable)
                                    }
                                  >
                                    <Box component="span">
                                      Available - <strong>{numberFormat(amountAvailable || 0)}</strong>.
                                    </Box>
                                  </Maybe>
                                  <Box component="span">{errors?.assets?.[0]?.amount?.message}</Box>
                                </Stack>
                              }
                              {...otherProps}
                              fullWidth
                            />
                          )}
                        />
                      </Maybe>
                      <Controller
                        control={control}
                        name="assets.0.rawPrice"
                        render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                          <NumericFormat
                            allowNegative={false}
                            thousandSeparator
                            decimalScale={2}
                            fixedDecimalScale
                            prefix="$"
                            label={
                              [model?.type ?? "", selectedType].includes(AllTransactionType.SELL)
                                ? "Gross Revenue"
                                : "Sub Total"
                            }
                            value={value}
                            customInput={TextField}
                            InputProps={{
                              ref,
                              endAdornment: (
                                <Tooltip title="Price x Quantity">
                                  <Box className={classes.Tooltip}>
                                    <MatIcon value="info" variant="round" size={25} color="action" />
                                  </Box>
                                </Tooltip>
                              ),
                            }}
                            error={!!errors?.assets?.[0]?.rawPrice}
                            helperText={errors?.assets?.[0]?.rawPrice?.message || undefined}
                            {...otherProps}
                            fullWidth
                          />
                        )}
                      />
                    </>
                  )}
                </Maybe>
                <Maybe condition={!creating && !canEditPrices}>
                  <ReadOnlyField
                    label={"Price"}
                    endAdornment={
                      <Tooltip title="Price (net of fees)">
                        <Box className={classes.Tooltip}>
                          <MatIcon value="info" variant="round" size={25} color="action" />
                        </Box>
                      </Tooltip>
                    }
                  >
                    <Typography>{currencyFormat(model?.assets?.[0]?.unitPrice || '')}</Typography>
                  </ReadOnlyField>
                  <ReadOnlyField label={"Quantity"}>
                    <Typography>{numberFormat(model?.assets?.[0]?.amount || 0)}</Typography>
                  </ReadOnlyField>
                  <ReadOnlyField
                    label={model?.type === AllTransactionType.SELL ? "Gross Revenue" : "Sub Total"}
                    endAdornment={
                      <Tooltip title="Price x Quantity">
                        <Box className={classes.Tooltip}>
                          <MatIcon value="info" variant="round" size={25} color="action" />
                        </Box>
                      </Tooltip>
                    }
                  >
                    <Typography>{currencyFormat(model?.assets?.[0]?.rawPrice || '')}</Typography>
                  </ReadOnlyField>
                </Maybe>
              </Stack>
              <Stack direction="row" gap={2}>
                <Controller
                  control={control}
                  name="assets.0.serviceFee"
                  render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                    <NumericFormat
                      allowNegative={false}
                      thousandSeparator
                      decimalScale={2}
                      fixedDecimalScale
                      prefix="$"
                      label="Service Fee"
                      value={value}
                      customInput={TextField}
                      InputProps={{
                        ref,
                      }}
                      {...otherProps}
                      fullWidth
                    />
                  )}
                />
                <Controller
                  control={control}
                  name="assets.0.otherFee"
                  render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                    <NumericFormat
                      allowNegative={false}
                      thousandSeparator
                      decimalScale={2}
                      fixedDecimalScale
                      prefix="$"
                      label="Other Fee"
                      value={value}
                      customInput={TextField}
                      InputProps={{
                        ref,
                      }}
                      {...otherProps}
                      fullWidth
                    />
                  )}
                />
                <Controller
                  control={control}
                  name="assets.0.feeTotal"
                  render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                    <NumericFormat
                      allowNegative={false}
                      thousandSeparator
                      decimalScale={2}
                      fixedDecimalScale
                      prefix="$"
                      label="Fee Total"
                      value={value}
                      customInput={TextField}
                      InputProps={{
                        ref,
                        endAdornment: (
                          <Tooltip title="Service Fee + Other Fee">
                            <Box className={classes.Tooltip}>
                              <MatIcon value="info" variant="round" size={25} color="action" />
                            </Box>
                          </Tooltip>
                        ),
                      }}
                      {...otherProps}
                      fullWidth
                      disabled
                    />
                  )}
                />
              </Stack>
              <Stack direction="row" justifyContent="flex-end" gap={2}>
                <Controller
                  control={control}
                  name="assets.0.grandTotal"
                  render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                    <NumericFormat
                      allowNegative={false}
                      thousandSeparator
                      decimalScale={2}
                      fixedDecimalScale
                      prefix="$"
                      label={
                        [model?.type ?? "", selectedType].includes(AllTransactionType.SELL)
                          ? "Net Revenue"
                          : "Grand Total"
                      }
                      value={value}
                      customInput={TextField}
                      InputProps={{
                        ref,
                        endAdornment: (
                          <Tooltip
                            title={`${[model?.type ?? "", selectedType].includes(AllTransactionType.SELL) ? "Gross Revenue -" : "Sub Total +"} Fee Total`}
                          >
                            <Box className={classes.Tooltip}>
                              <MatIcon value="info" variant="round" size={25} color="action" />
                            </Box>
                          </Tooltip>
                        ),
                      }}
                      {...otherProps}
                      disabled
                      classes={{
                        root: classes.InputRoot,
                      }}
                    />
                  )}
                />
              </Stack>
              <Stack direction="row" justifyContent="flex-end" gap={2}>
                <Maybe condition={creating}>
                  <Controller
                    name="trade.tif"
                    control={control}
                    render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                      <TextField
                        select
                        label="TIF"
                        value={value ?? ""}
                        InputProps={{
                          ref,
                          endAdornment: (
                            <Tooltip title="Time in force">
                              <Box className={classes.Tooltip} marginRight={2}>
                                <MatIcon value="info" variant="round" size={25} color="action" />
                              </Box>
                            </Tooltip>
                          ),
                        }}
                        {...otherProps}
                        error={!!errors?.trade?.tif}
                        helperText={errors?.trade?.tif?.message}
                        fullWidth
                      >
                        {[
                          {
                            label: TIFEnum.GTC,
                            value: TIFEnum.GTC,
                          },
                          {
                            label: TIFEnum.EOD,
                            value: TIFEnum.EOD,
                          },
                        ].map((option) => (
                          <MenuItem key={option.value} value={option.value}>
                            {option.label}
                          </MenuItem>
                        ))}
                      </TextField>
                    )}
                  />
                </Maybe>
                <Maybe condition={!creating}>
                  <ReadOnlyField
                    label={"TIF"}
                    endAdornment={
                      <Tooltip title="Time in force">
                        <Box className={classes.Tooltip}>
                          <MatIcon value="info" variant="round" size={25} color="action" />
                        </Box>
                      </Tooltip>
                    }
                  >
                    <Typography>{model?.trade?.tif}</Typography>
                  </ReadOnlyField>
                </Maybe>
              </Stack>
            </Stack>
            <Stack gap={2}>
              <Typography className={classes.SubHeader} variant="body2">
                Optional Order Info
              </Typography>
              <Stack direction="row" gap={2}>
                <Maybe condition={creating}>
                  <Controller
                    name="trade.poid"
                    control={control}
                    render={({ field: { ref, value, ...otherProps } }): JSX.Element => {
                      return (
                        <TextField
                          label="External TXN ID"
                          value={value ?? ""}
                          InputProps={{ ref }}
                          {...otherProps}
                          fullWidth
                        />
                      );
                    }}
                  />
                </Maybe>
                <Maybe condition={!creating}>
                  <ReadOnlyField label={"External TXN ID"}>
                    <Typography>{model?.trade?.poid}</Typography>
                  </ReadOnlyField>
                </Maybe>
              </Stack>
              <Stack direction="row" gap={2}>
                <Controller
                  name="memo"
                  control={control}
                  render={({ field: { ref, value, ...otherProps } }): JSX.Element => {
                    return (
                      <TextField
                        label="Memo (Optional)"
                        value={value ?? ""}
                        InputProps={{ ref }}
                        minRows={4}
                        multiline
                        {...otherProps}
                        fullWidth
                      />
                    );
                  }}
                />
              </Stack>
            </Stack>
            <Stack direction="row" justifyContent="space-between">
              <Button className={classes.ActionButton} color="error" onClick={() => popFromPath(creating ? 1 : 2)}>
                Cancel
              </Button>
              <LoadingButton
                className={classes.ActionButton}
                type="submit"
                variant="contained"
                loading={committingTrade}
                disabled={!creating && !hasEditChanges}
              >
                {creating ? "Submit" : "Update"} Trade
              </LoadingButton>
            </Stack>
          </Stack>
        )}
      </Maybe>

      <GenericDialog
        open={openAddVintage}
        title="Add New Vintage"
        onClose={() => toggleAddVintageModal(false)}
        positiveAction={{
          buttonText: "CREATE",
          disabled: !canSumbitNewVintage,
          onClick: () => toggleSubmittingDialogContent(true),
        }}
        negativeAction
        onNegativeClick={() => toggleAddVintageModal(false)}
        classes={{
          root: dialogClasses.AddOption,
          content: dialogClasses.AddOptionContent,
          actions: dialogClasses.Actions,
        }}
      >
        <AddNewVintageForm
          projectId={selectedProjectId as uuid}
          submitting={!!submittingDialogContent}
          setCanSumbit={toggleCanSumbitNewVintage}
          setSubmitting={toggleSubmittingDialogContent}
          onSubmitSuccess={(projectVintage) => {
            setTimeout(() => {
              toggleAddVintageModal(false);

              updateVintageMap({
                ...vintageMap,
                [(selectedProjectId as string)]: {
                  ...(vintageMap[selectedProjectId] || {}),
                  data: [...(vintageMap[selectedProjectId as keyof typeof vintageMap]?.data || []), projectVintage],
                  fetching: false,
                  fetched: true,
                  updated: true,
                },
              });

              smartSetValue("assets.0.projectVintage.id", projectVintage?.id, {
                shouldValidate: !!selectedProjectId,
              });
            });
          }}
        />
      </GenericDialog>

      <GenericDialog
        open={popoutInterrupt}
        title={`Interrupt trade ${creating ? "creation" : "editing"}?`}
        positiveAction={{
          buttonText: "INTERRUPT ANYWAY",
          className: classcat([dialogClasses.Action, dialogClasses.InterruptPositiveAction]),
          onClick: continueNavigation,
        }}
        negativeAction={{
          buttonText: "NO",
          className: dialogClasses.Action,
          onClick: cancelNavigation,
        }}
        onClose={cancelNavigation}
        classes={{
          title: classcat([dialogClasses.Title, dialogClasses.InterruptTitle]),
          content: dialogClasses.Content,
        }}
      >
        <Typography>
          {creating ? "You will lose the data you entered" : "You will lose the change you have made"}
        </Typography>
      </GenericDialog>

      <BookBreachedModal
        open={openLimitBreached}
        bookType={selectedBook?.type as BookType}
        onContinue={() => {
          toggleOpenLimitBreached(false);
          setTimeout(async () => await commitTrade());
        }}
        onClose={() => toggleOpenLimitBreached(false)}
      />
    </Container>
  );
};

export default TradeForm;
