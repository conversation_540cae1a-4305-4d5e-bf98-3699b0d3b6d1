import COLORS from "@components/ui/theme/colors";
import { MISSING_DATA } from "@constants/constants";
import { PurchaseFlowTypeUILabel } from "@constants/purchase-flows";
import dateFormatterEST from "@/utils/formatters/est-date-formatter";
import { Stack, Grid, Typography, Divider, Tooltip, Box } from "@mui/material";
import { numberFormat, currencyFormat, toNumber, Maybe } from "@rubiconcarbon/frontend-shared";
import { Fragment } from "react";
import { InfoRounded } from "@mui/icons-material";
import { AssetType, TrimmedProjectVintageResponse } from "@rubiconcarbon/shared-types";
import RCTEligibilityChip from "@components/ui/rct-eligibility-chip/rct-eligibility-chip";
import SuspendedChip from "@components/ui/suspended-chip/suspended-chip";
import { TransactionModel } from "@models/transaction";

import classes from "../../styles/confirmation.module.scss";

type ConfirmationProps = {
  data: TransactionModel;
};

const Confirmation = ({ data }: ConfirmationProps): JSX.Element => {
  return (
    <Stack gap={4}>
      <Grid item container gap={2}>
        <Typography className={classes.Label}>Organization:</Typography>
        <Typography className={classes.Value}>{data?.sale?.customerPortfolio?.organization?.name}</Typography>
      </Grid>

      <Grid item container gap={1}>
        <Typography className={classes.SubHeader} color={COLORS.rubiconGreen}>
          Product Details
        </Typography>
        <Grid className={classes.Table} container gap={0.5}>
          <Grid className={classes.Header} item container gap={1}>
            <Grid className={classes.Label} item xs={3.5}>
              Product
            </Grid>
            <Grid className={classes.Label} item xs={2.5}>
              Book
            </Grid>
            <Grid className={classes.Label} item container xs={1.5} gap={0.5} alignItems="center">
              <Typography fontWeight="inherit">Unit Price</Typography>
              <Tooltip title="Total Price / Quantity" sx={{ cursor: "pointer" }}>
                <InfoRounded fontSize="small" htmlColor="gray" />
              </Tooltip>
            </Grid>
            <Grid className={classes.Label} item xs={1.9}>
              Quantity
            </Grid>
            <Grid className={classes.Label} item container xs={1.9} gap={0.5} alignItems="center">
              <Typography fontWeight="inherit">Total Price</Typography>
              <Tooltip title="Total Price * Quantity" sx={{ cursor: "pointer" }}>
                <InfoRounded fontSize="small" htmlColor="gray" />
              </Tooltip>
            </Grid>
          </Grid>
          <Grid className={classes.Body} item container gap={1}>
            {data?.assets?.map((asset) => (
              <Fragment key={asset?.supplementaryAssetDetails?.id}>
                <Grid className={classes.Value} item xs={3.5}>
                  {data?.sale?.productType === AssetType.REGISTRY_VINTAGE ? (
                    <Stack gap={1}>
                      <Box component="span">
                        {asset?.supplementaryAssetDetails?.registryProjectId} - {asset?.supplementaryAssetDetails?.name}
                      </Box>
                      <Stack direction="row" gap={1}>
                        <Maybe condition={!!(asset?.supplementaryAssetDetails?.wholeAsset as TrimmedProjectVintageResponse)?.["project"]?.["suspended"]}>
                          <SuspendedChip />
                        </Maybe>
                        <RCTEligibilityChip
                          iconOnly={false}
                          vintage={asset?.supplementaryAssetDetails?.wholeAsset as any}
                        />
                      </Stack>
                    </Stack>
                  ) : (
                    asset?.supplementaryAssetDetails?.name
                  )}
                </Grid>
                <Grid className={classes.Value} item xs={2.5}>
                  {asset?.source?.name}
                </Grid>
                <Grid className={classes.Value} item xs={1.5}>
                  {currencyFormat(asset?.unitPrice ?? 0)}
                </Grid>
                <Grid className={classes.Value} item xs={1.9}>
                  {numberFormat(asset?.amount)}
                </Grid>
                <Grid className={classes.Value} item xs={1.8}>
                  {currencyFormat(asset?.rawPrice ?? 0)}
                </Grid>
                <Grid item xs={12}>
                  <Divider />
                </Grid>
              </Fragment>
            ))}
          </Grid>
          <Grid className={classes.Footer} item container>
            <Grid item xs={6.7} />
            <Grid className={classes.Value} item container xs={2.2} alignItems="center" gap={0.5}>
              <Typography variant="caption">Total Quantity:</Typography>
              <Typography variant="body2" fontWeight="bold">
                {numberFormat(
                  (data.assets || [])?.reduce((sum, { amount }) => sum + toNumber(amount, { parserBlacklist: [","] }), 0),
                  { fallback: MISSING_DATA },
                )}
              </Typography>
            </Grid>
            <Grid className={classes.Value} item container xs={3.1} alignItems="center" gap={0.5}>
              <Typography variant="caption">Grand Total:</Typography>
              <Typography variant="body2" fontWeight="bold">
                {currencyFormat(
                  (data.assets || [])?.reduce(
                    (sum, { rawPrice }) => sum + toNumber(rawPrice, { parserBlacklist: ["$", ","] }),
                    0,
                  ),
                  { fallback: MISSING_DATA },
                )}
              </Typography>
            </Grid>
          </Grid>
        </Grid>
      </Grid>

      <Grid item container gap={1}>
        <Typography className={classes.SubHeader} color={COLORS.rubiconGreen}>
          Sale Details
        </Typography>
        <Grid container gap={1}>
          <Grid item container gap={1}>
            <Grid className={classes.Label} item xs={4}>
              Settlement Type
            </Grid>
            <Grid className={classes.Label} item xs={3}>
              Risk Adjustment
            </Grid>
            <Grid className={classes.Label} item xs={2}>
              Payment Due Date
            </Grid>
          </Grid>
          <Grid item container gap={1}>
            <Grid className={classes.Value} item xs={4}>
              {data?.sale?.flowType ? PurchaseFlowTypeUILabel[data.sale.flowType] : undefined}
            </Grid>
            <Grid
              className={classes.Value}
              item
              xs={3}
            >{`${!data?.sale?.needsRiskAdjustment ? "Not " : ""}Added`}</Grid>
            <Grid className={classes.Value} item xs={2}>
              <Maybe condition={!!data?.sale?.paymentDueDate} fallback={MISSING_DATA}>
                {dateFormatterEST(data?.sale?.paymentDueDate?.toString() || "", ["date"])}
              </Maybe>
            </Grid>
          </Grid>
        </Grid>
        <Grid container direction="column" gap={1}>
          <Typography className={classes.Label}>Memo</Typography>
          <Typography className={classes.Value}>{data?.memo?.trim() || MISSING_DATA}</Typography>
        </Grid>
      </Grid>
    </Stack>
  );
};

export default Confirmation;
