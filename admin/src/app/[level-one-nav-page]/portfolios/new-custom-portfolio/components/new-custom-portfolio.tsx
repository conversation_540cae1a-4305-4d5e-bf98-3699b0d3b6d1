import { Grid, Autocomplete, Paper, Container, <PERSON>ack, TextField, Button } from "@mui/material";
import Decimal from "decimal.js";
import {
  AdminBookResponse,
  CustomPortfolioBookCreateRequest,
  OrganizationResponse,
  uuid,
} from "@rubiconcarbon/shared-types";
import { IsNotEmpty } from "class-validator";
import { classValidatorResolver } from "@hookform/resolvers/class-validator";
import { useMemo, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import useAutoCompleteOptions, { UseAutoCompleteOptionsReturnEntry } from "@hooks/use-auto-complete-options";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { LoadingButton } from "@mui/lab";
import { useLogger } from "@providers/logging";
import useNavigation from "@/hooks/use-navigation";
import { useRouter } from "next/navigation";

import classes from "../styles/new-custom-portfolio.module.scss";

class CreateCustomPortfolioModel {
  @IsNotEmpty({ message: "Required" })
  customer?: uuid;

  @IsNotEmpty({ message: "Required" })
  name?: string;

  @IsNotEmpty({ message: "Required" })
  description?: string;
}

const CREATE_FORM_VALUES: CreateCustomPortfolioModel = {};

const NewCustomPortfolio = ({
  organizations: customers = [],
}: {
  organizations: OrganizationResponse[];
}): JSX.Element => {
  const { logger } = useLogger();
  const router = useRouter();
  const { replacePathFromSegment } = useNavigation();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const [portfolioPayload, setPortfolioPayload] = useState<CustomPortfolioBookCreateRequest>();

  const CreateCustomPortfolioRequestResolver = useMemo(() => classValidatorResolver(CreateCustomPortfolioModel), []);

  const customerOptions = useAutoCompleteOptions({
    data: customers,
    keys: ["id", "name"],
    label: (entry: any) => entry?.name,
    value: (entry: any) => entry?.id,
    postTransform: (data: UseAutoCompleteOptionsReturnEntry[]) =>
      data?.sort((a, b) => a?.label?.localeCompare(b?.label)),
  });

  const {
    handleSubmit,
    formState: { errors, isSubmitting },
    control,
    watch,
  } = useForm<CreateCustomPortfolioModel>({
    resolver: CreateCustomPortfolioRequestResolver,
    mode: "onTouched",
    defaultValues: CREATE_FORM_VALUES,
  });

  const organizationId = watch("customer");

  const { trigger: saveBasket } = useTriggerRequest<AdminBookResponse, CustomPortfolioBookCreateRequest>({
    url: "admin/books/custom",
    method: "post",
    requestBody: portfolioPayload,
    swrOptions: {
      onSuccess: () => {
        enqueueSuccess("Successfully created custom portfolio.");
        replacePathFromSegment(1, `/`);
      },
      onError: (error: any) => {
        enqueueError("Unable to create custom porfolio.");
        logger.error(error?.message, {});
      },
    },
  });

  const onSubmit = async (formData: CreateCustomPortfolioModel): Promise<void> => {
    setPortfolioPayload({
      name: formData?.name ?? "",
      description: formData?.description,
      isEnabled: true,
      organizationId: organizationId,
      purchasePrice: new Decimal(0),
    });

    setTimeout(async () => await saveBasket());
  };

  return (
    <Container className={classes.Container} maxWidth={false} component={Paper}>
      <form id="new-custom-portfolio-form" onSubmit={handleSubmit(onSubmit)}>
        <Stack gap={5}>
          <fieldset style={{ display: "contents" }}>
            <Controller
              name="customer"
              control={control}
              render={({ field: { ref, value, onChange, ...otherProps } }): JSX.Element => (
                <Autocomplete
                  options={customerOptions}
                  value={customerOptions.find(({ value: v }) => v === value) || null}
                  loading
                  disablePortal
                  id="customer"
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Select customer"
                      inputRef={ref}
                      required
                      error={!!errors.customer}
                      helperText={errors.customer?.message}
                      {...otherProps}
                      fullWidth
                    />
                  )}
                  onChange={(_, selection) => onChange(selection?.value)}
                />
              )}
            />
          </fieldset>
          <fieldset style={{ display: "contents" }}>
            <Controller
              name="name"
              control={control}
              render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                <TextField
                  label="Portfolio name"
                  value={value ?? ""}
                  InputProps={{ ref }}
                  required
                  error={!!errors.name}
                  helperText={errors.name?.message}
                  {...otherProps}
                  fullWidth
                />
              )}
            />
          </fieldset>
          <fieldset style={{ display: "contents" }}>
            <Controller
              name="description"
              control={control}
              render={({ field: { ref, value, ...otherProps } }): JSX.Element => (
                <TextField
                  label="Portfolio description"
                  value={value ?? ""}
                  multiline
                  minRows={2}
                  InputProps={{ ref }}
                  required
                  error={!!errors.description}
                  helperText={errors.description?.message}
                  {...otherProps}
                  fullWidth
                />
              )}
            />
          </fieldset>
          <Grid className={classes.ActionContainer} item container justifyContent="end" gap={2}>
            <Button className={classes.Button} disabled={isSubmitting} variant="text" onClick={() => router.back()}>
              cancel
            </Button>
            <LoadingButton
              className={classes.Button}
              variant="contained"
              type="submit"
              form="new-custom-portfolio-form"
              loading={isSubmitting}
            >
              save
            </LoadingButton>
          </Grid>
        </Stack>
      </form>
    </Container>
  );
};

export default NewCustomPortfolio;
