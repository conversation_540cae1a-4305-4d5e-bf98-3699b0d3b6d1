import { ChangeEvent, useCallback, useContext, useEffect, useMemo, useState } from "react";
import { KeyedMutator } from "swr";
import {
  AdminBookResponse,
  AllRubiconHoldingBookTypes,
  BookRelations,
  GroupedAllocationWithNestedResponse,
  ProjectTypeQueryResponse,
  ProjectTypeRelations,
  ProjectTypeResponse,
  TrimmedProjectVintageResponse,
  uuid,
} from "@rubiconcarbon/shared-types";
import EnhancedTable, { ColDef } from "@components/ui/table/enhanced-table";
import { Checkbox, Chip, FormControlLabel, Grid, Stack, Tooltip, Typography } from "@mui/material";
import TableBox from "@components/ui/table-box/table-box";
import ActionButton from "@components/ui/action-button/action-button";
import {
  Maybe,
  deepClone,
  pickFromArrayOfRecords,
  searchByKeys,
  toTitleCase,
  uniqueByKeys,
} from "@rubiconcarbon/frontend-shared";
import { AxiosContext } from "@providers/axios-provider";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import { useUpdateEffect } from "react-use";
import { useLogger } from "@providers/logging";
import Decimal from "decimal.js";
import GenericDialog from "@components/ui/generic-dialog/generic-dialog";

import classes from "../styles/edit-credit-types.module.scss";

type ProjectTypeItem = {
  checked: boolean;
  allocated?: boolean;
  updateType?: "added" | "removed";
} & ProjectTypeResponse;

type ProjectCategoryItem = {
  id: string;
  checked: boolean;
  hasAllocated?: boolean;
  category: string;
  totalAvailable: number;
  types: ProjectTypeItem[];
};

type Result = {
  id: number;
  action: "allowed" | "not allowed";
  type: string;
  total: number;
};

type RowCheckboxProps = {
  name?: string;
  value?: number;
  checked: boolean;
  indeterminate?: boolean;
  disabled?: boolean;
  onChange: (event: ChangeEvent<HTMLInputElement>) => void;
};

type StatusChipProps = {
  allocated?: boolean;
  updateType?: "added" | "removed";
};

type EditProjectTypesDialogProps = {
  bookId: uuid;
  open: boolean;
  onClose: () => void;
  refreshBook: KeyedMutator<AdminBookResponse>;
};

const holdingAmount = (type: Partial<ProjectTypeItem>): number => {
  return Decimal.sum(
    ...(type?.assetAllocationsByBookType || [])
      .filter((f) => AllRubiconHoldingBookTypes.includes(f.bookType))
      .map((m) => m.totalAmountAllocated),
    0,
  ).toNumber();
};

const RowCheckbox = ({ name, value, checked, indeterminate, disabled, onChange }: RowCheckboxProps): JSX.Element => (
  <Checkbox
    name={name}
    value={value}
    checked={checked}
    indeterminate={indeterminate}
    disabled={disabled}
    onChange={onChange}
  />
);

const StatusChip = ({ allocated, updateType }: StatusChipProps): JSX.Element => (
  <>
    <Maybe condition={!!updateType}>
      <Chip
        className={`${classes.Chip} ${updateType === "added" ? classes.AddedChip : classes.RemovedChip}`}
        label={updateType}
      />
    </Maybe>
    <Maybe condition={!!allocated}>
      <Tooltip title="Credits are allocated to the book">
        <Chip className={`${classes.Chip} ${classes.AllocatedChip}`} label="allocated" />
      </Tooltip>
    </Maybe>
  </>
);

const EditProjectTypesDialog = ({ bookId, open, onClose, refreshBook }: EditProjectTypesDialogProps): JSX.Element => {
  const { api } = useContext(AxiosContext);
  const { logger } = useLogger();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();

  const {
    data: book,
    trigger: getBook,
    isMutating: isBookLoading,
  } = useTriggerRequest<AdminBookResponse>({
    url: "admin/books/{id}",
    pathParams: {
      id: bookId,
    },
    queryParams: {
      includeRelations: [BookRelations.OWNER_ALLOCATIONS_BY_PROJECT_TYPE],
    },
    swrOptions: {
      onSuccess: () => {
        setTimeout(async () => await getTypes());
      },
      onError: (error: any) => {
        enqueueError("Unable to load book.");
        logger.error(`Unable to fetch book with id: ${bookId}. Error: ${error?.message}`, {});
      },
    },
  });

  const { data: typesResponse, trigger: getTypes } = useTriggerRequest<ProjectTypeQueryResponse>({
    url: "admin/project-types",
    pathParams: {
      id: bookId,
    },
    queryParams: {
      limit: 500,
      includeRelations: [ProjectTypeRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE],
    },
    swrOptions: {
      onError: (error: any) => {
        enqueueError("Unable to load property types.");
        logger.error(`Unable to fetch property types. Error: ${error?.message}`, {});
      },
    },
  });

  const original = useMemo(
    () => Object.freeze(book?.ownerAllocationsByProjectType || []),
    [book?.ownerAllocationsByProjectType],
  );
  // todo : @kofi to check, I think you can just look in ownerAllocationsByProjectTypeId
  const originalIds = useMemo(() => Object.freeze(original.map(({ projectType }) => projectType.id)), [original]);
  const allocatedIds = useMemo(() => {
    return (book?.ownerAllocations as GroupedAllocationWithNestedResponse)?.allocations?.reduce(
      (accum: number[], component) => {
        const id = (component.detailedAsset as TrimmedProjectVintageResponse)?.project?.projectType?.id;
        if (!!id && !accum.includes(id)) accum.push(id);
        return accum;
      },
      [],
    );
  }, [book?.ownerAllocations]);

  const originalCategories = useMemo(() => {
    const responseData = typesResponse?.data || [];
    const uniqueCategories = uniqueByKeys(responseData, ["category"]).map(({ category }) => category);

    // todo : @kofi I think this whole part needs to be rewritten too, it should be ablet o use ownerAllocationsByProjectType
    const categories: ProjectCategoryItem[] = uniqueCategories.reduce((accum, current) => {
      let types = pickFromArrayOfRecords<ProjectTypeResponse>(
        responseData.filter(({ category }) => category === current),
        ["id", "name", "type", "assetAllocationsByBookType"],
      ) as ProjectTypeItem[];

      types = types.map((projectType: ProjectTypeItem) => ({
        allocated: allocatedIds?.includes(projectType.id),
        ...projectType,
        checked: originalIds.includes(projectType.id),
      }));

      accum.push({
        id: current,
        hasAllocated: types?.some(({ allocated }) => !!allocated),
        checked: types?.every(({ checked }) => !!checked),
        category: current,
        totalAvailable: types.reduce((sum, type) => sum + holdingAmount(type), 0),
        types,
      });

      return accum;
    }, [] as ProjectCategoryItem[]);
    return categories;
  }, [allocatedIds, originalIds, typesResponse?.data]);

  const [confirmationDialogOpened, setConfirmationDialogOpened] = useState<boolean>(false);
  const [allSelected, setAllSelected] = useState<boolean>(false);
  const [someSelected, setSomeSelected] = useState<boolean>(false);
  const [hasChanges, setHasChanges] = useState<boolean>(false);
  const [allSelectedCategory, setAllSelectedCategory] = useState<Record<string, boolean>>({});
  const [someSelectedCategory, setSomeSelectedCategory] = useState<Record<string, boolean>>({});
  const [projectCategories, setProjectCategories] = useState<ProjectCategoryItem[]>([]);
  const [filteredIds, setFilteredIds] = useState<string[]>([]);

  const added: Result[] = useMemo(
    () =>
      projectCategories.flatMap(({ types }) =>
        types
          .filter(({ updateType }) => updateType === "added")
          .map(({ id, type, ...rest }) => ({
            id,
            action: "allowed",
            type,
            total: holdingAmount(rest),
          })),
      ),
    [projectCategories],
  );

  const removed: Result[] = useMemo(
    () =>
      projectCategories.flatMap(({ types }) =>
        types
          .filter(({ updateType }) => updateType === "removed")
          .map(({ id, type, ...rest }) => ({
            id,
            action: "not allowed",
            type,
            total: holdingAmount(rest),
          })),
      ),
    [projectCategories],
  );

  const columnsDef: ColDef[] = [
    {
      columnName: "checked",
      displayName: "",
      displayComponent: (
        <RowCheckbox
          name="global"
          checked={allSelected}
          indeterminate={someSelected}
          onChange={(event: ChangeEvent<HTMLInputElement>) => handleCheckboxCheck(event)}
        />
      ),
      formatter: {
        func: (data: any) => (
          <RowCheckbox
            value={data.get("id") as number}
            checked={allSelectedCategory[data.get("category") as string]}
            indeterminate={someSelectedCategory[data.get("category") as string]}
            disabled={data.get("allocated") as boolean}
            onChange={handleCheckboxCheck}
          />
        ),
        inputFields: ["id", "checked", "allocated", "category"],
      },
      sortable: false,
    },
    {
      columnName: "category",
      displayName: "Category (Total Holding)",
      formatter: {
        func: (data: any) => (
          <Typography>
            {data.get("category") as string}
            {"  "}
            <strong>({data.get("totalAvailable") as string})</strong>
          </Typography>
        ),
        inputFields: ["category", "totalAvailable"],
      },
    },
    {
      columnName: "types",
      displayName: "Types (Total Holding)",
      formatter: {
        func: (data: any): JSX.Element => (
          <Stack>
            {(data.get("types") as ProjectTypeItem[]).map(({ id, checked, type, allocated, updateType, ...rest }) => (
              <Grid key={type} container gap={1} justifyContent="space-between" alignItems="center">
                <Grid item sm={8} md={8} lg={9} xl={9.5}>
                  <Tooltip title={allocated ? "Credits are allocated to the book" : ""}>
                    <FormControlLabel
                      control={
                        <RowCheckbox
                          name={data.get("category") as string}
                          value={id}
                          checked={checked}
                          disabled={allocated}
                          onChange={handleCheckboxCheck}
                        />
                      }
                      label={
                        <>
                          {toTitleCase(type, null, /(and|to|in)/i)} <strong>({holdingAmount(rest)})</strong>
                        </>
                      }
                    />
                  </Tooltip>
                </Grid>
                <Grid item sm={3} md={3} lg={2} xl={1.5}>
                  <StatusChip allocated={allocated} updateType={updateType} />
                </Grid>
              </Grid>
            ))}
          </Stack>
        ),
        inputFields: ["category", "totalAvailable", "types"],
      },
    },
  ];

  const confirmationColumnDef: ColDef[] = [
    {
      columnName: "action",
      displayName: "Action",
      formatter: {
        func: (input: any) => toTitleCase(input as string),
      },
    },
    {
      columnName: "type",
      displayName: "Credit Type",
      formatter: {
        func: (input: any) => toTitleCase(input as string),
      },
    },
    {
      columnName: "total",
      displayName: "Total Credits in Inventory",
    },
  ];

  useUpdateEffect(() => {
    if (open && !isBookLoading) setTimeout(async () => await getBook());
  }, [open]);

  useEffect(() => {
    setProjectCategories(
      Object.assign<ProjectCategoryItem[], ProjectCategoryItem[]>([], originalCategories).sort((a, b) =>
        a.hasAllocated ? -1 : b.hasAllocated ? 1 : 0,
      ),
    );
    setFilteredIds(originalCategories.map(({ id }) => id));
  }, [originalCategories]);

  useEffect(() => {
    setAllSelectedCategory(
      projectCategories.reduce((accum, { category, types }) => {
        Object.assign(accum, { [category]: types.every(({ checked }) => !!checked) });
        return accum;
      }, {}),
    );
  }, [projectCategories]);

  useEffect(() => {
    setSomeSelectedCategory(
      projectCategories.reduce((accum, { category, types }) => {
        Object.assign(accum, { [category]: !allSelectedCategory[category] && types.some(({ checked }) => !!checked) });
        return accum;
      }, {}),
    );
  }, [projectCategories, allSelectedCategory]);

  useEffect(() => {
    setAllSelected(Object.values(allSelectedCategory).every((checked) => !!checked));
  }, [allSelectedCategory]);

  useEffect(() => {
    setSomeSelected(
      !allSelected &&
        (Object.values(someSelectedCategory).some((checked) => !!checked) ||
          Object.values(allSelectedCategory).some((checked) => !!checked)),
    );
  }, [allSelected, allSelectedCategory, someSelectedCategory]);

  useEffect(() => {
    setHasChanges(projectCategories.some(({ types }) => types.some(({ updateType }) => !!updateType)));
  }, [projectCategories]);

  const handleChangesReset = useCallback(() => {
    setProjectCategories(originalCategories.sort((a, b) => (a.hasAllocated ? -1 : b.hasAllocated ? 1 : 0)));
  }, [originalCategories]);

  const handleCheckboxCheck = (event: ChangeEvent<HTMLInputElement>): void => {
    event.preventDefault();

    const { checked, value, name } = event.currentTarget;
    const clone: ProjectCategoryItem[] = deepClone(projectCategories);

    if (name === "global") {
      setProjectCategories((previous) =>
        previous.map(({ types, ...rest }) => ({
          ...rest,
          checked,
          types: types.map(({ id, updateType, allocated, ...rest }) => ({
            id,
            allocated,
            ...rest,
            checked: !allocated ? checked : rest.checked,
            updateType: !allocated
              ? originalIds.includes(id)
                ? !checked
                  ? "removed"
                  : undefined
                : !originalIds.includes(id)
                  ? checked
                    ? "added"
                    : undefined
                  : updateType
              : undefined,
          })),
        })),
      );
    } else {
      const checkValue = parseInt(value);

      if (isNaN(checkValue)) {
        const catIndex = clone.findIndex((cat) => cat.category === value);
        const types = clone[catIndex].types;

        clone[catIndex].types = types.map(({ id, updateType, allocated, ...rest }) => ({
          id,
          allocated,
          ...rest,
          checked: !allocated ? checked : rest.checked,
          updateType: !allocated
            ? originalIds.includes(id)
              ? !checked
                ? "removed"
                : undefined
              : !originalIds.includes(id)
                ? checked
                  ? "added"
                  : undefined
                : updateType
            : undefined,
        }));

        setProjectCategories(clone);
      } else {
        const catIndex = clone.findIndex((cat) => cat.category === name);
        const types = clone[catIndex].types;
        const typeIndex = types.findIndex((type) => type.id === checkValue);
        const type = clone[catIndex].types[typeIndex];

        type.checked = type.id === checkValue ? checked : type.checked;
        type.updateType =
          type.id === checkValue
            ? originalIds.includes(checkValue)
              ? !checked
                ? "removed"
                : undefined
              : !originalIds.includes(checkValue)
                ? checked
                  ? "added"
                  : undefined
                : type.updateType
            : type.updateType;

        setProjectCategories(clone);
      }
    }
  };

  const handleProjectTypesSearch = (input: string): void => {
    const filteredIds = searchByKeys(input, projectCategories, ["category", "types.type"]).map(({ id }) => id);
    setFilteredIds(filteredIds);
  };

  const saveUpdatedProjectTypes = async (): Promise<void> => {
    const projectTypeIds = projectCategories.flatMap(({ types }) =>
      types.filter(({ checked }) => !!checked).map(({ id }) => id),
    );

    try {
      await api.patch(`admin/books/${book?.id}`, { projectTypeIds });

      refreshBook();
      setConfirmationDialogOpened(false);
      onClose();
      enqueueSuccess("Successfully updated allowed credit types for portfolio");
    } catch {
      enqueueError("Unable to update allowed credit types for portfolio");
    }
  };

  const SearhBarContent = useMemo(
    (): JSX.Element => (
      <ActionButton onClickHandler={() => handleChangesReset()} isDisabled={!hasChanges}>
        Reset
      </ActionButton>
    ),
    [handleChangesReset, hasChanges],
  );

  return (
    <>
      <GenericDialog
        open={open}
        title="Edit Credit Types"
        dismissable
        positiveAction={{
          buttonText: "SAVE",
          disabled: !hasChanges,
        }}
        negativeAction
        classes={{
          root: classes.EditCreditTypes,
        }}
        onClose={onClose}
        onNegativeClick={onClose}
        onPositiveClick={() => setConfirmationDialogOpened(true)}
      >
        <TableBox variant="outlined">
          <EnhancedTable
            name="projectCategories"
            columnsDef={columnsDef}
            data={projectCategories.filter(({ id }) => filteredIds.includes(id))}
            elevation={0}
            getFilteredData={handleProjectTypesSearch}
            searchBarContent={() => SearhBarContent}
          />
        </TableBox>
      </GenericDialog>
      <GenericDialog
        open={confirmationDialogOpened}
        title="Confirm Credit Types Updates"
        positiveAction={{
          buttonText: "YES, PROCEED",
        }}
        negativeAction
        classes={{
          root: classes.ConfirmationRoot,
        }}
        onClose={() => setConfirmationDialogOpened(false)}
        onPositiveClick={saveUpdatedProjectTypes}
        onNegativeClick={() => setConfirmationDialogOpened(false)}
      >
        <Stack gap={3}>
          <Typography>Are you sure you want to save the following changes?</Typography>
          <TableBox variant="outlined">
            <EnhancedTable
              name="confirmation"
              columnsDef={confirmationColumnDef}
              data={[...added, ...removed]}
              elevation={0}
            />
          </TableBox>
        </Stack>
      </GenericDialog>
    </>
  );
};

export default EditProjectTypesDialog;
