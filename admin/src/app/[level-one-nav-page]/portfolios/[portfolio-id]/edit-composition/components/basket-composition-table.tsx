import React, { useState, useEffect, useCallback, useContext, useMemo } from "react";
import useS<PERSON> from "swr";
import Decimal from "decimal.js";
import Link from "next/link";
import {
  Box,
  Typography,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Tooltip,
  TextField,
  Autocomplete,
  Stack,
} from "@mui/material";
import CreateIcon from "@mui/icons-material/Create";
import DeleteIcon from "@mui/icons-material/Delete";
import CheckIcon from "@mui/icons-material/Check";
import {
  PermissionEnum,
  uuid,
  ProjectTypeResponse,
  VintagePricingResponse,
  AdminBookResponse,
  TrimmedProjectResponse,
  AdminProjectVintageQueryResponse,
  AdminProjectVintageResponse,
  AdminProjectQueryResponse,
  BookType,
  ProjectVintageRelations,
  BookRelations,
  ProjectRelations,
  GroupedAllocationWithNestedResponse,
  AssetType,
  TrimmedProjectVintageResponse,
} from "@rubiconcarbon/shared-types";
import DetailsIcon from "@mui/icons-material/Info";
import integerFormat from "@/utils/formatters/integer-format";
import COLORS from "@components/ui/theme/colors";
import SearchAppBar from "@components/ui/search/enhanced-search";
import AddIcon from "@mui/icons-material/Add";
import ActionButton from "@components/ui/action-button/action-button-enhanced";
import UndoIcon from "@mui/icons-material/Undo";
import { NumericFormat } from "react-number-format";
import { convertAmountToNumber } from "@utils/helpers/general/general";
import ProjectVintage, { getBufferCategory } from "@models/project-vintage";
import CancelIcon from "@mui/icons-material/Cancel";
import { AxiosContext } from "@providers/axios-provider";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import BasketCompositionVintage from "@models/basket-composition-vintage";
import { isEmpty, isNil } from "lodash";
import { MISSING_DATA, MISSING_PERMISSIONS, RCT_STANDARD, SUSPENDED } from "@constants/constants";
import currencyFormat from "@/utils/formatters/currency-format";
import TableBox from "@components/ui/table-box/table-box";
import { isNothing, Maybe, Undefinable, useRequest } from "@rubiconcarbon/frontend-shared";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import { CsvBuilder } from "filefy";
import decimalFormat from "@/utils/formatters/decimal-format";
import SuspendedChip from "@components/ui/suspended-chip/suspended-chip";
import RCTEligibilityChip from "@components/ui/rct-eligibility-chip/rct-eligibility-chip";
import useAuth from "@providers/auth-provider";
import { useLogger } from "@providers/logging";
import { omitFromRecord, percentageFormat } from "@rubiconcarbon/frontend-shared";
import { projectIsRCTEligible } from "@utils/helpers/project/project-is-rct-eligible";
import CompositionConfirmationModal from "./composition-confirmation-modal";

interface CustomTrimmedProjectResponse extends TrimmedProjectResponse {
  bufferCategoryName?: string;
}

interface ProjectVintageTableRow extends ProjectVintage {
  isHide?: boolean;
}

interface IFormField {
  error: boolean;
  message: string;
}

interface IProject extends IFormField {
  value?: CustomTrimmedProjectResponse;
}

interface IVintage extends IFormField {
  value?: ProjectVintage;
}

interface IQuantity extends IFormField {
  value?: number;
}

function validateQuantity(quantity: number, availableAmount: number): IQuantity {
  if (quantity === 0)
    return {
      value: quantity,
      error: true,
      message: "Quantity must be greater than 0",
    };

  if (quantity > availableAmount) {
    return {
      value: quantity,
      error: true,
      message: `Quantity must be equal to or less than amount available (${integerFormat(availableAmount)})`,
    };
  }
  return {
    value: quantity,
    error: false,
    message: `${integerFormat(availableAmount)} available`,
  };
}

const isRowContainsValue = (row: ProjectVintage, searchString: string): boolean => {
  if (
    row.projectRegistryId?.toUpperCase().includes(searchString) ||
    row.projectName.toUpperCase().includes(searchString) ||
    row.vintageName.toUpperCase().includes(searchString) ||
    row.quantity.toString().includes(searchString) ||
    row.projectLocation?.toUpperCase().includes(searchString) ||
    row.projectType?.toUpperCase().includes(searchString) ||
    (row?.suspended === true && SUSPENDED.includes(searchString.toUpperCase())) ||
    (row?.rctStandard === true && RCT_STANDARD.includes(searchString.toUpperCase()))
  )
    return true;

  return false;
};

const newRowStyle = {
  verticalAlign: "top",
};

const itemStyle = {
  paddingTop: "10px",
};

const getProjectsList = (book?: AdminBookResponse): CustomTrimmedProjectResponse[] => {
  const projectList: CustomTrimmedProjectResponse[] = [];

  if (
    !!book &&
    book?.ownerAllocations &&
    (book.ownerAllocations as GroupedAllocationWithNestedResponse)?.allocations?.length > 0
  ) {
    (book.ownerAllocations as GroupedAllocationWithNestedResponse)?.allocations
      ?.filter((allocation) => allocation.asset.type === AssetType.REGISTRY_VINTAGE)
      ?.forEach((allocation) => {
        const project = (allocation.detailedAsset as TrimmedProjectVintageResponse).project;
        if (!projectList.find((p) => p.id === project?.id)) {
          projectList.push(project!);
        }
      });
  }
  return projectList;
};

const isEligibleFormatter = (inputMap: Map<string, any>): string => {
  const buffer = inputMap.get("buffer");
  return projectIsRCTEligible({
    riskBufferPercentage: buffer as any,
    project: {
      suspended: inputMap.get("suspended"),
      rctStandard: inputMap.get("rctStandard"),
      isScienceTeamApproved: inputMap.get("isScienceTeamApproved"),
    },
  } as any)
    ? "Yes"
    : "No";
};

function Row(props: {
  row: ProjectVintage;
  idx: uuid;
  allowActions: boolean;
  editIdx?: uuid;
  startEditing: (idx: uuid) => void;
  stopEditing: () => void;
  deleteHandler: (id: uuid) => void;
  currencyOnChangeHandler: (value?: number, id?: uuid) => void;
}): JSX.Element {
  const { row, idx, allowActions, editIdx, startEditing, stopEditing, deleteHandler, currencyOnChangeHandler } = props;
  const { api } = useContext(AxiosContext);
  const { enqueueError } = useSnackbarVariants();
  const { logger } = useLogger();

  const [availableQuantity, setAvailableQuantity] = useState<number>(0);
  const [quantity, setQuantity] = useState<IQuantity>({
    value: row.quantity,
    error: false,
    message: `${integerFormat(availableQuantity)} available`,
  });
  const [initialValue, setInitialValue] = useState<Undefinable<number>>();

  const getVintageAvailableAmount = useCallback(
    (id: uuid) => {
      api
        .get<AdminProjectVintageResponse>(
          `admin/project-vintages/${id}?includeRelations=${ProjectVintageRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE}`,
        )
        .then(async (response) => {
          if (response?.data)
            setAvailableQuantity(
              (row?.amountAvailable ?? 0) +
                (response?.data?.assetAllocationsByBookType?.find((f) => f.bookType === BookType.PORTFOLIO_DEFAULT)
                  ?.totalAmountAvailable ?? 0),
            );
        })
        .catch((e) => {
          enqueueError("Failed to retrieve vintage information.");
          logger.error(`Failed to retrieve vintage information: ${e?.message}`, {});
        });
    },
    [api, row, enqueueError, logger],
  );

  const editRowHandler = (idx: uuid): void => {
    getVintageAvailableAmount(row.id);
    startEditing(idx);
  };

  useEffect(() => {
    setQuantity({
      value: row.quantity,
      error: false,
      message: `${integerFormat(availableQuantity)} available`,
    });
    setInitialValue(row.quantity);
  }, [row.amountAvailable, row.quantity, availableQuantity]);

  const currencyRowOnChangeHandler = (event: React.ChangeEvent<HTMLInputElement>): void => {
    const value = convertAmountToNumber(event.target.value);
    const newQuantity = validateQuantity(value, availableQuantity);
    setQuantity(newQuantity);
  };

  const cancelEditHandler = (): void => {
    stopEditing();
    currencyOnChangeHandler(initialValue, row.id);
  };

  const stopEditingHandler = (): void => {
    if (!quantity.error) currencyOnChangeHandler(quantity?.value, row.id);
    stopEditing();
  };

  const currentlyEditing = idx === editIdx;

  return (
    <React.Fragment>
      <TableRow sx={{ verticalAlign: "top" }}>
        <TableCell align="left">
          <Typography variant="body2" component="div" sx={itemStyle}>
            <RCTEligibilityChip
              vintage={
                { riskBufferPercentage: row?.buffer as any, project: omitFromRecord(row, ["buffer"]) as any } as any
              }
            />
          </Typography>
        </TableCell>
        <TableCell align="left">
          <Typography variant="body2" component="div" sx={itemStyle}>
            {row.projectRegistryId ?? MISSING_DATA}
            <Stack direction="row" gap={1}>
              <Maybe condition={row?.suspended === true}>
                <SuspendedChip />
              </Maybe>
            </Stack>
          </Typography>
        </TableCell>
        <TableCell align="left">
          <Typography variant="body2" component="div" sx={itemStyle}>
            {row.projectName ?? MISSING_DATA}
          </Typography>
        </TableCell>
        <TableCell align="left">
          <Typography variant="body2" component="div" sx={itemStyle}>
            {row.projectType ?? MISSING_DATA}
          </Typography>
        </TableCell>
        <TableCell align="left">
          <Typography variant="body2" component="div" sx={itemStyle}>
            {row.projectLocation ?? MISSING_DATA}
          </Typography>
        </TableCell>
        <TableCell align="left">
          <Typography variant="body2" component="div" sx={itemStyle}>
            {row.vintageName ?? MISSING_DATA}
          </Typography>
        </TableCell>
        <TableCell align="left">
          <Typography variant="body2" component="div" sx={itemStyle}>
            {row.buffer !== null ? `${percentageFormat(row?.buffer ?? "")}` : MISSING_DATA}
          </Typography>
        </TableCell>
        <TableCell align="left">
          <Typography variant="body2" component="div" sx={itemStyle}>
            {row?.bufferCategory ?? MISSING_DATA}
          </Typography>
        </TableCell>
        <TableCell align="left">
          {currentlyEditing ? (
            <NumericFormat
              size="small"
              defaultValue={row.quantity}
              decimalScale={0}
              inputProps={{ maxLength: 20, style: { fontSize: 14 } }}
              allowNegative={false}
              customInput={TextField}
              type="text"
              thousandSeparator={","}
              value={quantity.value}
              onChange={(e) => currencyRowOnChangeHandler(e)}
              helperText={quantity.message}
              error={quantity.error}
            />
          ) : (
            <Typography variant="body2" component="div" sx={itemStyle}>
              {integerFormat(row.quantity)}
            </Typography>
          )}
        </TableCell>
        <TableCell align="left">
          <Typography variant="body2" component="div" sx={itemStyle}>
            {row.averageCostBasis ? currencyFormat(+row.averageCostBasis, 4) : MISSING_DATA}
          </Typography>
        </TableCell>
        <TableCell align="left">
          <Typography variant="body2" component="div" sx={itemStyle}>
            {row.latestTraderPrice ? currencyFormat(+row.latestTraderPrice, 2) : MISSING_DATA}
          </Typography>
        </TableCell>
        <TableCell align="left">
          <Typography variant="body2" component="div" sx={itemStyle}>
            {row?.portfolioAllocation ?? MISSING_DATA}
          </Typography>
        </TableCell>
        <TableCell>
          {currentlyEditing ? (
            <Box sx={{ width: "70px" }}>
              <IconButton
                sx={{ color: COLORS.rubiconGreen }}
                disabled={quantity.error}
                edge="start"
                onClick={stopEditingHandler}
              >
                <CheckIcon />
              </IconButton>
              <IconButton sx={{ marginLeft: "1px", color: COLORS.red }} edge="start" onClick={cancelEditHandler}>
                <CancelIcon />
              </IconButton>
            </Box>
          ) : (
            <Tooltip title={allowActions ? "" : MISSING_PERMISSIONS}>
              <span>
                <IconButton
                  disabled={!allowActions}
                  sx={{ color: COLORS.rubiconGreen }}
                  edge="start"
                  onClick={editRowHandler.bind(this, idx)}
                >
                  <CreateIcon />
                </IconButton>
              </span>
            </Tooltip>
          )}
        </TableCell>
        <TableCell>
          <Tooltip title={allowActions ? "" : MISSING_PERMISSIONS}>
            <span>
              <IconButton
                disabled={!allowActions}
                sx={{ color: COLORS.rubiconGreen }}
                edge="start"
                onClick={deleteHandler.bind(this, row.id)}
              >
                <DeleteIcon />
              </IconButton>
            </span>
          </Tooltip>
        </TableCell>
        <TableCell>
          <Link href={`/projects/${row.projectId}`}>
            <Tooltip title="View Details" enterDelay={750} enterNextDelay={250}>
              <IconButton aria-label="project-info" size="small" sx={{ color: COLORS.rubiconGreen }}>
                <DetailsIcon />
              </IconButton>
            </Tooltip>
          </Link>
        </TableCell>
      </TableRow>
    </React.Fragment>
  );
}

interface BasketCompositionTableProps {
  rows: ProjectVintage[];
  basketId: uuid;
  allowedProjectTypes: ProjectTypeResponse[];
  onProjectedAllocationChanged: (projectedData: ProjectVintage[]) => void;
  onVintagesReset: () => void;
  onRefresh: () => Promise<AdminBookResponse>;
}

export default function BasketCompositionTable(props: BasketCompositionTableProps): JSX.Element {
  const { rows, allowedProjectTypes, basketId, onProjectedAllocationChanged, onVintagesReset, onRefresh } = props;
  const { api } = useContext(AxiosContext);
  const [vintages, setVintages] = useState<ProjectVintageTableRow[]>([]);
  const [editIdx, setEditIdx] = useState<Undefinable<uuid>>();
  const [showNewRow, setShowNewRow] = useState<boolean>(false);
  const [availableVintages, setAvailableVintages] = useState<ProjectVintage[]>();
  const [isConfirmationDialogOpen, setIsConfirmationDialogOpen] = useState<boolean>(false);
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const [compositionList, setCompositionList] = useState<BasketCompositionVintage[]>([]);
  const [enrichedProjects, setEnrichedProjects] = useState<CustomTrimmedProjectResponse[]>();
  const [filteredProjects, setFilteredProjects] = useState<CustomTrimmedProjectResponse[]>();
  const { user: loginUser } = useAuth();
  const { logger } = useLogger();
  const [vintageQuantity, setVintageQuantity] = useState<IQuantity>({
    value: 0,
    error: false,
    message: "",
  });

  const [selectedProject, setSelectedProject] = useState<IProject>({
    error: false,
    message: "please select a project",
  });

  const [selectedCountry, setSelectedCountry] = useState<IProject>({
    error: false,
    message: "",
  });

  const [selectedRegistryId, setSelectedRegistryId] = useState<IProject>({
    error: false,
    message: "",
  });

  const [selectedType, setSelectedType] = useState<IProject>({
    error: false,
    message: "",
  });

  const [selectedVintage, setSelectedVintage] = useState<IVintage>({
    error: false,
    message: "",
  });

  function alphabeticalThenNumberSort(a: { x: string; y: number }, b: { x: string; y: number }): number {
    let compareVal = a.x.localeCompare(b.x);
    if (compareVal === 0) {
      compareVal = b.y - a.y;
    }
    return compareVal;
  }

  const { data: defaultPortfolio } = useRequest<AdminBookResponse>({
    url: `/admin/books/${process.env.NEXT_PUBLIC_ADMIN_PORTFOLIO_DEFAULT}`,
    queryParams: {
      includeRelations: [BookRelations.OWNER_ALLOCATIONS_NESTED, BookRelations.OWNER_ALLOCATIONS_BY_PROJECT_TYPE],
    },
    swrOptions: {
      onError: (error: any) => {
        enqueueError("Unable to load portfolio.");
        logger.error(
          `Unable to load projects for portfolio ${process.env.NEXT_PUBLIC_ADMIN_PORTFOLIO_DEFAULT}: ${error?.message}.`,
          {},
        );
      },
    },
  });

  const allowedTypeIds = useMemo(() => allowedProjectTypes?.map(({ id }) => id) || [], [allowedProjectTypes]);

  //Filtering out projects that are not in the allowed types
  const selectableProjects: CustomTrimmedProjectResponse[] = useMemo(() => {
    const projectsList = getProjectsList(defaultPortfolio);
    return projectsList?.length > 0
      ? projectsList.filter(({ projectType }) => allowedTypeIds.includes(projectType.id))
      : [];
  }, [allowedTypeIds, defaultPortfolio]);

  const selectableProjectIds = useMemo(
    () => (!isEmpty(selectableProjects) ? selectableProjects.map((p) => p.id) : []),
    [selectableProjects],
  );

  const { data: selectableProjectsQueryResponse } = useSWR<AdminProjectQueryResponse>(
    !isEmpty(selectableProjectIds)
      ? `/admin/projects?ids=${selectableProjectIds.join(",")}&includeRelations=${ProjectRelations.BUFFER_CATEGORY}`
      : null,
  );

  const getAvailableCountries = (projectList?: CustomTrimmedProjectResponse[]): CustomTrimmedProjectResponse[] => {
    let uniqueCountries: CustomTrimmedProjectResponse[] = [];

    if (projectList) {
      uniqueCountries = projectList
        .filter((obj, index) => {
          return index === projectList.findIndex((o) => obj?.country?.name === o?.country?.name);
        })
        .sort((a, b) => (a?.country?.name ?? "").localeCompare(b?.country?.name ?? ""));
    }

    return uniqueCountries;
  };

  const getAvailableTypes = (projectList?: CustomTrimmedProjectResponse[]): CustomTrimmedProjectResponse[] => {
    let uniqueTypes: CustomTrimmedProjectResponse[] = [];

    if (projectList) {
      uniqueTypes = projectList
        .filter((obj, index) => {
          return (
            allowedTypeIds.includes(obj.projectType.id) &&
            index === projectList.findIndex((o) => obj?.projectType?.id === o?.projectType?.id)
          );
        })
        .sort((a, b) => (a?.projectType?.type ?? "").localeCompare(b?.projectType?.type ?? ""));
    }

    return uniqueTypes;
  };

  useEffect(() => {
    if (rows) {
      setVintages(rows);
    }
  }, [rows]);

  useEffect(() => {
    if (selectableProjects && !isEmpty(selectableProjectsQueryResponse?.data)) {
      const projectsWithBufferCat = selectableProjects
        .map((p) => {
          return { ...p, bufferCategoryName: getBufferCategory(p.id, selectableProjectsQueryResponse?.data) };
        })
        .sort((a, b) => a.name.localeCompare(b.name));

      setEnrichedProjects(projectsWithBufferCat);
      setFilteredProjects(projectsWithBufferCat);
    }
  }, [selectableProjects, selectableProjectsQueryResponse?.data]);

  useEffect(() => {
    if (vintages) {
      vintages.sort((a, b) =>
        alphabeticalThenNumberSort({ x: a.projectName, y: +a.vintageName }, { x: b.projectName, y: +b.vintageName }),
      );
    }
  }, [vintages]);

  const getAvailableVintages = useCallback(
    (id: uuid) => {
      api
        .get<AdminProjectVintageQueryResponse>(
          `admin/project-vintages?includeRelations=${ProjectVintageRelations.PROJECT}&includeRelations=${ProjectVintageRelations.ASSET_ALLOCATIONS}&includeRelations=${ProjectVintageRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE}&projectIds=${id}`,
        )
        .then(async (response) => {
          const vintagesList = response?.data?.data.filter((el) => !vintages?.some((v) => v.id === el.id));
          const sortedVintagesList = vintagesList.sort((a, b) => (+a.name < +b.name ? -1 : +a.name > +b.name ? 1 : 0));
          const responseTrader = await api.post<VintagePricingResponse[]>(
            `reporting/vintage-pricing?source=rubicon_trader`,
            { vintage_ids: sortedVintagesList.map((x) => x.id) },
          );
          const responsePrice = await api.post<VintagePricingResponse[]>(`reporting/vintage-pricing?source=viridios`, {
            vintage_ids: sortedVintagesList.map((x) => x.id),
          });

          setAvailableVintages(
            sortedVintagesList.map((pv: AdminProjectVintageResponse) => {
              const portfolioDefaultAllocations = pv?.assetAllocationsByBookType?.find(
                (f) => f.bookType === BookType.PORTFOLIO_DEFAULT,
              );
              return {
                ...pv,
                latestTraderPrice: responseTrader.data.find((y) => y.vintage_id == pv.id)?.price,
                latestPrice: responsePrice.data.find((y) => y.vintage_id == pv.id)?.price,
                projectName: pv.project?.name ?? "",
                vintageName: pv.name,
                basketChartDisplayGroup: "",
                projectId: pv.project?.id as uuid,
                quantity: 0,
                buffer: isNothing(pv.riskBufferPercentage) ? undefined : +pv.riskBufferPercentage!,
                amountAvailable:
                  (portfolioDefaultAllocations?.totalAmountAllocated ?? 0) -
                  (portfolioDefaultAllocations?.totalAmountPendingSell ?? 0),
                // todo : this should be the book's amount available + the portfolio default's amount available
                // amountAvailable: (x?.amountAllocatedPortfolioDefault ?? 0) - (x?.amountPendingSell ?? 0),
              };
            }),
          );
        })
        .catch((e) => {
          logger.error(`Failed to retrieve vintages: ${e?.message}`, {});
          enqueueError("Failed to retrieve vintages.");
        });
    },
    [api, enqueueError, vintages, logger],
  );

  useEffect(() => {
    if (selectedProject.value) {
      getAvailableVintages(selectedProject.value.id);
    }
  }, [selectedProject, getAvailableVintages]);

  useEffect(() => {
    const updates = updateChanges(rows, vintages);
    setCompositionList(updates);
  }, [rows, vintages]);

  const updateChanges = (rowsArray: ProjectVintage[], vintagesArray: ProjectVintage[]): BasketCompositionVintage[] => {
    const updatesList: BasketCompositionVintage[] = [];

    rowsArray?.forEach((row) => {
      const vintage = vintagesArray?.find((v) => v.id === row.id);
      let quantity = 0;

      if (!vintage) {
        updatesList.push({
          id: row.id,
          quantity: 0,
          name: `${row.projectName} - ${row.vintageName}`,
          originalQuantity: row.quantity,
        });
      } else {
        if (vintage.quantity !== row.quantity) {
          quantity = vintage.quantity;
          updatesList.push({
            id: row.id,
            quantity,
            name: `${row.projectName} - ${row.vintageName}`,
            originalQuantity: row.quantity,
          });
        }
      }
    });

    vintagesArray?.forEach((vintage) => {
      const row = rowsArray?.find((r) => r.id === vintage.id);
      if (!row) {
        updatesList.push({
          id: vintage.id,
          quantity: vintage.quantity,
          name: `${vintage.projectName} - ${vintage.vintageName}`,
          originalQuantity: 0,
        });
      }
    });

    return updatesList;
  };

  const onSearchChangeHandler = (value: string): void => {
    getFilteredData(value);
  };

  const getFilteredData = (input: string): void => {
    const searchString = input.toUpperCase();
    const allVintages = vintages.map((vintage) => {
      if (isRowContainsValue(vintage, searchString)) {
        vintage.isHide = false;
      } else {
        vintage.isHide = true;
      }
      return vintage;
    });

    setVintages(allVintages);
  };

  const startEditingHandler = (idx: uuid): void => {
    setEditIdx(idx);
  };

  const stopEditingHandler = (): void => {
    setEditIdx(undefined);
  };

  const deleteHandler = (selectedVintage: uuid): void => {
    const filteredVintages = vintages.filter((vintage) => vintage.id !== selectedVintage);
    setVintages(filteredVintages);
    onProjectedAllocationChanged(filteredVintages);
  };

  const resetAvailableProjectNames = (
    type?: CustomTrimmedProjectResponse,
    country?: CustomTrimmedProjectResponse,
  ): void => {
    let resetProjectNames = enrichedProjects;

    if (country) {
      resetProjectNames = resetProjectNames?.filter((obj) => obj?.country?.name === country?.country?.name);
    }

    if (type) {
      resetProjectNames = resetProjectNames?.filter((obj) => obj.projectType.id === type.projectType.id);
    }

    setFilteredProjects(resetProjectNames);

    if (resetProjectNames?.length === 1) {
      setSelectedProject({
        value: resetProjectNames[0],
        error: false,
        message: "",
      });
      setSelectedRegistryId({
        value: resetProjectNames[0],
        error: false,
        message: "",
      });

      if (!type) {
        setSelectedType({
          value: resetProjectNames[0],
          error: false,
          message: "",
        });
      }

      if (!country) {
        setSelectedCountry({
          value: resetProjectNames[0],
          error: false,
          message: "",
        });
      }
    }
  };

  const resetHandler = (): void => {
    setEditIdx(undefined);
    setVintages(rows);
    setShowNewRow(false);
    onVintagesReset();
    resetNewVintageFields();
  };

  const addVintageHandler = (): void => {
    setShowNewRow(true);
  };

  const currencyOnChangeHandler = (value: number, id: uuid): void => {
    const updatedRows = vintages.map((vintage) => {
      return vintage.id === id ? { ...vintage, quantity: value } : vintage;
    });
    setVintages(updatedRows);
    onProjectedAllocationChanged(updatedRows);
  };

  const projectsDefaultProps = {
    options: filteredProjects ?? [],
    getOptionLabel: (option: CustomTrimmedProjectResponse): string => (option?.name ? option.name : ""),
    isOptionEqualToValue: (option: CustomTrimmedProjectResponse, value: CustomTrimmedProjectResponse): boolean =>
      option.id === value.id,
  };

  const projectRegistryIdDefaultProps = {
    options: filteredProjects ?? [],
    getOptionLabel: (option: CustomTrimmedProjectResponse): string =>
      option?.registryProjectId ? option.registryProjectId : "",
    isOptionEqualToValue: (option: CustomTrimmedProjectResponse, value: CustomTrimmedProjectResponse): boolean =>
      option.id === value.id,
  };

  const projectTypesDefaultProps = {
    options: getAvailableTypes(filteredProjects),
    getOptionLabel: (option: CustomTrimmedProjectResponse): string =>
      option?.projectType?.type ? option.projectType.type : "",
    isOptionEqualToValue: (option: CustomTrimmedProjectResponse, value: CustomTrimmedProjectResponse): boolean =>
      option?.projectType?.id === value?.projectType?.id,
  };

  const countriesDefaultProps = {
    options: getAvailableCountries(filteredProjects),
    getOptionLabel: (option: CustomTrimmedProjectResponse): string => (option?.country?.name ? option.country?.name : ""),
    isOptionEqualToValue: (option: CustomTrimmedProjectResponse, value: CustomTrimmedProjectResponse): boolean =>
      option?.country?.name === value?.country?.name,
  };

  const vintagesDefaultProps = {
    options: availableVintages ?? [],
    getOptionLabel: (option: ProjectVintage): string => (option?.vintageName ? option.vintageName : ""),
    isOptionEqualToValue: (option: ProjectVintage, value: ProjectVintage): boolean => option.id === value.id,
  };

  const projectSelectionHandler = (
    event: React.ChangeEvent<HTMLInputElement>,
    newValue: Undefinable<CustomTrimmedProjectResponse>,
  ): void => {
    setVintageQuantity({
      value: 0,
      error: false,
      message: "",
    });

    setSelectedVintage({
      error: false,
      message: "",
    });

    setSelectedCountry({
      value: newValue,
      error: false,
      message: "",
    });

    setSelectedType({
      value: newValue,
      error: false,
      message: "",
    });

    setSelectedRegistryId({
      value: newValue,
      error: false,
      message: newValue ? "" : "please select a project",
    });

    setSelectedProject({
      value: newValue,
      error: false,
      message: newValue ? "" : "please select a project",
    });

    resetAvailableProjectNames(newValue, newValue);
  };

  const countrySelectionHandler = (
    event: React.ChangeEvent<HTMLInputElement>,
    newValue: Undefinable<CustomTrimmedProjectResponse>,
  ): void => {
    setSelectedCountry({
      value: newValue,
      error: false,
      message: "",
    });

    setSelectedVintage({
      error: false,
      message: "",
    });

    setSelectedRegistryId({
      error: false,
      message: "please select a project",
    });

    setSelectedProject({
      error: false,
      message: "please select a project",
    });

    setVintageQuantity({
      value: 0,
      error: false,
      message: "",
    });

    resetAvailableProjectNames(selectedType.value, newValue);
  };

  const projectTypeSlectionHandler = (
    event: React.ChangeEvent<HTMLInputElement>,
    newValue: Undefinable<CustomTrimmedProjectResponse>,
  ): void => {
    setSelectedType({
      value: newValue,
      error: false,
      message: "",
    });

    setSelectedVintage({
      error: false,
      message: "",
    });

    setSelectedRegistryId({
      error: false,
      message: "please select a project",
    });

    setSelectedProject({
      error: false,
      message: "please select a project",
    });

    setVintageQuantity({
      value: 0,
      error: false,
      message: "",
    });

    resetAvailableProjectNames(newValue, selectedCountry.value);
  };

  const vintageSelectionHandler = (
    event: React.ChangeEvent<HTMLInputElement>,
    newValue: Undefinable<ProjectVintage>,
  ): void => {
    setSelectedVintage({
      value: newValue,
      error: false,
      message: "",
    });

    setVintageQuantity({
      value: 0,
      error: false,
      message: newValue ? `${integerFormat(newValue?.amountAvailable)} available` : "",
    });
  };

  const onSubmit = (): void => {
    setIsConfirmationDialogOpen(true);
  };

  const newVintageQuantityHandler = (newQuantity: string): void => {
    if (selectedVintage.value) {
      const value = convertAmountToNumber(newQuantity);
      const validatedQuantity = validateQuantity(value, selectedVintage.value.amountAvailable!);

      setVintageQuantity(validatedQuantity);
    }
  };

  const deleteNewVintageHandler = (): void => {
    resetNewVintageFields();
  };

  const isNewVintageValid = (): boolean => {
    if (selectedProject.value === null) {
      setSelectedProject({
        ...selectedProject,
        error: true,
        message: "please select a project",
      });

      return false;
    }

    if (selectedVintage.value === null) {
      setSelectedVintage({
        ...selectedVintage,
        error: true,
        message: "please select a vintage",
      });

      return false;
    }

    const validatedQuantity = validateQuantity(vintageQuantity?.value ?? 0, selectedVintage!.value!.amountUnallocated!);

    if (validatedQuantity.error) {
      setVintageQuantity(validatedQuantity);
      return false;
    }

    return true;
  };

  const addNewVintageHandler = async (): Promise<void> => {
    if (!isNewVintageValid()) return;

    const newProjectVintage: ProjectVintage = {
      id: selectedVintage?.value?.id as uuid,
      projectName: selectedProject?.value?.name ?? "",
      projectId: selectedProject?.value?.id as uuid,
      projectRegistryId: selectedProject?.value?.registryProjectId,
      vintageName: selectedVintage?.value?.vintageName ?? "",
      quantity: vintageQuantity?.value ?? 0,
      // todo (purchases) book issue fix
      // basketChartDisplayGroup: selectedProject.value?.basketChartDisplayGroup,
      basketChartDisplayGroup: "",
      // todo (purchases) book issue fix
      amountAvailable: selectedVintage?.value?.amountAvailable ?? 0,
      buffer: !isNil(selectedVintage?.value?.buffer) ? +selectedVintage.value.buffer : undefined,
      bufferCategory: selectedProject.value?.bufferCategoryName,
      projectLocation: selectedProject.value?.country?.name,
      projectType: selectedProject.value?.projectType?.type,
      averageCostBasis: selectedVintage.value?.averageCostBasis,
      latestTraderPrice: selectedVintage.value?.latestTraderPrice,
      latestPrice: selectedVintage.value?.latestPrice,
      rctStandard: selectedProject.value?.rctStandard,
      suspended: selectedProject.value?.suspended,
    };

    const newVintages = vintages ? [...vintages] : [];
    newVintages.push(newProjectVintage);
    setVintages(newVintages);
    onProjectedAllocationChanged(newVintages);
    resetNewVintageFields();
  };

  const resetNewVintageFields = (): void => {
    setSelectedVintage({
      error: false,
      message: "",
    });

    setVintageQuantity({
      value: 0,
      error: false,
      message: "",
    });

    setSelectedCountry({
      error: false,
      message: "",
    });

    setSelectedType({
      error: false,
      message: "",
    });

    setSelectedProject({
      error: false,
      message: "please select a project",
    });

    setSelectedRegistryId({
      error: false,
      message: "please select a project",
    });

    setFilteredProjects(enrichedProjects);

    setShowNewRow(false);
  };

  const allocationEligibility = useMemo(
    () =>
      !!selectedVintage?.value === null || selectedVintage?.value === undefined
        ? null
        : projectIsRCTEligible({
            riskBufferPercentage: selectedVintage?.value?.buffer as any,
            project: {
              suspended: selectedRegistryId?.value?.suspended,
              rctStandard: selectedRegistryId?.value?.rctStandard,
              isScienceTeamApproved: selectedRegistryId?.value?.isScienceTeamApproved,
            },
          } as any),
    [
      selectedVintage?.value,
      selectedRegistryId?.value?.rctStandard,
      selectedRegistryId?.value?.suspended,
      selectedRegistryId?.value?.isScienceTeamApproved,
    ],
  );

  const allowActions: boolean = useMemo(
    () => !!loginUser?.hasPermission(PermissionEnum.TRANSFERS_EXECUTE),
    [loginUser],
  );

  const refreshPage = (): void => {
    onRefresh();
  };

  function closeConfirmationHandler(): void {
    setIsConfirmationDialogOpen(false);
  }

  function onSuccessHandler(message: string): void {
    setCompositionList([]);
    enqueueSuccess(message);
  }

  const columns = [
    "",
    "Registry ID",
    "Project Name",
    "Project Type",
    "Country",
    "Vintage",
    "Buffer",
    "Buffer Category",
    "Quantity",
    "Cost Basis",
    "MTM",
    "Portfolio Allocation",
  ];

  interface IExportFormatter {
    func: (input: string | number | Decimal | Date | boolean | Map<string, string | number | Date>) => any;
    inputFields?: string[];
  }

  interface ExportColDef {
    name: string;
    displayName: string;
    formatter?: IExportFormatter;
  }

  const exportCols: ExportColDef[] = [
    {
      name: "projectRegistryId",
      displayName: "Registry ID",
    },
    {
      name: "projectName",
      displayName: "Project Name",
    },
    {
      name: "suspended",
      displayName: "Suspended",
      formatter: {
        func: (input: boolean) => (input === true ? "Yes" : "No"),
      },
    },
    {
      name: "rctStandard",
      displayName: "RCT Standard",
      formatter: {
        func: (input: boolean) => (input === true ? "Yes" : "No"),
      },
    },
    {
      name: "rctEligible",
      displayName: "RCT Eligible",
      formatter: {
        func: isEligibleFormatter,
        inputFields: ["suspended", "rctStandard", "buffer", "isScienceTeamApproved"],
      },
    },
    {
      name: "projectType",
      displayName: "Project Type",
    },
    {
      name: "projectLocation",
      displayName: "Country",
    },
    {
      name: "vintageName",
      displayName: "Vintage",
    },
    {
      name: "buffer",
      displayName: "Buffer",
      formatter: {
        func: (input: Decimal) => (input !== null && isFinite(+input) ? `${decimalFormat(+input)}%` : "N/A"),
      },
    },
    {
      name: "bufferCategory",
      displayName: "Buffer Category",
    },
    {
      name: "quantity",
      displayName: "Quantity",
      formatter: { func: integerFormat as any },
    },
    {
      name: "averageCostBasis",
      displayName: "Cost Basis",
      formatter: {
        func: (input: Decimal) => (input ? "$" + input.toFixed(4, Decimal.ROUND_HALF_UP).toString() : MISSING_DATA),
      },
    },
    {
      name: "latestTraderPrice",
      displayName: "MTM",
      formatter: {
        func: (input: Decimal) => (input ? "$" + input.toFixed(2, Decimal.ROUND_HALF_UP).toString() : MISSING_DATA),
      },
    },
    {
      name: "portfolioAllocation",
      displayName: "Portfolio Allocation",
    },
  ];

  const getFormatterInputFields = (
    inputFields: string[],
    row: ProjectVintage,
    colDef: ExportColDef,
  ): Map<string, string | number | Date> | string => {
    if (!inputFields) return row[colDef.name as keyof ProjectVintage] as string;

    const map = new Map<string, string | number | Date>();
    inputFields.forEach((element) => {
      const value = row[element as keyof ProjectVintage];
      if (typeof value === 'string' || typeof value === 'number' || value instanceof Date) {
        map.set(element, value);
      }
    });

    return map;
  };

  const formatRowValue = (row: ProjectVintage, exportColDef: ExportColDef): string => {
    if (row[exportColDef.name as keyof ProjectVintage] === null) return MISSING_DATA;

    if (exportColDef?.formatter) {
      let formatterInputFields: ReturnType<typeof getFormatterInputFields>;
      if (exportColDef?.formatter?.inputFields) {
        formatterInputFields = getFormatterInputFields(exportColDef.formatter.inputFields, row, exportColDef);
      } else {
        formatterInputFields = row[exportColDef.name as keyof ProjectVintage];
      }
      return exportColDef.formatter.func(formatterInputFields);
    }
    return row[exportColDef.name as keyof ProjectVintage] as string;
  };

  const exportHandler = (): void => {
    if (vintages) {
      const columns = exportCols?.map((colDef) => colDef.displayName);
      const rows = vintages.map((row) => exportCols.map((colDef) => formatRowValue(row, colDef)));
      const csvBuilder = new CsvBuilder("export_composition.csv");
      csvBuilder.setColumns(columns).addRows(rows).exportFile();
    }
  };

  return (
    <TableBox>
      <Paper sx={{ width: "100%", overflow: "hidden" }}>
        <SearchAppBar onChangeHandler={onSearchChangeHandler}>
          <Stack direction="row" gap="var(--action-button-gap)">
            <ActionButton
              isDisabled={isEmpty(compositionList)}
              onClickHandler={onSubmit}
              requiredPermission={PermissionEnum.TRANSFERS_EXECUTE} // update rct composition is the same as a transfer execute request, permission is the same
            >
              Save
            </ActionButton>
            <ActionButton
              onClickHandler={addVintageHandler}
              startIcon={<AddIcon />}
              requiredPermission={PermissionEnum.TRANSFERS_EXECUTE}
              style={{ width: "165px" }}
            >
              Add Vintage
            </ActionButton>
            <ActionButton
              onClickHandler={resetHandler}
              startIcon={<UndoIcon />}
              requiredPermission={PermissionEnum.TRANSFERS_EXECUTE}
            >
              RESET
            </ActionButton>
            <Tooltip title="Export table data to csv file">
              <Box>
                <ActionButton
                  onClickHandler={exportHandler}
                  startIcon={<FileDownloadIcon />}
                  tooltip="Export table data to csv file"
                  style={{
                    borderColor: "rgba(0, 0, 0, 0.23)",
                    color: "rgba(0, 0, 0, 0.54)",
                    backgroundColor: "rgba(255, 255, 255, 1)",
                    ontWeight: 600,
                    "&:hover": {
                      backgroundColor: "rgba(255, 255, 255, 1)",
                      boxShadow: "rgb(0 0 0 / 10%) 0px 4px 4px",
                    },
                  }}
                >
                  Export
                </ActionButton>
              </Box>
            </Tooltip>
          </Stack>
        </SearchAppBar>
        <TableContainer sx={{ maxHeight: 800 }}>
          <Table aria-label="composition table" stickyHeader>
            <TableHead sx={{ backgroundColor: COLORS.tableHeader }}>
              <TableRow>
                {columns?.map((column, idx) => (
                  <TableCell key={`${column}-${idx}`}>
                    <Typography variant="body2" component="h4" fontWeight="700">
                      {column}
                    </Typography>
                  </TableCell>
                ))}
                <TableCell />
                <TableCell />
                <TableCell />
              </TableRow>
            </TableHead>
            <TableBody>
              <Maybe condition={showNewRow}>
                <TableRow sx={newRowStyle}>
                  <TableCell align="left">
                    <Typography variant="body2" component="div" sx={itemStyle}>
                      {allocationEligibility === null ? (
                        MISSING_DATA
                      ) : (
                        <RCTEligibilityChip
                          vintage={
                            {
                              riskBufferPercentage: selectedVintage?.value?.buffer as any,
                              project: omitFromRecord(selectedVintage?.value ?? { buffer: null }, ["buffer"]) as any,
                            } as any
                          }
                        />
                      )}
                    </Typography>
                  </TableCell>
                  <TableCell align="left">
                    <Autocomplete
                      {...projectRegistryIdDefaultProps}
                      onChange={projectSelectionHandler}
                      value={selectedRegistryId.value ?? null}
                      loading
                      loadingText="Add eligible projects"
                      disablePortal
                      id="registryId"
                      renderInput={(params) => (
                        <TextField
                          error={selectedRegistryId.error}
                          helperText={selectedRegistryId.message}
                          required
                          {...params}
                          size="small"
                          InputProps={{
                            ...params.InputProps,
                            style: { fontSize: 14, minWidth: "150px" },
                          }}
                        />
                      )}
                    />
                  </TableCell>
                  <TableCell align="left">
                    <Autocomplete
                      {...projectsDefaultProps}
                      onChange={projectSelectionHandler}
                      value={selectedProject.value ?? null}
                      loading
                      loadingText="Add eligible projects"
                      disablePortal
                      id="project"
                      renderInput={(params) => (
                        <TextField
                          error={selectedProject.error}
                          helperText={selectedProject.message}
                          required
                          {...params}
                          size="small"
                          InputProps={{
                            ...params.InputProps,
                            style: { fontSize: 14, minWidth: "150px" },
                          }}
                        />
                      )}
                    />
                  </TableCell>
                  <TableCell align="left">
                    <Autocomplete
                      {...projectTypesDefaultProps}
                      onChange={projectTypeSlectionHandler}
                      value={selectedType.value ?? null}
                      loading
                      loadingText="Add eligible credit types"
                      disablePortal
                      id="type"
                      renderInput={(params) => (
                        <TextField
                          error={selectedType.error}
                          helperText={selectedType.message}
                          required
                          {...params}
                          size="small"
                          InputProps={{
                            ...params.InputProps,
                            style: { fontSize: 14, minWidth: "150px" },
                          }}
                        />
                      )}
                    />
                  </TableCell>
                  <TableCell align="left">
                    <Autocomplete
                      {...countriesDefaultProps}
                      onChange={countrySelectionHandler}
                      value={selectedCountry.value ?? null}
                      loading
                      disablePortal
                      id="country"
                      renderInput={(params) => (
                        <TextField
                          error={selectedCountry.error}
                          helperText={selectedCountry.message}
                          required
                          {...params}
                          size="small"
                          InputProps={{
                            ...params.InputProps,
                            style: { fontSize: 14, minWidth: "150px" },
                          }}
                        />
                      )}
                    />
                  </TableCell>
                  <TableCell align="left">
                    <Autocomplete
                      {...vintagesDefaultProps}
                      disabled={isNil(selectedProject.value)}
                      onChange={vintageSelectionHandler}
                      value={selectedVintage.value ?? null}
                      disablePortal
                      id="vintage"
                      renderInput={(params) => (
                        <TextField
                          error={selectedVintage.error}
                          helperText={selectedVintage.message}
                          required
                          {...params}
                          size="small"
                          InputProps={{
                            ...params.InputProps,
                            style: { fontSize: 14, width: "120px" },
                          }}
                        />
                      )}
                    />
                  </TableCell>
                  <TableCell align="left">
                    <Typography variant="body2" component="div" sx={itemStyle}>
                      {isNil(selectedVintage.value?.buffer)
                        ? MISSING_DATA
                        : selectedVintage.value?.buffer.toFixed(2).toString().concat("%")}
                    </Typography>
                  </TableCell>
                  <TableCell align="left">
                    <Typography variant="body2" component="div" sx={itemStyle}>
                      {isNil(selectedProject.value?.bufferCategoryName)
                        ? MISSING_DATA
                        : selectedProject.value?.bufferCategoryName}
                    </Typography>
                  </TableCell>
                  <TableCell align="left">
                    <NumericFormat
                      disabled={isNil(selectedVintage.value)}
                      size="small"
                      defaultValue={0}
                      value={vintageQuantity.value}
                      decimalScale={0}
                      inputProps={{ maxLength: 20, style: { fontSize: 14 } }}
                      allowNegative={false}
                      customInput={TextField}
                      type="text"
                      thousandSeparator={","}
                      helperText={vintageQuantity.message}
                      error={vintageQuantity.error}
                      onChange={(e) => newVintageQuantityHandler(e.target.value)}
                    />
                  </TableCell>
                  <TableCell align="left">
                    <Typography variant="body2" component="div" sx={itemStyle}>
                      {selectedVintage?.value?.averageCostBasis
                        ? currencyFormat(+selectedVintage.value.averageCostBasis, 4)
                        : MISSING_DATA}
                    </Typography>
                  </TableCell>
                  <TableCell align="left">
                    <Typography variant="body2" component="div" sx={itemStyle}>
                      {selectedVintage?.value?.latestTraderPrice
                        ? currencyFormat(+selectedVintage.value.latestTraderPrice, 2)
                        : MISSING_DATA}
                    </Typography>
                  </TableCell>
                  <TableCell align="left">
                    <Typography variant="body2" component="div" sx={itemStyle}>
                      {MISSING_DATA}
                    </Typography>
                  </TableCell>
                  <TableCell align="left">
                    <IconButton
                      sx={{ color: COLORS.rubiconGreen }}
                      edge="start"
                      onClick={addNewVintageHandler}
                      disabled={vintageQuantity.error}
                    >
                      <CheckIcon />
                    </IconButton>
                  </TableCell>
                  <TableCell align="left">
                    <IconButton sx={{ color: COLORS.rubiconGreen }} edge="start" onClick={deleteNewVintageHandler}>
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                  <TableCell align="left"></TableCell>
                </TableRow>
              </Maybe>
              {vintages?.map(
                (row) =>
                  !row.isHide && (
                    <Row
                      key={row.id}
                      row={row}
                      idx={row.id}
                      allowActions={allowActions}
                      editIdx={editIdx}
                      startEditing={startEditingHandler}
                      stopEditing={stopEditingHandler}
                      deleteHandler={deleteHandler}
                      currencyOnChangeHandler={currencyOnChangeHandler}
                    />
                  ),
              )}
            </TableBody>
          </Table>
        </TableContainer>
        <Maybe condition={isEmpty(vintages) && !showNewRow}>
          <Box sx={{ height: "50px" }}>
            <Typography variant="body2" component="p" sx={{ marginTop: "30px", textAlign: "center" }}>
              Click <b>ADD VINTAGE</b>
            </Typography>
          </Box>
        </Maybe>
      </Paper>
      <Box>
        <CompositionConfirmationModal
          isOpen={!!isConfirmationDialogOpen}
          basketId={basketId}
          onClose={closeConfirmationHandler}
          onConfirm={onSuccessHandler}
          onError={enqueueError}
          basketComposition={compositionList}
          refreshData={refreshPage}
        />
      </Box>
    </TableBox>
  );
}
