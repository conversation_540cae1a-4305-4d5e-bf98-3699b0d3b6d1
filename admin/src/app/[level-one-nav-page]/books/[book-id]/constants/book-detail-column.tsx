import { Maybe, Nullable, numberFormat, percentageFormat } from "@rubiconcarbon/frontend-shared";
import { Box, Grid, Stack, Typography } from "@mui/material";
import SuspendedChip from "@components/ui/suspended-chip/suspended-chip";
import { MISSING_DATA } from "@constants/constants";
import { ArrowDropDown, ArrowDropUp } from "@mui/icons-material";
import { GenericTableColumn } from "@components/ui/generic-table/types/generic-table-column";
import { TrimmedProjectVintageResponse } from "@rubiconcarbon/shared-types";
import RCTEligibilityChip from "@components/ui/rct-eligibility-chip/rct-eligibility-chip";
import { GenericTableFieldSizeEnum } from "@components/ui/generic-table/constants/generic-table-field-size.enum";
import { BookDetailModel } from "../models/book-detail";

export const BOOK_DETAIL_COLUMNS: GenericTableColumn<BookDetailModel>[] = [
  {
    field: null,
    label: "",
    exportable: false,
    width: GenericTableFieldSizeEnum.tiny,
    fixedWidth: true,
    renderDataCell: (row): JSX.Element => {
      return (
        <Maybe condition={'project' in (row?.detailedAsset || {})}>
          <Typography variant="body2" component="div" sx={{ marginTop: "2px" }}>
            <RCTEligibilityChip vintage={row?.detailedAsset as any} />
          </Typography>
        </Maybe>
      );
    },
  },
  {
    field: "allocation",
    label: "Allocation",
    width: GenericTableFieldSizeEnum.medium,
    maxWidth: GenericTableFieldSizeEnum.xxlarge,
    hide: (rows) => !rows?.some(({ allocation }) => !!allocation),
  },
  {
    field: "detailedAsset.project.name",
    label: "Name",
    hide: true,
    exportable: true,
  },
  {
    field: "detailedAsset.project.registryProjectId",
    label: "Registry ID",
    hide: true,
    exportable: true,
  },
  {
    field: "detailedAsset.name",
    label: "Vintage",
    hide: true,
    exportable: true,
  },
  {
    field: "detailedAsset",
    label: "Project",
    exportable: false,
    width: GenericTableFieldSizeEnum.medium,
    maxWidth: GenericTableFieldSizeEnum.large,
    transformDataValue: (value: TrimmedProjectVintageResponse) =>
      `${value?.name} - ${value?.project?.registryProjectId}`,
    renderDataCell: (row): JSX.Element => {
      return (
        <Stack gap={1}>
          <Typography fontSize={14} fontWeight={300}>
            {(row?.detailedAsset as TrimmedProjectVintageResponse)?.project?.name}
          </Typography>
          <Typography fontSize={14} fontWeight={300} color="GrayText">
            {(row?.detailedAsset as TrimmedProjectVintageResponse)?.project?.registryProjectId} -{" "}
            {(row?.detailedAsset as TrimmedProjectVintageResponse)?.name}
          </Typography>
          <Grid container gap={1} alignItems="center">
            <Maybe condition={!!(row?.detailedAsset as TrimmedProjectVintageResponse)?.project?.suspended}>
              <SuspendedChip />
            </Maybe>
          </Grid>
        </Stack>
      );
    },
  },
  {
    field: "detailedAsset.project.suspended",
    label: "Suspended",
    collapse: true,
    transformDataValue: (value: boolean) => (value ? "Yes" : "No"),
  },
  {
    field: "detailedAsset.project.rctStandard",
    label: "RCT Standard",
    collapse: true,
    transformDataValue: (value: boolean) => (value ? "Yes" : "No"),
  },
  {
    field: "detailedAsset.project.projectType.type",
    label: "Type",
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
  },
  {
    field: "detailedAsset.project.country.name",
    label: "Country",
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
  },
  {
    field: "amountAllocated",
    label: "Holding",
    type: "number",
    headerTooltipContent: <>Quantity of credits held</>,
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
  },
  {
    field: "currentPrice",
    label: "Price",
    type: "money",
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
  },
  {
    field: "value" as any,
    label: "Value",
    type: "money",
    headerTooltipContent: <>Total value of credits (settled)</>,
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
    deriveDataValue: (row: BookDetailModel): Nullable<number> => {
      if (!!row?.amountAllocated && !!row?.currentPrice) return +row.amountAllocated * +row.currentPrice;
      return null;
    },
  },
  {
    field: "change",
    label: "Change",
    type: "money",
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
  },
  {
    field: "changePercentage",
    label: "Change %",
    type: "percentage",
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
    renderDataCell: (row): JSX.Element => {
      const percentage = row.changePercentage;
      const percentageScaled = row.changePercentage * 100;
      const isNeutral = Math.abs(percentageScaled)?.toFixed(2) === "0.00";
      const isPositive = percentageScaled > 0;

      return (
        <Stack
          direction="row"
          alignItems="center"
          gap={1}
          color={!isNeutral ? (isPositive ? "green" : "red") : "inherit"}
        >
          <Maybe condition={isNeutral}>{MISSING_DATA}</Maybe>
          <Maybe condition={!isNeutral}>
            <Box minWidth={60}>{percentageFormat(Math.abs(percentage))}</Box>{" "}
            {isPositive ? <ArrowDropUp /> : <ArrowDropDown />}
          </Maybe>
        </Stack>
      );
    },
  },
  {
    field: "detailedAsset.averageCostBasis",
    label: "Cost Basis",
    type: "money",
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
    transformDataValue: (value) => numberFormat(value, { prepend: "$", decimalPlaces: 4, fallback: MISSING_DATA }),
  },
  {
    field: "detailedAsset.riskBufferPercentage",
    label: "Buffer",
    type: "percentage",
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
  },
  {
    field: "buffer_category_name",
    label: "Buffer Category",
    width: GenericTableFieldSizeEnum.medium,
    maxWidth: GenericTableFieldSizeEnum.flexmedium,
  },
];
