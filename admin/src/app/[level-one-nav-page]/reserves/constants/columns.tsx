import { Maybe, numberFormat } from "@rubiconcarbon/frontend-shared";
import { CancelRounded, CheckRounded, DeleteRounded, ModeEditRounded } from "@mui/icons-material";
import { Box, Divider, Grid, IconButton, Link, Stack, Tooltip, Typography } from "@mui/material";
import { MouseEvent, useMemo } from "react";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import { GenericTableColumn } from "@components/ui/generic-table/types/generic-table-column";
import useAuth from "@providers/auth-provider";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import GenericTableFormErrorHelperText from "@components/ui/generic-table/components/generic-table-form-error-helper-text";
import useGenericTableRowActions from "@components/ui/generic-table/hooks/use-generic-table-row-actions";
import useGenericTableRowState from "@components/ui/generic-table/hooks/use-generic-table-row-state";
import { GenericTableFieldSizeEnum } from "@components/ui/generic-table/constants/generic-table-field-size.enum";
import COLORS from "@components/ui/theme/colors";
import SuspendedChip from "@components/ui/suspended-chip/suspended-chip";
import CustomerPortfolio from "@components/ui/customer-portfolio/customer-portfolio";
import { ReserveModel } from "../models/reserve";

type RowOnlyProps = {
  row: GenericTableRowModel<ReserveModel>;
};

const ActionsCell = ({ row }: RowOnlyProps): JSX.Element => {
  const { user } = useAuth();
  const { toggleEdit, cancelAmendment, deleteRow } = useGenericTableRowActions<ReserveModel>();
  const { creating, editing, deleting, dirty, submitting, disabled, isRowActive } =
    useGenericTableRowState<ReserveModel>(row);

  const permissions = useMemo(() => user?.permissions || [], [user?.permissions]);

  const hasPermissionToEdit = permissions.includes(PermissionEnum.RESERVES_WRITE);

  return (
    <Stack direction="row">
      <Maybe condition={!isRowActive || deleting}>
        <Tooltip title={!hasPermissionToEdit ? "Insufficient permissions" : ""}>
          <Box>
            <IconButton
              color="primary"
              disabled={!hasPermissionToEdit || disabled}
              onClick={(event: MouseEvent<HTMLButtonElement>) => {
                event?.preventDefault();
                event?.stopPropagation();
                toggleEdit(row);
              }}
            >
              <ModeEditRounded />
            </IconButton>
          </Box>
        </Tooltip>
        <Divider sx={{ height: 28.5, m: 0.5 }} orientation="vertical" />
        <Tooltip title={!hasPermissionToEdit ? "Insufficient permissions" : ""}>
          <Box>
            <IconButton
              color="primary"
              disabled={!hasPermissionToEdit || disabled}
              onClick={(event: MouseEvent<HTMLButtonElement>) => {
                event?.preventDefault();
                event?.stopPropagation();

                if (!deleting) deleteRow(row);
              }}
            >
              <DeleteRounded />
            </IconButton>
          </Box>
        </Tooltip>
      </Maybe>
      <Maybe condition={isRowActive && !deleting}>
        <IconButton
          color="primary"
          onClick={(event: MouseEvent<HTMLButtonElement>) => event.stopPropagation()}
          type="submit"
          disabled={submitting || (editing && !dirty)}
        >
          <CheckRounded />
        </IconButton>
        <Divider sx={{ height: 28.5, m: 0.5 }} orientation="vertical" />
        <IconButton
          color="error"
          onClick={(event: MouseEvent<HTMLButtonElement>) => {
            event?.preventDefault();
            event?.stopPropagation();

            if (creating) cancelAmendment();
            else toggleEdit(row);
          }}
        >
          <CancelRounded />
        </IconButton>
      </Maybe>
    </Stack>
  );
};

const MemoToolTip = ({ row }: RowOnlyProps): JSX.Element => {
  const { creating, amending } = useGenericTableRowState<ReserveModel>(row);

  return (
    <>
      <Maybe condition={amending}>Click to {creating ? "add" : "edit"} memo</Maybe>
      <Maybe condition={!amending}>Click to view memo</Maybe>
    </>
  );
};

export const COLUMNS: GenericTableColumn<ReserveModel>[] = [
  {
    field: "projectVintage.project.id",
    label: "Project",
    type: "autocomplete",
    editable: false,
    width: GenericTableFieldSizeEnum.flexmedium,
    maxWidth: GenericTableFieldSizeEnum.xlarge,
    deriveDataValue: (row) =>
      `${row?.projectVintage?.project?.registryProjectId} - ${row?.projectVintage?.project?.name}`,
    formHelperText: ({ errors }) => (
      <GenericTableFormErrorHelperText errors={[errors?.amends?.[0]?.projectVintage?.project?.id?.message]} />
    ),
    renderDataCell: (row: ReserveModel): JSX.Element => (
      <Stack direction="row" justifyContent="space-between" alignItems="center">
        <Stack gap={1}>
          <Link
            href={`/projects/${row?.projectVintage?.project?.id}`}
            style={{ color: COLORS.rubiconGreen, textUnderlineOffset: 3 }}
            onClick={(event) => event.stopPropagation()}
          >
            <Typography variant="body2" fontWeight={300}>
              {row?.projectVintage?.project?.registryProjectId} - {row?.projectVintage?.project?.name}
            </Typography>
          </Link>

          <Maybe condition={!!row?.projectVintage?.project?.suspended}>
            <Grid container gap={1} alignItems="center">
              <SuspendedChip />
            </Grid>
          </Maybe>
        </Stack>
      </Stack>
    ),
  },
  {
    field: "projectVintage.id",
    label: "Vintage",
    type: "autocomplete",
    editable: false,
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
    deriveDataValue: (row) => row?.projectVintage?.name || '',
    disable: (row) => !row?.projectVintage?.project?.id,
    formHelperText: ({ errors }) => (
      <GenericTableFormErrorHelperText errors={[errors?.amends?.[0]?.projectVintage?.id?.message]} />
    ),
  },
  {
    field: "amount",
    label: "Reserved",
    type: "number",
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
    validator: {
      max: (row) => (row?.currentEditAllocated || 0) + (row?.amountAvailable || 0),
    },
    disable: (row) => !row?.projectVintage?.id,
    formHelperText: ({ row, errors }): JSX.Element => {
      const available = (row?.currentEditAllocated || 0) + (row?.amountAvailable || 0);
      const helperText = row?.projectVintage?.id ? (
        <>
          Available - <strong>{numberFormat(available)}</strong>
        </>
      ) : null;
      const merged = [errors?.amends?.[0]?.amount?.message, helperText];

      return <GenericTableFormErrorHelperText stackable={!!helperText} errors={merged} />;
    },
  },
  {
    field: "organizationAndMemo.id",
    label: "Customer",
    type: "autocomplete",
    editable: false,
    width: GenericTableFieldSizeEnum.flexsmall,
    maxWidth: GenericTableFieldSizeEnum.flexmedium,
    deriveDataValue: (row) => row?.organizationAndMemo?.name || '',
    formHelperText: ({ errors }) => (
      <GenericTableFormErrorHelperText errors={[errors?.amends?.[0]?.organizationAndMemo?.id?.message]} />
    ),
    renderDataCell: (row: ReserveModel): JSX.Element => (
      <CustomerPortfolio
        portfolio={{ name: row?.organizationAndMemo?.name, organization: row?.organizationAndMemo } as any}
        style={{ fontSize: 14, fontWeight: 300 }}
      />
    ),
  },
  {
    field: "organizationAndMemo.memo",
    label: "Memo",
    type: "text-popper",
    width: GenericTableFieldSizeEnum.medium,
    maxWidth: GenericTableFieldSizeEnum.xxlarge,
    formHelperText: ({ errors }) => (
      <GenericTableFormErrorHelperText errors={[errors?.amends?.[0]?.organizationAndMemo?.memo?.message]} />
    ),
    tooltip: (row): JSX.Element => <MemoToolTip row={row} />,
  },
  {
    field: "updatedAt",
    label: "Last Updated",
    type: "date",
    creatable: false,
    editable: false,
    width: GenericTableFieldSizeEnum.flexsmall,
    fixedWidth: true,
    hide: (rows) => rows?.some((row) => row?.creating || row?.editing),
  },
  {
    field: "actions" as any,
    label: "Actions",
    type: "action",
    sortable: false,
    exportable: false,
    creatable: false,
    editable: false,
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
    renderDataCell: (row): JSX.Element => <ActionsCell row={row} />,
  },
];
