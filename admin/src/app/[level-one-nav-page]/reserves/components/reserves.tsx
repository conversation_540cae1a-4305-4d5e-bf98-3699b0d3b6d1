import { Stack, Typography } from "@mui/material";
import {
  BookRelations,
  AdminBookResponse,
  AllocationResponse,
  OrganizationResponse,
  PermissionEnum,
  AdminProjectVintageResponse,
  ReserveCreateRequest,
  ReserveQueryResponse,
  ReserveResponse,
  ReserveUpdateRequest,
  uuid,
  GroupedAllocationWithNestedResponse,
  ReserveRelation,
  AssetType,
  ProjectVintageRelations,
} from "@rubiconcarbon/shared-types";
import { useCallback, useContext, useEffect, useMemo, useState } from "react";
import { Maybe, px, uniqueByKeys, useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import useAutoCompleteOptions, { UseAutoCompleteOptionsReturnEntry } from "@hooks/use-auto-complete-options";
import { FileDownloadRounded } from "@mui/icons-material";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import useNavigationInterceptor from "@hooks/use-navigation-interceptor";
import { useLogger } from "@providers/logging";
import { AxiosContext } from "@providers/axios-provider";
import { useUpdateEffect } from "react-use";
import { classValidatorResolver } from "@hookform/resolvers/class-validator";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import GenericDialog from "@components/ui/generic-dialog/generic-dialog";
import GenericTable from "@components/ui/generic-table";
import { DEFAULT_EXPORT_STYLE } from "@components/ui/generic-table/constants/generic-table-styles";
import useGenericTableUtility, { Values } from "@components/ui/generic-table/hooks/use-generic-table-utility";
import { GenericTableColumn } from "@components/ui/generic-table/types/generic-table-column";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import { COLUMNS } from "../constants/columns";
import { OrganizationAndMemo, ReserveFormModel, ReserveModel, Vintage } from "../models/reserve";
import {
  amountUnallocatedByBook,
  amountAllocatedToPortfolio,
  amountUnallocatedByVintage,
  pendingSellByVintageAssetAllocation,
} from "../utilities/amount-calculations";
import ProjectVintageAllocation from "./project-vintage-allocation";

import dialogClasses from "../styles/dialog.module.scss";

const ReserveFormResolver = classValidatorResolver(ReserveFormModel);

const Reserves = ({
  reservesResponse: serverReservesResponse,
  defaultPortofolioResponse: serverDefaultPortofolioResponse,
  organizationsResponse,
}: {
  reservesResponse: ReserveQueryResponse;
  defaultPortofolioResponse: AdminBookResponse;
  organizationsResponse: OrganizationResponse[];
}): JSX.Element => {
  const { logger } = useLogger();
  const { api } = useContext(AxiosContext);
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();

  const [expanded, setExpanded] = useState<Record<uuid, { data: AdminProjectVintageResponse; fetching: boolean }>>();

  const {
    data: reservesResponse,
    isMutating: loadingReserves,
    trigger: refreshRows,
  } = useTriggerRequest<ReserveQueryResponse>({
    url: "admin/reserves",
    queryParams: {
      limit: SERVER_PAGINATION_LIMIT,
      includeTotalCount: true,
      includeRelations: [ReserveRelation.PROJECT_VINTAGE, ReserveRelation.ORGANIZATION],
    },
    optimisticData: serverReservesResponse,
    swrOptions: {
      onError: (error: any) => {
        enqueueError("Unable to fetch reserves.");
        logger.error(`Unable to fetch reserves. Error: ${error?.message}`, {});
      },
    },
  });

  const {
    data: book,
    isMutating: loadingBook,
    trigger: refreshBook,
  } = useTriggerRequest<AdminBookResponse>({
    url: `admin/books/${process.env.NEXT_PUBLIC_ADMIN_PORTFOLIO_DEFAULT}`,
    queryParams: {
      includeRelations: [BookRelations.OWNER_ALLOCATIONS_NESTED],
    },
    optimisticData: serverDefaultPortofolioResponse,
    swrOptions: {
      onError: (error: any) => {
        enqueueError("Unable to fetch vintage data.");
        logger.error(`Unable to fetch vintage data. Error: ${error?.message}`, {});
      },
    },
  });

  const getSingleProjectVintage = async (id: uuid): Promise<void> => {
    try {
      const data = await api.get<AdminProjectVintageResponse>(
        `admin/project-vintages/${id}?includeRelations=${ProjectVintageRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE}`,
      );
      setExpanded((previous = {}) => ({
        ...previous,
        [id]: {
          data: data.data,
          fetching: false,
        },
      }));
    } catch (error: any) {
      enqueueError("Unable to fetch project vintage data.");
      logger.error(`Unable to fetch project vintage data. Error: ${error?.message}`, {});
    }
  };

  const [popoutRemove, setPopoutRemove] = useState<boolean>(false);
  const [deletingRowId, setDeletingRowId] = useState<uuid>();
  const [requestPayload, setRequestPayload] = useState<ReserveCreateRequest | ReserveUpdateRequest>();

  const { form, table } = useGenericTableUtility<ReserveModel>({
    form: {
      mode: "onSubmit",
      resolver: ReserveFormResolver as any,
      defaultValues: {
        amends: [],
      },
    },
  });

  const { handleSubmit, watch, smartSetValue } = form || {};

  const firstValue = watch?.("amends.0");

  const amendingRowId = firstValue?.id;

  const creating = useMemo(() => !!firstValue?.creating, [firstValue?.creating]);
  const editing = useMemo(() => !!firstValue?.editing, [firstValue?.editing]);

  // @kofi
  // maybe throw or an error here or something so it's very obvoius allocations_with_nested needs to be added?
  const validVintages = useMemo(
    () =>
      (book?.ownerAllocations as GroupedAllocationWithNestedResponse)?.allocations.filter(
        (allocation) => allocation.asset.type === AssetType.REGISTRY_VINTAGE && allocation.amountAvailable > 0,
      ) || [],
    [book?.ownerAllocations],
  );

  const toRowModel = useCallback(
    (row: ReserveResponse): GenericTableRowModel<ReserveModel> => {
      const vintage = validVintages?.find(({ asset }) => row?.projectVintage?.id === asset?.id);
      return {
        id: row?.id,
        projectVintage: row?.projectVintage as unknown as Vintage,
        amount: row?.amount,
        organizationAndMemo: {
          ...row?.organization,
          memo: row?.memo,
        } as unknown as OrganizationAndMemo,
        amountAvailable: vintage?.amountAvailable,
        updatedAt: row?.updatedAt,
        currentEditAllocated: row?.amount,
      };
    },
    [validVintages],
  );

  const {
    navigating: popoutInterrupt,
    continueNavigation,
    cancelNavigation,
  } = useNavigationInterceptor({
    shouldIntercept: () => !!form?.formState?.isDirty && (creating || editing),
  });

  const selectedProjectId = watch?.("amends.0.projectVintage.project.id");
  const selectedProjectVintageId = watch?.("amends.0.projectVintage.id");

  const projectOptions = useAutoCompleteOptions({
    data: validVintages,
    keys: ["asset"],
    label: (entry: any) => `${entry?.asset.registryProjectId} - ${entry?.asset.name}`,
    value: (entry: any) => entry?.asset.projectId,
    preTransform: (data: AllocationResponse[]) => uniqueByKeys(data, ["asset.projectId"]), // todo : @kofi idk what this is
    postTransform: (data: UseAutoCompleteOptionsReturnEntry[]) =>
      data?.sort((a, b) => a?.label?.localeCompare(b?.label)),
  });

  const vintageOptions = useAutoCompleteOptions({
    data: validVintages,
    keys: ["asset"],
    label: (entry: any) => entry?.asset.label ?? "",
    value: (entry: any) => entry?.asset.id,
    preTransform: (data: AllocationResponse[]) =>
      uniqueByKeys(
        data?.filter(({ asset }) => asset?.projectId === selectedProjectId),
        ["asset.id", "asset.label"],
      ),
    postTransform: (data: UseAutoCompleteOptionsReturnEntry[]) =>
      data?.sort((a, b) => a?.label?.localeCompare(b?.label)),
  });

  const organizationOptions = useAutoCompleteOptions({
    data: organizationsResponse,
    keys: ["id", "name"],
    label: (entry: any) => entry?.name,
    value: (entry: any) => entry?.id,
    postTransform: (data: UseAutoCompleteOptionsReturnEntry[]) =>
      data?.sort((a, b) => a?.label?.localeCompare(b?.label)),
  });

  const columns: GenericTableColumn<ReserveModel>[] = useMemo(
    () =>
      COLUMNS.reduce((accum, column) => {
        return [
          ...accum,
          ["Project", "Vintage", "Reserved", "Customer"].includes(column.label ?? "")
            ? {
                ...column,
                valueOptions:
                  column.label === "Project"
                    ? projectOptions
                    : column.label === "Vintage"
                      ? vintageOptions
                      : column.label === "Customer"
                        ? organizationOptions
                        : column.valueOptions,
              }
            : column,
        ];
      }, []),
    [organizationOptions, projectOptions, vintageOptions],
  );

  const { trigger: commitReserve, isMutating: committingReserve } = useTriggerRequest<ReserveResponse>({
    url: `admin/reserves${editing ? "/{id}" : ""}`,
    method: creating ? "post" : "patch",
    pathParams: {
      id: editing ? amendingRowId : null,
    },
    requestBody: requestPayload,
    swrOptions: {
      onSuccess: async () => {
        enqueueSuccess(`Successfully ${creating ? "created" : "updated"} reserve${editing ? " data." : "."}`);
        await refreshRows();
        await refreshBook();
      },
      onError: () => enqueueError(`Unable to ${creating ? "create" : "update"} reserve ${editing ? "data." : "."}`),
    },
  });

  const { trigger: deleteReserve, isMutating: deletingReserve } = useTriggerRequest({
    url: "admin/reserves/{id}",
    method: "delete",
    pathParams: {
      id: deletingRowId,
    },
    swrOptions: {
      onError: () => enqueueError("Unable to delete reserve."),
      onSuccess: async () => {
        enqueueSuccess("Successfully deleted reserve.");
        setPopoutRemove(false);
        await refreshRows();
        await refreshBook();
      },
    },
  });

  useUpdateEffect(() => {
    if (!selectedProjectVintageId) smartSetValue?.("amends.0.amount", undefined);
    else {
      const foundVintage = validVintages.find(
        ({ asset }) => (asset?.id as uuid) === (selectedProjectVintageId as uuid),
      );
      smartSetValue?.("amends.0.amountAvailable", foundVintage ? amountUnallocatedByBook(foundVintage) : 0);
    }
  }, [selectedProjectVintageId, validVintages]);

  useEffect(() => {
    const subscription = watch?.((value = {}, { name, type }) => {
      if (type === "change") {
        switch (name) {
          case "amends.0.projectVintage.project.id": // todo : @kofi I assume this is wrong
            if (!value?.amends?.at(0)?.projectVintage?.project?.id)
              smartSetValue?.("amends.0.projectVintage.id", undefined);
            break;
          default:
            break;
        }
      }
    });
    return (): void => subscription?.unsubscribe();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [validVintages, watch]);

  const onRowRemove = async (row: GenericTableRowModel<ReserveModel>): Promise<boolean> => {
    setDeletingRowId(row?.id);
    setTimeout(() => setPopoutRemove(true));
    return true;
  };

  const onSubmit = async (formData: Values<ReserveModel>): Promise<void> => {
    const firstAmend = formData.amends?.[0];
    if (!firstAmend) return;

    const { amount = 0, projectVintage, organizationAndMemo } = firstAmend as ReserveModel;

    if (creating) {
      const payload: ReserveCreateRequest = {
        amount,
        projectVintageId: projectVintage?.id as uuid,
        ...px({
          organizationId: (organizationAndMemo?.id ?? "")?.trim() || null,
          memo: organizationAndMemo?.memo?.trim() || null,
        }),
      };
      setRequestPayload(payload);
      setTimeout(() => commitReserve());
    } else {
      const payload: ReserveUpdateRequest = {
        amount,
        ...px({
          memo: organizationAndMemo?.memo?.trim() || null,
        }),
      };
      setRequestPayload(payload);
      setTimeout(() => {
        setExpanded((previous = {}) => ({
          ...previous,
          [projectVintage?.id as uuid]: {
            ...(previous?.[projectVintage?.id as uuid] || {}),
            fetching: true,
          },
        }));
        commitReserve().then(async () => await getSingleProjectVintage(projectVintage?.id as uuid));
      });
    }
  };

  return (
    <>
      <GenericTable
        id="Reserves"
        loading={loadingReserves}
        reloadingRow={loadingReserves || committingReserve || deletingReserve}
        columns={columns}
        pageableData={reservesResponse}
        globalSearch={{
          searchKeys: [
            "projectVintage.project.name",
            "projectVintage.project.registryProjectId",
            "projectVintage.name",
            "amountAvailable",
            "organizationAndMemo.name",
            "organizationAndMemo.memo",
            "updatedAt",
          ],
        }}
        toRowModel={toRowModel}
        isExpandable
        eager={{
          expand: true,
        }}
        export={{
          filename: `Reserves-${new Date()}`,
          setClientCanExport: table?.setClientCanExport,
          bindClientExport: table?.bindClientExport,
        }}
        styles={{
          root: {
            maxHeight: "calc(100vh - 185px)",
          },
        }}
        toolbarActionButtons={[
          {
            children: "Export",
            startIcon: <FileDownloadRounded />,
            requiredPermission: PermissionEnum.RESERVES_READ,
            style: DEFAULT_EXPORT_STYLE,
            isDisabled: loadingReserves || !table?.clientCanExport,
            onClickHandler: table?.handleClientExport,
          },
          {
            children: "Add Reserve",
            requiredPermission: PermissionEnum.RESERVES_WRITE,
            isDisabled: loadingReserves || creating || editing,
            onClickHandler: table?.handleAddRow,
          },
        ]}
        resetForm={table?.resetForm}
        useForm={table?.useForm}
        bindAddRow={table?.bindAddRow}
        renderExpandContent={(row): JSX.Element => {
          const ex = expanded?.[row?.projectVintage?.id as any];

          if (!ex?.data && !ex?.fetching) {
            setExpanded((previous = {}) => ({
              ...previous,
              [row?.projectVintage?.id as any]: {
                fetching: true,
              },
            }));
            setTimeout(async () => await getSingleProjectVintage(row?.projectVintage?.id as any));
          }

          const vintage = expanded?.[row?.projectVintage?.id as any]?.data;

          return (
            <Maybe condition={!!vintage}>
              {() => (
                <ProjectVintageAllocation
                  reservesAmount={row?.amount ?? 0}
                  allocatedToPortfolio={amountAllocatedToPortfolio(vintage!)}
                  unallocated={amountUnallocatedByVintage(vintage!)}
                  tradeSellAmount={pendingSellByVintageAssetAllocation(vintage!)}
                  tradeBuyAmount={vintage!.assetAllocations?.totalAmountPendingBuy ?? 0}
                  loading={ex?.fetching || loadingBook}
                />
              )}
            </Maybe>
          );
        }}
        onFormSubmit={handleSubmit?.(onSubmit)}
        onRowRemove={onRowRemove}
      />
      <GenericDialog
        open={popoutRemove}
        title="Confirm Reserve Deletion"
        positiveAction={{
          buttonText: "YES, PROCEED",
          loading: deletingReserve,
          disabled: deletingReserve,
          className: dialogClasses.PositiveAction,
        }}
        negativeAction={{
          buttonText: "CANCEL",
          disabled: deletingReserve,
        }}
        onClose={() => setPopoutRemove(false)}
        onPositiveClick={() => deleteReserve()}
        onNegativeClick={() => setPopoutRemove(false)}
        classes={{
          title: dialogClasses.Title,
          content: dialogClasses.Content,
        }}
      >
        <Stack component="p" rowGap={1}>
          <Typography component="span">Do you want to delete reserve.</Typography>
        </Stack>
      </GenericDialog>
      <GenericDialog
        open={popoutInterrupt}
        title={`Interrupt reserve ${creating ? "creation" : "editing"}?`}
        positiveAction={{
          buttonText: "INTERRUPT ANYWAY",
          onClick: continueNavigation,
          className: dialogClasses.PositiveAction,
        }}
        negativeAction={{
          buttonText: "NO",
          onClick: cancelNavigation,
        }}
        onClose={cancelNavigation}
        classes={{
          title: dialogClasses.Title,
          content: dialogClasses.Content,
        }}
      >
        <Typography>
          {creating ? "You will lose the data you entered" : "You will lose the change you have made"}
        </Typography>
      </GenericDialog>
    </>
  );
};

export default Reserves;
