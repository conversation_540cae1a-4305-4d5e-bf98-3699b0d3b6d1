import { Box, ToggleButton, ToggleButtonGroup, Typography } from "@mui/material";
import { useCallback, useMemo, useState } from "react";
import useSWR, { mutate } from "swr";
import TableBox from "@components/ui/table-box/table-box";
import EnhancedTable, { ColDef, SortOrder } from "@components/ui/table/enhanced-table";
import { stringComparator } from "@utils/comparators/comparator";
import dateFormatterEST from "@/utils/formatters/est-date-formatter";
import { Maybe, percentageFormat, useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import currencyFormat from "@/utils/formatters/currency-format";
import ArrowDropUpIcon from "@mui/icons-material/ArrowDropUp";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import { ResponseType } from "../types/response-type";
import { useLogger } from "@providers/logging";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { useDebounce } from "react-use";

import classes from "../styles/bid-ask.module.scss";

const toggleButtonStyle = {
  "&.MuiButtonBase-root": {
    color: "#094436",
    backgroundColor: "white",
  },
  "&.MuiButtonBase-root: hover": {
    backgroundColor: "#D1E1CB",
  },
  "&.Mui-selected": {
    color: "white",
    backgroundColor: "#094436",
  },
  "&.Mui-selected: hover": {
    backgroundColor: "rgb(0 78 60)",
  },
};

enum FilterEnum {
  "1D" = "1 Day",
  "1W" = "1 Week",
  "1M" = "1 Month",
  "3M" = "3 Months",
  "YTD" = "YTD",
  "1Y" = "1 Year",
}

const BidAskSection = ({ marketOffers: serverMarketOffers = [] }: { marketOffers: ResponseType[] }): JSX.Element => {
  const { logger } = useLogger();
  const { enqueueError } = useSnackbarVariants();
  const [filter, setFilter] = useState<string>("1 Week");
  const [searchFilter, setSearchFilter] = useState<string>("");
  const [debouncedSearchFilter, setDebouncedSearchFilter] = useState<string>("");
  const [page, setPage] = useState<number>(0);
  const [rowsPerPage, setRowsPerPage] = useState<number>(10);
  const [sortColumn, setSortColumn] = useState<string>("pricing_date");
  const [sortOrder, setSortOrder] = useState<SortOrder>(SortOrder.DESC);
  const [isExportTriggered, setIsExportTriggered] = useState(false);

  const {
    data,
    trigger: refreshMarketOffers,
    isMutating: loading,
  } = useTriggerRequest<ResponseType[]>({
    url: "/reporting/market-offers",
    queryParams: {
      limit: rowsPerPage,
      offset: page * rowsPerPage,
      sort_order: sortOrder,
      sort_column: sortColumn,
      date_filter: filter,
      search_string: debouncedSearchFilter,
    },
    optimisticData: serverMarketOffers,
    swrOptions: {
      onError: (error: any) => {
        enqueueError("Unable to fetch market offers.");
        logger.error(`Unable to fetch market offers: ${error?.message}`, {});
      },
    },
  });

  const marketOffers = useMemo(() => (data ? data.map((x) => ({ ...x, id: `${x.index}` })) : []), [data]);

  const { mutate: triggerExport } = useSWR<ResponseType[]>(
    isExportTriggered
      ? `/reporting/market-offers?date_filter=${filter}&search_string=${debouncedSearchFilter}&sort_order=${sortOrder}&sort_column=${sortColumn}`
      : null,
    { revalidateOnFocus: false, revalidateOnReconnect: false, shouldRetryOnError: false },
  );

  const handleChange = useCallback(
    (event: React.MouseEvent<HTMLElement>, newFilter: string): void => {
      event.preventDefault();
      if (newFilter) {
        setFilter(FilterEnum[newFilter as keyof typeof FilterEnum]);
        setPage(0);

        setTimeout(async () => {
          await refreshMarketOffers({
            queryParams: {
              offset: 0,
              date_filter: FilterEnum[newFilter as keyof typeof FilterEnum],
            },
          });
        });
      }
    },
    [refreshMarketOffers],
  );

  useDebounce(
    () => {
      if (searchFilter !== debouncedSearchFilter) {
        setDebouncedSearchFilter(searchFilter);
        setPage(0);

        setTimeout(async () => {
          await refreshMarketOffers({
            queryParams: {
              offset: 0,
              search_string: searchFilter,
            },
          });
        });
      }
    },
    600,
    [searchFilter],
  );

  const toggleOptions = useMemo(() => ["1D", "1W", "1M", "3M", "YTD", "1Y"], []);

  const columnsDef = useMemo<ColDef[]>(
    () => [
      {
        columnName: "project_name",
        displayName: "Name",
        comparator: stringComparator as any,
        formatter: {
          func: (x: any): JSX.Element => {
            return (
              <>
                <div>{x.get("project_name")}</div>
                <div style={{ color: "#a0a0a0" }}>
                  {x.get("project_id")} - {x.get("vintage")}
                </div>
              </>
            );
          },
          inputFields: ["project_name", "project_id", "vintage"],
        },
      },
      {
        columnName: "project_id",
        displayName: "Registry ID",
        hide: true,
      },
      {
        columnName: "vintage",
        displayName: "Vintage",
        hide: true,
      },
      {
        columnName: "country",
        displayName: "Country",
        comparator: stringComparator as any,
      },
      {
        columnName: "type",
        displayName: "Type",
        comparator: stringComparator as any,
      },
      {
        columnName: "bid_price",
        displayName: "Bid",
      },
      {
        columnName: "bid_change_perc",
        displayName: "Bid % Change",
        formatter: {
          func: ((bid_change_perc: number): JSX.Element => {
            return (
              <>
                <Typography
                  display={"inline-flex"}
                  variant="body2"
                  component="div"
                  sx={{ width: "100px" }}
                  className={
                    bid_change_perc < 0 ? classes.negativeValue : bid_change_perc == 0 ? "" : classes.positiveValue
                  }
                >
                  {percentageFormat(Math.abs(bid_change_perc))}
                  <Maybe condition={bid_change_perc != 0}>
                    {bid_change_perc < 0 ? <ArrowDropDownIcon /> : <ArrowDropUpIcon />}{" "}
                  </Maybe>
                </Typography>
              </>
            );
          }) as any,
        },
      },
      {
        columnName: "bid_change",
        displayName: "Bid Change",
        formatter: { func: currencyFormat as any },
        exportFormatter: { func: currencyFormat as any },
      },
      {
        columnName: "offer_price",
        displayName: "Ask",
      },
      {
        columnName: "offer_change_perc",
        displayName: "Ask % Change",
        formatter: {
          func: (offer_change_perc: number): JSX.Element => {
            return (
              <>
                <Typography
                  display={"inline-flex"}
                  variant="body2"
                  component="div"
                  sx={{ width: "100px" }}
                  className={
                    offer_change_perc < 0 ? classes.negativeValue : offer_change_perc == 0 ? "" : classes.positiveValue
                  }
                >
                  {percentageFormat(Math.abs(offer_change_perc))}
                  <Maybe condition={offer_change_perc != 0}>
                    {offer_change_perc < 0 ? <ArrowDropDownIcon /> : <ArrowDropUpIcon />}{" "}
                  </Maybe>
                </Typography>
              </>
            );
          },
        },
      },
      {
        columnName: "offer_change",
        displayName: "Ask Change",
        formatter: { func: currencyFormat as any },
        exportFormatter: { func: currencyFormat as any },
      },
      {
        columnName: "traded_price",
        displayName: "Traded Price",
      },
      {
        columnName: "volume",
        displayName: "Volume",
      },
      {
        columnName: "pricing_date",
        displayName: "Date",
        formatter: { func: dateFormatterEST as any },
        exportFormatter: { func: dateFormatterEST as any },
      },
      {
        columnName: "source",
        displayName: "Source",
        comparator: stringComparator as any,
      },
    ],
    [],
  );

  const getSearchBarContent = useCallback((): JSX.Element => {
    return (
      <Box sx={{ textAlign: "right" }}>
        <ToggleButtonGroup
          color="primary"
          value={Object.keys(FilterEnum).find((key) => FilterEnum[key as keyof typeof FilterEnum] === filter)}
          exclusive
          onChange={handleChange}
          aria-label="Platform"
          sx={{ height: "35px" }}
        >
          {toggleOptions.map((option) => (
            <ToggleButton key={option} value={option} sx={toggleButtonStyle}>
              {option}
            </ToggleButton>
          ))}
        </ToggleButtonGroup>
      </Box>
    );
  }, [toggleOptions, filter, handleChange]);

  const getFilteredData = (input: string): void => {
    setSearchFilter(input);
  };

  const handlePageChange = (newPage: number, newRowsPerPage: number): void => {
    setPage(newPage);
    setRowsPerPage(newRowsPerPage);

    setTimeout(async () => {
      await refreshMarketOffers({
        queryParams: {
          limit: newRowsPerPage,
          offset: newPage * newRowsPerPage,
        },
      });
    });
  };

  const handleSortChange = (columnName: string, order: SortOrder): void => {
    setSortColumn(columnName);
    setSortOrder(order);

    setTimeout(async () => {
      await refreshMarketOffers({
        queryParams: {
          sort_order: order,
          sort_column: columnName,
        },
      });
    });
  };

  const handleExport = async (): Promise<ResponseType[]> => {
    setIsExportTriggered(true);

    try {
      await mutate(
        `/reporting/market-offers?date_filter=${filter}&search_string=${debouncedSearchFilter}&sort_order=${sortOrder}&sort_column=${sortColumn}`,
      );
      const export_vals = await triggerExport();
      return export_vals ?? [];
    } catch (error) {
      console.error("Error triggering export:", error);
      return [];
    } finally {
      setIsExportTriggered(false);
    }
  };

  return (
    <Box>
      <TableBox>
        {marketOffers && (
          <EnhancedTable
            name={"bid_ask_info"}
            columnsDef={columnsDef}
            exportable={true}
            data={marketOffers as any}
            getFilteredData={getFilteredData}
            defaultSort={{ columnName: "project_name", order: SortOrder.ASC }}
            rowsCountPerPage={rowsPerPage}
            searchBarContent={getSearchBarContent}
            filter={searchFilter}
            onSearchHandler={getFilteredData}
            onPageChange={handlePageChange}
            onSortChange={handleSortChange}
            onExport={handleExport as any}
            loading={loading}
            serverSidePagination={true}
            serverSideSorting={true}
          />
        )}
      </TableBox>
    </Box>
  );
};

export default BidAskSection;
