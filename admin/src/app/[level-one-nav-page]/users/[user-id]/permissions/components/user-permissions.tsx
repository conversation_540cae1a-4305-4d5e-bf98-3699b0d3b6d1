import React, { useContext, useState, useEffect, BaseSyntheticEvent, SyntheticEvent } from "react";
import { AxiosContext } from "@providers/axios-provider";
import { useRouter } from "next/navigation";
import {
  UserResponse,
  OrganizationResponse,
  OrganizationUserRole,
  TrimmedUserOrganizationResponse,
} from "@rubiconcarbon/shared-types";
import {
  Box,
  Grid,
  Card,
  CardActions,
  CardContent,
  TextField,
  Button,
  Autocomplete,
  FormControlLabel,
  Typography,
  FormLabel,
  FormControl,
  Radio,
  RadioGroup,
} from "@mui/material";
import { isEmpty } from "lodash";
import ResultDialog from "@components/ui/dialogs/result-dialog";
import { isNothing } from "@rubiconcarbon/frontend-shared";
import { HttpStatusCodes, HttpStatusLabels } from "@/constants/http";
import { HttpStatusLabelsType } from "@/types/http";

const noteStyle = {
  marginBottom: 1,
  lineHeight: "150%",
  color: "rgba(0, 0, 0, 0.6)",
  fontWeight: 400,
  marginTop: 0.5,
};

const radioLabelStyle = {
  marginTop: 1,
  marginBottom: 1,
};

export default function UserPermissions({
  userResponse,
  organizationsResponse,
}: {
  userResponse: UserResponse;
  organizationsResponse: OrganizationResponse[];
}): JSX.Element {
  const [submissionInFlight, setSubmissionInFlight] = useState<boolean>(false);
  const { api } = useContext(AxiosContext);
  const router = useRouter();
  const [selectedOrganization, setSelectedOrganization] = useState<any>(null);
  const [selectedRole, setSelectedRole] = useState<string>("");
  const [permissionsResultState, setPermissionsResultState] = useState({
    isOpen: false,
    isCloseAfterConfirm: true,
    responseType: HttpStatusLabels.SUCCESS,
    resultMessage: "",
  });

  const shouldDisableOrganizations = !isNothing(userResponse?.organization);

  useEffect(() => {
    if (userResponse && !isNothing(userResponse?.organization)) {
      setSelectedOrganization(userResponse.organization);

      if (!isNothing(userResponse?.organization?.userRoles))
        setSelectedRole(userResponse?.organization?.userRoles[0] ?? "");
    }
  }, [userResponse]);

  const organizationSelectionHandler = (
    _: SyntheticEvent,
    newValue: OrganizationResponse | null,
  ): void => {
    setSelectedOrganization(newValue);
  };

  const closePermissionsResultHandler = (): void => {
    setPermissionsResultState({
      ...permissionsResultState,
      isOpen: false,
    });

    if (permissionsResultState.isCloseAfterConfirm) {
      setSubmissionInFlight(true);
      router.back();
    }
    setSubmissionInFlight(false);
  };

  const onSubmit = (event: BaseSyntheticEvent): void => {
    event.preventDefault();

    const payload = {
      roles: [selectedRole],
    };

    setSubmissionInFlight(true);

    let message = "";
    let isCloseAfterConfirm = true;
    let activeResponseType: HttpStatusLabelsType;
    api
      .put<OrganizationResponse>(`admin/organizations/${selectedOrganization.id}/users/${userResponse.id}`, payload)
      .then((response) => {
        if (response.status === HttpStatusCodes.CREATED || response.status === HttpStatusCodes.SUCCESS) {
          activeResponseType = HttpStatusLabels.SUCCESS;
          message = `User organization & permissions were set successfully.`;
        } else {
          message = "Failed to set user organization & permissions. Please try again later.";
          activeResponseType = HttpStatusLabels.ERROR;
        }
      })
      .catch((e) => {
        console.error("Setting user organization & permissions failed!", e);
        activeResponseType = HttpStatusLabels.ERROR;
        if (isEmpty(e?.response?.data?.message)) {
          message = "Failed to set user organization & permissions. Please try again later.";
        } else {
          message = `Failed to set user organization & permissions. ${e.response.data.message}`;
          isCloseAfterConfirm = false;
        }
      })
      .finally(() => {
        setSubmissionInFlight(false);
        setPermissionsResultState({
          isCloseAfterConfirm,
          resultMessage: message,
          isOpen: true,
          responseType: activeResponseType,
        });
      });
  };

  const organizationDefaultProps = {
    options: organizationsResponse.filter((organization) => organization.isEnabled),
    getOptionLabel: (option: OrganizationResponse): string => (option?.name ? option.name : ""),
    isOptionEqualToValue: (option: any, value: any): boolean =>
      option.id === value.id,
  };

  const roleChangeHandler = (event: React.ChangeEvent<HTMLInputElement>): void => {
    setSelectedRole((event.target as HTMLInputElement).value);
  };

  const itemDtailsCard = userResponse && (
    <React.Fragment>
      <CardContent>
        <form id="permissions-request-form" onSubmit={onSubmit}>
          <fieldset disabled={submissionInFlight} style={{ display: "contents" }}>
            <Grid container gap={4} flexDirection="column">
              <FormControl fullWidth>
                <FormLabel component="p" sx={{ fontWeight: 600, lineHeight: "175%" }}>
                  Organization
                </FormLabel>
                <FormLabel component="legend" sx={{ marginBottom: 1.5, lineHeight: "150%" }}>
                  Select from the list below to assign user <b>{userResponse.email}</b> to an organization.
                </FormLabel>
                <Autocomplete
                  {...organizationDefaultProps}
                  onChange={organizationSelectionHandler}
                  value={selectedOrganization}
                  loading
                  disabled={shouldDisableOrganizations}
                  disablePortal
                  id="organization"
                  renderInput={(params) => <TextField required {...params} label="Organization name" />}
                />
                <FormLabel component="legend" sx={noteStyle}>
                  Note: Organization must be created before you can assign a user.
                </FormLabel>
              </FormControl>

              <FormControl fullWidth sx={{ marginTop: -3 }}>
                <FormLabel component="p" sx={{ fontWeight: 600, lineHeight: "175%" }}>
                  Roles
                </FormLabel>
                <FormLabel id="organization-user-role-group">
                  Select one role below to set organization permissions.
                </FormLabel>
                <RadioGroup
                  aria-labelledby="organization-user-role-group"
                  name="controlled-radio-buttons-group"
                  value={selectedRole}
                  onChange={roleChangeHandler}
                >
                  <FormControlLabel
                    sx={radioLabelStyle}
                    disabled
                    value={OrganizationUserRole.MANAGER}
                    control={<Radio required />}
                    label={
                      <div>
                        <Typography>Manager</Typography>
                        <Typography variant="caption">
                          Can submit transactions and manage permissions for organization users.
                        </Typography>
                      </div>
                    }
                  />
                  <FormControlLabel
                    sx={radioLabelStyle}
                    value={OrganizationUserRole.VIEWER}
                    control={<Radio required />}
                    label={
                      <div>
                        <Typography>Viewer</Typography>
                        <Typography variant="caption">Can view account information and request quotes.</Typography>
                      </div>
                    }
                  />
                  <FormControlLabel
                    sx={radioLabelStyle}
                    value={OrganizationUserRole.TRANSACTOR}
                    control={<Radio required />}
                    label={
                      <div>
                        <Typography>Transaction Manager</Typography>
                        <Typography variant="caption">
                          Can view account information, request quotes, and submit retirement requests.
                        </Typography>
                      </div>
                    }
                  />
                </RadioGroup>
              </FormControl>
            </Grid>
          </fieldset>
        </form>
      </CardContent>
      <CardActions
        sx={{
          justifyContent: "space-between",
        }}
      >
        <Box ml={2} sx={{ display: "flex" }}></Box>
        <Box mr={2} mb={1} mt={1}>
          <Button
            sx={{ marginRight: 3, fontWeight: 600 }}
            disabled={submissionInFlight}
            variant="text"
            onClick={() => router.back()}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            type="submit"
            form="permissions-request-form"
            sx={{ px: 3.5, color: "#FFFFFF" }}
            disabled={submissionInFlight}
          >
            Assign
          </Button>
        </Box>
      </CardActions>
    </React.Fragment>
  );

  return (
    <Box mt={4}>
      <Grid container spacing={0} direction="column" alignItems="center" style={{ minHeight: "100vh" }}>
        <Box mt={0} sx={{ width: "100%", minWidth: 800, maxWidth: 1200 }}>
          <Card variant="elevation" sx={{ borderRadius: 4, backgroundColor: "#FAFAFA" }}>
            {itemDtailsCard}
          </Card>
        </Box>
      </Grid>
      <ResultDialog
        isOpen={permissionsResultState.isOpen}
        onClose={closePermissionsResultHandler}
        message={permissionsResultState.resultMessage}
        title={"Set Organization and Permissions"}
        requestInFlight={submissionInFlight}
        responseType={permissionsResultState.responseType}
      />
    </Box>
  );
}
