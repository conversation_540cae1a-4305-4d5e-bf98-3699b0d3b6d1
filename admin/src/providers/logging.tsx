/* eslint-disable no-console */
import { context } from "@opentelemetry/api";
import { logs, SeverityNumber, LogAttributes } from "@opentelemetry/api-logs";
import { LoggerProvider, BatchLogRecordProcessor } from "@opentelemetry/sdk-logs";
import { PropsWithChildren, createContext, useContext } from "react";
import { OTLPLogExporter } from "@opentelemetry/exporter-logs-otlp-http";
import { OTLPExporterNodeConfigBase } from "@opentelemetry/otlp-exporter-base";

type LoggingLevel = "debug" | "info" | "warn" | "error";

const levelMapping = new Map<LoggingLevel, SeverityNumber>([
  ["debug", SeverityNumber.DEBUG],
  ["info", SeverityNumber.INFO],
  ["warn", SeverityNumber.WARN],
  ["error", SeverityNumber.ERROR],
]);

const serviceName = process.env.NEXT_PUBLIC_APP_NAME ?? "rubicon-admin";
const deploymentEnvironment = process.env.NEXT_PUBLIC_DEPLOYMENT_ENVIRONMENT ?? "local";

interface Logger {
  debug(message: any, attributes: LogAttributes): void;
  info(message: any, attributes: LogAttributes): void;
  warn(message: any, attributes: LogAttributes): void;
  error(message: any, attributes: LogAttributes): void;
  message(level: LoggingLevel, message: any, attributes: LogAttributes): void;
}

// Logger instance

let logger: Logger;

// run on client only
if (typeof window !== "undefined") {
  const collectorOptions: OTLPExporterNodeConfigBase = {
    url: "/logging",
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Headers": "*",
    },
    concurrencyLimit: 10,
  };

  // OpenTelemetry Provider
  const loggerProvider = new LoggerProvider();
  loggerProvider.addLogRecordProcessor(new BatchLogRecordProcessor(new OTLPLogExporter(collectorOptions)));
  logs.setGlobalLoggerProvider(loggerProvider);

  class TelemetryLogger implements Logger {
    private logger = logs.getLogger("default");

    debug(message: any, attributes: LogAttributes = {}): void {
      this.message("debug", message, attributes);
    }
    info(message: any, attributes: LogAttributes = {}): void {
      this.message("info", message, attributes);
    }
    warn(message: any, attributes: LogAttributes = {}): void {
      this.message("warn", message, attributes);
    }
    error(message: any, attributes: LogAttributes = {}): void {
      this.message("error", message, attributes);
    }
    message(level: LoggingLevel, message: any, attributes: LogAttributes): void {
      if (!("service.name" in attributes))
        attributes["service.name"] = serviceName;

      if (!("deployment.environment" in attributes))
        attributes["deployment.environment"] = deploymentEnvironment;

      this.logger.emit({
        severityNumber: levelMapping.get(level),
        attributes,
        body: message,
        context: context.active(),
      });
    }
  }

  logger = new TelemetryLogger();
} else {
  class ConsoleLogger implements Logger {
    debug(message: any, attributes: LogAttributes = {}): void {
      console.debug(message, attributes);
    }
    info(message: any, attributes: LogAttributes = {}): void {
      console.info(message, attributes);
    }
    warn(message: any, attributes: LogAttributes = {}): void {
      console.warn(message, attributes);
    }
    error(message: any, attributes: LogAttributes = {}): void {
      console.error(message, attributes);
    }
    message(level: LoggingLevel, message: any, attributes: LogAttributes): void {
      console.log(message, attributes, level);
    }
  }

  // logger instance
  logger = new ConsoleLogger();
}

// Context
export function useLogger(): { logger: Logger } {
  return useContext(createContext<{ logger: Logger }>({ logger }));
}

// Provider
export function LoggingProvider(props: PropsWithChildren): JSX.Element {
  return <>{props.children}</>;
}
