declare module '@hcorta/react-echarts' {
    import { Component } from 'react';
    import { EChartsOption } from 'echarts';
  
    export interface EChartProps {
      option: EChartsOption;
      style?: React.CSSProperties;
      className?: string;
      theme?: string | object;
      onChartReady?: (chart: any) => void;
      showLoading?: boolean;
      loadingOption?: any;
      onEvents?: Record<string, () => any>;
      opts?: {
        renderer?: 'canvas' | 'svg';
        width?: number | string;
        height?: number | string;
        devicePixelRatio?: number;
      };
      // Add other props you use
      [key: string]: any;
    }
  
    export class EChart extends Component<EChartProps> {}
    
    // If there are other exports, add them here
    export default EChart;
  }