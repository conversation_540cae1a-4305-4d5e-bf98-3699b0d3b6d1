import { BookTypeToLabel } from "@constants/book-type-to-label";
import { PrimitiveTypeBook } from "@models/primitive-type-book";
import { pickFromRecord, calculator } from "@rubiconcarbon/frontend-shared";
import {
  AdminBookResponse,
  AssetType,
  BookType,
  AllocationResponse,
  GroupingParentResponse,
  GroupedAllocationResponse,
  GroupedAllocationWithNestedResponse,
  TrimmedBookResponse,
  AssetTypeGroupedAllocationResponse,
  BookAction,
  GroupedPriceResponse,
} from "@rubiconcarbon/shared-types";

type COMBINATION_TYPE =
  | "parent_book"
  | "trimmed_parent_book"
  | "rct_allocations"
  | "registry_vintage_allocations"
  | "owner_rct_allocations"
  | "owner_registry_vintage_allocations"
  | "owner_rct_allocations:available"
  | "owner_registry_vintage_allocations:available"
  | "owner_rct_prices"
  | "owner_registry_vintage_prices"
  | "owner_rct_nested_allocations"
  | "owner_registry_vintage_nested_allocations"
  | "owner_rct_nested_allocations:available"
  | "owner_registry_vintage_nested_allocations:available";

type COMBINATION_RETURN = {
  parentBook?: AdminBookResponse;
  trimmedParentBook?: TrimmedBookResponse;
  rctAllocations?: GroupedAllocationResponse;
  registryVintageAllocations?: GroupedAllocationResponse;
  ownerRctPrice?: GroupedPriceResponse;
  ownerRegistryVintagePrices?: GroupedPriceResponse;
  ownerRctAllocations?: AllocationResponse[];
  ownerRegistryVintageAllocations?: AllocationResponse[];
  ownerRctNestedAllocations?: Record<string, AllocationResponse>;
  ownerRegistryVintageNestedAllocations?: Record<string, AllocationResponse>;
};

const toCc = (str: string): string => str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());

const NON_RCT_BOOK_TYPES = [
  BookType.COMPLIANCE_DEFAULT,
  BookType.OPPORTUNISTIC_DEFAULT,
  BookType.REHABILITATION_DEFAULT,
  BookType.AGED_DEFAULT,
];

// @deprecated | kofi
export const getBookTypeToBook = (books: AdminBookResponse[] = []): Record<BookType, PrimitiveTypeBook> => {
  return books?.reduce(
    (record: Record<BookType, PrimitiveTypeBook>, book) => {
      if (NON_RCT_BOOK_TYPES.includes(book.type)) record[book.type] = book as unknown as PrimitiveTypeBook;
      else {
        let current = record[BookType.PORTFOLIO_DEFAULT] || ({} as PrimitiveTypeBook);
        const bookAllocations = book.ownerAllocations;

        if (book.type === BookType.PORTFOLIO_DEFAULT)
          current = {
            ...current,
            ...pickFromRecord(book, ["id", "type", "isEnabled", "description", "allowedActions"]),
          } as unknown as PrimitiveTypeBook;

        record[BookType.PORTFOLIO_DEFAULT] = {
          ...current,
          name: BookTypeToLabel[BookType.PORTFOLIO_DEFAULT] ?? "",
          limit: {
            ...(current?.limit || {}),
            holdingPriceMax: calculator(current?.limit?.holdingPriceMax)
              .add(book?.limit?.holdingPriceMax)
              .calculate()
              .toString(),
            holdingPriceMin: calculator(current?.limit?.holdingPriceMin)
              .add(book?.limit?.holdingPriceMin)
              .calculate()
              .toString(),
          },
          priceVintagesAllocated: calculator(current?.priceVintagesAllocated)
            .add(bookAllocations?.groupedPrices?.totalPriceAllocated)
            .calculate()
            .toString(),
          priceVintagesPendingBuy: calculator(current?.priceVintagesPendingBuy)
            .add(bookAllocations?.groupedPrices?.totalPricePendingBuy)
            .calculate()
            .toString(),
          priceVintagesPendingSell: calculator(current?.priceVintagesPendingSell)
            .add(bookAllocations?.groupedPrices?.totalPricePendingSell)
            .calculate()
            .toString(),
          amountVintagesAllocated: calculator(current?.amountVintagesAllocated)
            .add(bookAllocations?.totalAmountAllocated)
            .calculate()
            .toNumber(),
          amountVintagesPendingBuy: calculator(current?.amountVintagesPendingBuy)
            .add(bookAllocations?.totalAmountPendingBuy)
            .calculate()
            .toNumber(),
          amountVintagesPendingSell: calculator(current?.amountVintagesPendingSell)
            .add(bookAllocations?.totalAmountPendingSell)
            .calculate()
            .toNumber(),
        };
      }

      return record;
    },
    {} as Record<BookType, PrimitiveTypeBook>,
  );
};

export const groupParentBookTypesByRegistryVintage = (
  groupings: GroupingParentResponse[],
  bookActions: BookAction[] = [BookAction.BUY, BookAction.SELL],
): Record<string, BookType[]> => {
  let pairs: Record<string, BookType[]> = {};

  const withAllocations = groupings?.filter(
    (group) =>
      Object.hasOwn(group?.ownerAllocations ?? {}, "allocations") &&
      (group?.ownerAllocations as GroupedAllocationWithNestedResponse)?.allocations?.length > 0,
  );

  for (const group of withAllocations || []) {
    const allocations = (group.ownerAllocations as GroupedAllocationWithNestedResponse)?.allocations;

    for (const { asset, owner } of allocations) {
      if (asset?.type === AssetType.REGISTRY_VINTAGE) {
        pairs = {
          ...pairs,
          [asset?.id]: Array.from(
            new Set(
              [
                ...(pairs?.[asset?.id] || []),
                owner?.allowedActions?.some((action) => bookActions.includes(action)) ? owner?.type : null,
              ].filter((type) => !!type),
            ),
          ),
        };
      }
    }
  }

  return pairs;
};

export const groupAssetAllocationsByParentBookTypes = (
  groupings: GroupingParentResponse[],
  assetTypes: AssetType[],
): Record<string, AssetTypeGroupedAllocationResponse[]> =>
  groupings?.reduce(
    (record, { books, ownerAllocationsByAssetType = [] }) => ({
      ...record,
      [(books?.length > 1 ? BookType.PORTFOLIO_DEFAULT : books?.at(0)?.type) || '']: ownerAllocationsByAssetType?.filter(
        ({ assetType }) => assetTypes.includes(assetType),
      ),
    }),
    {},
  );

export const groupTrimmedParentBookByParentBookType = (
  groupings: GroupingParentResponse[],
): Record<string, TrimmedBookResponse> =>
  groupings?.reduce(
    (record, { books }) => ({
      ...record,
      [(books?.length > 1 ? BookType.PORTFOLIO_DEFAULT : books?.at(0)?.type) || '']:
        books?.length > 1 ? books?.find(({ type }) => type === BookType.PORTFOLIO_DEFAULT) : books?.at(0),
    }),
    {},
  );

export const groupParentBookByParentBookType = (
  groupings: GroupingParentResponse[],
): Record<string, AdminBookResponse> =>
  groupings?.reduce(
    (record, { books, ...rest }) => ({
      ...record,
      [(books?.length > 1 ? BookType.PORTFOLIO_DEFAULT : books?.at(0)?.type) || '']: {
        ...rest,
        ...(books?.length > 1 ? books?.find(({ type }) => type === BookType.PORTFOLIO_DEFAULT) : books?.at(0)),
      },
    }),
    {},
  );

export const groupParentBookAllocationsByParentBookType = (
  groupings: GroupingParentResponse[],
  take: ("allocations" | "allocations:available" | "groupedPrices")[] = ["allocations", "groupedPrices"],
  assetTypes: AssetType[] = [AssetType.RCT, AssetType.REGISTRY_VINTAGE],
): Record<string, Partial<GroupedAllocationResponse | GroupedAllocationWithNestedResponse>> => {
  const record: Record<string, Partial<GroupedAllocationResponse | GroupedAllocationWithNestedResponse>> = {};

  for (const { books, ownerAllocations } of groupings || []) {
    const bookType = books?.length > 1 ? BookType.PORTFOLIO_DEFAULT : books?.at(0)?.type;

    if (bookType) {
      record[bookType] = {};

      for (const key of take) {
        const ownerAllocs = ownerAllocations as GroupedAllocationWithNestedResponse;
  
        if (key?.startsWith("allocations")) {
          const filtered = ownerAllocs?.allocations?.filter(({ asset, amountAvailable = 0 }) =>
            assetTypes?.includes(asset?.type) && key?.endsWith("available")
              ? amountAvailable > 0
              : assetTypes?.includes(asset?.type),
          );
          record[bookType] = { ...record[bookType], allocations: filtered };
        } else if (key === "groupedPrices") record[bookType][key] = ownerAllocs?.groupedPrices;
      }
    }
  }

  return record;
};

export const groupParentBookNestedAllocationsByParentBookType = (
  groupings: GroupingParentResponse[],
  take: "allocations" | "allocations:available" = "allocations",
  assetTypes: AssetType[] = [AssetType.RCT, AssetType.REGISTRY_VINTAGE],
): Record<string, Record<string, AllocationResponse>> => {
  const record: Record<string, Record<string, AllocationResponse>> = {};

  for (const { books, ownerAllocations } of groupings || []) {
    const bookType = books?.length > 1 ? BookType.PORTFOLIO_DEFAULT : books?.at(0)?.type;

    if (bookType) {
      record[bookType] = {};

      const ownerAllocs = ownerAllocations as GroupedAllocationWithNestedResponse;
  
      const filtered = ownerAllocs?.allocations?.filter(({ asset, amountAvailable = 0, owner }) =>
        owner?.type?.endsWith(":default") && assetTypes?.includes(asset?.type)
          ? take?.endsWith("available")
            ? amountAvailable > 0
            : true
          : false,
      );
  
      for (const allocation of filtered) {
        record[bookType][allocation?.asset?.id] = allocation;
      }
    }
  }

  return record;
};

export const getParentBooksFromGrouping = (groupings: GroupingParentResponse[]): TrimmedBookResponse[] =>
  Object.values(groupParentBookByParentBookType(groupings));

export const combineByParentBookType = (
  groupings: GroupingParentResponse[],
  combineTypes: COMBINATION_TYPE[],
): Record<string, COMBINATION_RETURN> => {
  const combinations = combineTypes
    ?.filter((type) => !type.endsWith(":available"))
    ?.reduce(
      (record, type) => ({
        ...(record  as COMBINATION_RETURN),
        [toCc(type)]: undefined,
      }),
      {},
    );

  const group = groupings?.reduce(
    (record, { books }) => ({
      ...record,
      [(books?.length > 1 ? BookType.PORTFOLIO_DEFAULT : books?.at(0)?.type) || '']: { ...combinations },
    }),
    {} as Record<string, COMBINATION_RETURN>,
  );

  if (combineTypes.includes("parent_book")) {
    const section = groupParentBookByParentBookType(groupings);

    for (const [type, book] of Object.entries(section || {})) {
      if (type in group && group[type] && 'parentBook' in group[type]) {
        (group[type] as COMBINATION_RETURN).parentBook = book;
      }
    }
  }

  if (combineTypes.includes("trimmed_parent_book")) {
    const section = groupTrimmedParentBookByParentBookType(groupings);

    for (const [type, book] of Object.entries(section || {})) {
      if (type in group && group[type] && 'trimmedParentBook' in group[type]) {
        (group[type] as COMBINATION_RETURN).trimmedParentBook = book;
      }
    }
  }

  if (combineTypes.includes("rct_allocations")) {
    const section = groupAssetAllocationsByParentBookTypes(groupings, [AssetType.RCT]);

    for (const [type, allocation] of Object.entries(section || {})) {
      if (type in group && group[type] && 'rctAllocations' in group[type]) {
        (group[type] as COMBINATION_RETURN).rctAllocations = allocation?.at(0);
      }
    }
  }

  if (combineTypes.includes("registry_vintage_allocations")) {
    const section = groupAssetAllocationsByParentBookTypes(groupings, [AssetType.REGISTRY_VINTAGE]);

    for (const [type, allocation] of Object.entries(section || {})) {
      if (type in group && group[type] && 'registryVintageAllocations' in group[type]) {
        (group[type] as COMBINATION_RETURN).registryVintageAllocations = allocation?.at(0);
      }
    }
  }

  if (combineTypes.includes("owner_rct_allocations")) {
    const section = groupParentBookAllocationsByParentBookType(groupings, ["allocations"], [AssetType.RCT]);

    for (const [type, ownerAllocation] of Object.entries(section || {})) {
      if (type in group && group[type]) {
        (group[type] as COMBINATION_RETURN).ownerRctAllocations = (ownerAllocation as GroupedAllocationWithNestedResponse)?.["allocations"];
      }
    }
  }

  if (combineTypes.includes("owner_rct_allocations:available")) {
    const section = groupParentBookAllocationsByParentBookType(groupings, ["allocations:available"], [AssetType.RCT]);

    for (const [type, ownerAllocation] of Object.entries(section || {})) {
      if (type in group && group[type]) {
        (group[type] as COMBINATION_RETURN).ownerRctAllocations = (ownerAllocation as GroupedAllocationWithNestedResponse)?.["allocations"];
      }
    }
  }

  if (combineTypes.includes("owner_registry_vintage_allocations")) {
    const section = groupParentBookAllocationsByParentBookType(
      groupings,
      ["allocations"],
      [AssetType.REGISTRY_VINTAGE],
    );

    for (const [type, ownerAllocation] of Object.entries(section || {})) {
      if (type in group && group[type]) {
        (group[type] as COMBINATION_RETURN).ownerRegistryVintageAllocations = (ownerAllocation as GroupedAllocationWithNestedResponse)?.["allocations"];
      }
    }
  }

  if (combineTypes.includes("owner_registry_vintage_allocations:available")) {
    const section = groupParentBookAllocationsByParentBookType(
      groupings,
      ["allocations:available"],
      [AssetType.REGISTRY_VINTAGE],
    );

    for (const [type, ownerAllocation] of Object.entries(section || {})) {
      if (type in group && group[type]) {
        (group[type] as COMBINATION_RETURN).ownerRegistryVintageAllocations = (ownerAllocation as GroupedAllocationWithNestedResponse)?.["allocations"];
      }
    }
  }

  if (combineTypes.includes("owner_rct_prices")) {
    const section = groupParentBookAllocationsByParentBookType(groupings, ["groupedPrices"], [AssetType.RCT]);

    for (const [type, ownerAllocation] of Object.entries(section || {})) {
      if (type in group && group[type]) {
        (group[type] as COMBINATION_RETURN).ownerRctPrice = ownerAllocation?.["groupedPrices"];
      }
    }
  }

  if (combineTypes.includes("owner_registry_vintage_prices")) {
    const section = groupParentBookAllocationsByParentBookType(
      groupings,
      ["groupedPrices"],
      [AssetType.REGISTRY_VINTAGE],
    );

    for (const [type, ownerAllocation] of Object.entries(section || {})) {
      if (type in group && group[type]) {
        (group[type] as COMBINATION_RETURN).ownerRegistryVintagePrices = ownerAllocation?.["groupedPrices"];
      }
    }
  }

  if (combineTypes.includes("owner_rct_nested_allocations")) {
    const section = groupParentBookNestedAllocationsByParentBookType(groupings, "allocations", [AssetType.RCT]);

    for (const [type, nestedAllocations] of Object.entries(section || {})) {
      if (type in group && group[type]) {
        (group[type] as COMBINATION_RETURN).ownerRctNestedAllocations = nestedAllocations;
      }
    }
  }

  if (combineTypes.includes("owner_rct_nested_allocations:available")) {
    const section = groupParentBookNestedAllocationsByParentBookType(groupings, "allocations:available", [
      AssetType.RCT,
    ]);

    for (const [type, nestedAllocations] of Object.entries(section || {})) {
      if (type in group && group[type]) {
        (group[type] as COMBINATION_RETURN).ownerRctNestedAllocations = nestedAllocations;
      }
    }
  }

  if (combineTypes.includes("owner_registry_vintage_nested_allocations")) {
    const section = groupParentBookNestedAllocationsByParentBookType(groupings, "allocations", [
      AssetType.REGISTRY_VINTAGE,
    ]);

    for (const [type, nestedAllocations] of Object.entries(section || {})) {
      if (type in group && group[type]) {
        (group[type] as COMBINATION_RETURN).ownerRegistryVintageNestedAllocations = nestedAllocations;
      }
    }
  }

  if (combineTypes.includes("owner_registry_vintage_nested_allocations:available")) {
    const section = groupParentBookNestedAllocationsByParentBookType(groupings, "allocations:available", [
      AssetType.REGISTRY_VINTAGE,
    ]);

    for (const [type, nestedAllocations] of Object.entries(section || {})) {
      if (type in group && group[type]) {
        (group[type] as COMBINATION_RETURN).ownerRegistryVintageNestedAllocations = nestedAllocations;
      }
    }
  }

  return group || ({} as Record<string, COMBINATION_RETURN>);
};
