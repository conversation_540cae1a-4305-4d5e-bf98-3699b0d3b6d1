import { calculator, toNumber, Undefinable } from "@rubiconcarbon/frontend-shared";
import {
  AdminBookResponse,
  AllocationResponse,
  AssetType,
  GroupedAllocationWithNestedResponse,
  GroupedPriceResponse,
} from "@rubiconcarbon/shared-types";

type COMBINATION_TYPE =
  | "rct_asset_to_allocations"
  | "rct_asset_to_allocations:available"
  | "registry_vintage_asset_to_allocations"
  | "registry_vintage_asset_to_allocations:available"
  | "summarized_numbers"
  | "summarized_rct_numbers"
  | "summarized_registry_vintage_numbers"
  | "rct_asset_prices"
  | "registry_vintage_asset_prices";

type COMBINATION_RETURN = {
  rctAssetToAllocations?: Record<string, AllocationResponse>;
  registryVintageAssetToAllocations: Record<string, AllocationResponse>;
  summarizedNumbers: Record<AssetType, SummarizedBookNumbers>;
  summarizedRctNumbers: SummarizedBookNumbers;
  summarizedRegistryVintageNumbers: SummarizedBookNumbers;
  rctAssetPrices: GroupedPriceResponse;
  registryVintageAssetPrices: GroupedPriceResponse;
};

export type SummarizedBookNumbers = {
  value: number; // price allocated
  pending: number; // net pending [buy and sell]
  total: number; // value + net pending
  holding: number; // quantity of credits held
};

const toCc = (str: string): keyof COMBINATION_RETURN => str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase()) as keyof COMBINATION_RETURN;

export const getBookAssetToAllocations = (
  book: AdminBookResponse,
  take: "allocations" | "allocations:available" = "allocations",
  assetTypes: AssetType[] = [AssetType.RCT, AssetType.REGISTRY_VINTAGE],
): Record<string, AllocationResponse> => {
  const record: Record<string, AllocationResponse> = {};

  const filtered = (book?.ownerAllocations as GroupedAllocationWithNestedResponse)?.allocations?.filter(
    ({ asset, amountAvailable = 0 }) =>
      assetTypes?.includes(asset?.type) && take?.endsWith("available") ? amountAvailable > 0 : true,
  );

  for (const allocation of filtered || []) {
    record[allocation?.asset?.id] = allocation;
  }

  return record;
};

export const getAssetSummarizedBookNumbers = (
  book: AdminBookResponse,
  assetTypes: AssetType[] = [AssetType.RCT, AssetType.REGISTRY_VINTAGE],
): Undefinable<SummarizedBookNumbers> =>
  book.ownerAllocationsByAssetType
    ?.filter(({ assetType }) => assetTypes.includes(assetType))
    .reduce(
      (record, { groupedPrices, totalAmountAllocated }) => ({
        ...record,
        value: toNumber(groupedPrices?.totalPriceAllocated),
        pending: calculator(groupedPrices?.totalPricePendingBuy)
          ?.subtract(groupedPrices?.totalPricePendingSell)
          ?.calculate()
          ?.toNumber(),
        total: calculator(groupedPrices?.totalPriceAllocated)
          ?.add(groupedPrices?.totalPricePendingBuy)
          ?.subtract(groupedPrices?.totalPricePendingSell)
          ?.calculate()
          ?.toNumber(),
        holding: totalAmountAllocated,
      }),
      {} as SummarizedBookNumbers,
    );

export const getAssetTypeToSummarizedBookNumbers = (
  book: AdminBookResponse,
  assetTypes: AssetType[] = [AssetType.RCT, AssetType.REGISTRY_VINTAGE],
): Record<AssetType, SummarizedBookNumbers> => {
  const record = {} as Record<AssetType, SummarizedBookNumbers>;

  const filtered = book?.ownerAllocationsByAssetType?.filter(({ assetType }) => assetTypes.includes(assetType));

  for (const { assetType, groupedPrices, totalAmountAllocated } of filtered || []) {
    record[assetType] = {
      value: toNumber(groupedPrices?.totalPriceAllocated),
      pending: calculator(groupedPrices?.totalPricePendingBuy)
        ?.subtract(groupedPrices?.totalPricePendingSell)
        ?.calculate()
        ?.toNumber(),
      total: calculator(groupedPrices?.totalPriceAllocated)
        ?.add(groupedPrices?.totalPricePendingBuy)
        ?.subtract(groupedPrices?.totalPricePendingSell)
        ?.calculate()
        ?.toNumber(),
      holding: totalAmountAllocated,
    } as SummarizedBookNumbers;
  }

  return record as Record<AssetType, SummarizedBookNumbers>;
};

export const getAssetTypePrices = (
  book: AdminBookResponse,
  assetTypes: AssetType[] = [AssetType.RCT, AssetType.REGISTRY_VINTAGE],
): Record<AssetType, GroupedPriceResponse> => {
  const record = {} as Record<AssetType, GroupedPriceResponse>;

  const filtered = book?.ownerAllocationsByAssetType?.filter(({ assetType }) => assetTypes.includes(assetType));

  for (const { assetType, groupedPrices } of filtered || []) {
    record[assetType] = groupedPrices || {} as GroupedPriceResponse;
  }

  return record as Record<AssetType, GroupedPriceResponse>;
};

export const combineByBook = (book: AdminBookResponse, combineTypes: COMBINATION_TYPE[]): COMBINATION_RETURN => {
  const combinations = combineTypes
    ?.filter((type) => !type.endsWith(":available"))
    ?.reduce(
      (record, type) => ({
        ...(record as COMBINATION_RETURN),
        [toCc(type)]: undefined,
      }),
      {} as COMBINATION_RETURN,
    );

  if (combineTypes.includes("rct_asset_to_allocations")) {
    const value = getBookAssetToAllocations(book, "allocations", [AssetType.RCT]);
    combinations.rctAssetToAllocations = value;
  }

  if (combineTypes.includes("rct_asset_to_allocations:available")) {
    const value = getBookAssetToAllocations(book, "allocations:available", [AssetType.RCT]);
    combinations.rctAssetToAllocations = value;
  }

  if (combineTypes.includes("registry_vintage_asset_to_allocations")) {
    const value = getBookAssetToAllocations(book, "allocations", [AssetType.REGISTRY_VINTAGE]);
    combinations.registryVintageAssetToAllocations = value;
  }

  if (combineTypes.includes("registry_vintage_asset_to_allocations:available")) {
    const value = getBookAssetToAllocations(book, "allocations:available", [AssetType.REGISTRY_VINTAGE]);
    combinations.registryVintageAssetToAllocations = value;
  }

  if (combineTypes.includes("summarized_numbers")) {
    const value = getAssetTypeToSummarizedBookNumbers(book);
    combinations.summarizedNumbers = value;
  }

  if (combineTypes.includes("summarized_rct_numbers")) {
    const value = getAssetTypeToSummarizedBookNumbers(book, [AssetType.RCT]);
    combinations.summarizedRctNumbers = value?.rct;
  }

  if (combineTypes.includes("summarized_registry_vintage_numbers")) {
    const value = getAssetTypeToSummarizedBookNumbers(book, [AssetType.REGISTRY_VINTAGE]);
    combinations.summarizedRegistryVintageNumbers = value?.registry_vintage;
  }

  if (combineTypes.includes("rct_asset_prices")) {
    const value = getAssetTypePrices(book, [AssetType.RCT]);
    combinations.rctAssetPrices = value?.rct;
  }

  if (combineTypes.includes("registry_vintage_asset_prices")) {
    const value = getAssetTypePrices(book, [AssetType.REGISTRY_VINTAGE]);
    combinations.registryVintageAssetPrices = value?.registry_vintage;
  }

  return combinations;
};
