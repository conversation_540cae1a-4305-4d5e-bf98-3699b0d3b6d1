import { RctAssetResponse, VintageAssetResponse } from "@rubiconcarbon/shared-types";

export const getProductName = (assets: (RctAssetResponse | VintageAssetResponse)[]): string => {
  const isMultiple = assets?.length > 1;
  const isPortfolio = assets?.some((asset) => "rct" in asset);

  if (!isMultiple)
    if (isPortfolio) return (assets?.at?.(0) as RctAssetResponse)?.rct?.name;
    else {
      const vintage = (assets?.at?.(0) as VintageAssetResponse)?.projectVintage;
      return `${vintage?.project?.name} - (${vintage?.project?.registryProjectId} - ${vintage?.name})`;
    }
  else return "Multiple Products";
};
