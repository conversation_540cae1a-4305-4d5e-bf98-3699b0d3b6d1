import {
  PurchaseFlowType,
  RetirementStatus,
  RetirementType,
  TradeType,
  TransactionType,
  VintageAssetResponse,
  type AssetType,
  type TradeUpdatableStatus,
  type TransactionResponse,
  type TrimmedBookResponse,
  uuid,
  BookType,
  TrimmedProjectVintageResponse,
  CounterpartyRole,
} from "@rubiconcarbon/shared-types";
import { Transform, Type } from "class-transformer";
import { ArrayNotEmpty, IsDate, IsNotEmpty, Min, MinDate, ValidateIf, ValidateNested } from "class-validator";
import { PrimitiveTypeBook } from "@models/primitive-type-book";
import { isNothing, MaxOfField, toNumber } from "@rubiconcarbon/frontend-shared";
import { TransactionStatus, TransactionUpdateStatus } from "@constants/transaction-status";
import { PrimitiveTypeVintage } from "@models/primitive-type-vintage";
import { TIFEnum } from "@constants/tif.enum";

const minDate = new Date();
minDate.setHours(0, 0, 0, 0);

export enum AllTransactionType {
  PURCHASE = TransactionType.PURCHASE, // sales
  BUY = TradeType.BUY, // trade
  SELL = TradeType.SELL, // trade
  RETIREMENT = RetirementType.RETIREMENT, // retirement
  TRANSFER_OUTFLOW = RetirementType.TRANSFER_OUTFLOW, // retirement
}

export type TrimmedTransactionModel = {
  typeF?: string;
  productF?: string;
  updatedAtF?: string;
} & TransactionResponse;

export class Counterparty {
  constructor() {}

  @IsNotEmpty({ message: "Required" })
  id: uuid;

  name: string;

  isOnboarded: boolean;

  tradeConfirmEmails: string[] = [];

  isEnabled: boolean;

  createdAt?: Date;
}

export class TradeCounterparty {
  constructor() {}

  @ValidateNested()
  @IsNotEmpty({ message: "Required" })
  @Type(() => Counterparty)
  counterparty: Counterparty;

  @IsNotEmpty({ message: "Required" })
  role: CounterpartyRole;

  isPrimary: boolean = false;

  comments?: string;
}

export class NonTradeAssetDetails {
  constructor() {}

  @IsNotEmpty({ message: "Required" })
  id: uuid; // id is either from portfolio or project vintage

  sourceId?: uuid; // id of source book

  type: "portfolio" | "vintage";

  sourceType?: BookType; // type or source book

  name: string; // name is either from portfolio or project vintage

  sourceName?: string; // name of source book

  available: number;

  projectName?: string;

  registryProjectId?: string;

  wholeAsset?: TrimmedBookResponse | TrimmedProjectVintageResponse; // added for suspended and rct eligibilty tags
}

export class AssetOrder {
  constructor() {}

  _id: uuid = uuid(); // for list rendering performance only

  isASale?: boolean = false; // either trade or customer sales

  isAPurchase?: boolean = false; // customer sales

  @MaxOfField<AssetOrder>("amountAvailable", { message: "Quantity cannot exceed credits available" })
  @Min(1, { message: "Quantity must be greater than 0." })
  @IsNotEmpty({ message: "Required." })
  @Transform(({ value }) => (!isNothing(value, ["string"]) ? toNumber(value, { parserBlacklist: [","] }) : undefined))
  @ValidateIf(
    (o: AssetOrder) =>
      // it is a trade           // OR // [ it is a purchase or a non sale ] AND [it has non trade asset stuff] //
      (o.isASale && !o.isAPurchase) || ((o.isAPurchase || !o.isASale) && !!o?.supplementaryAssetDetails),
  )
  amount: string; // number

  @ValidateIf((o: AssetOrder) => !!o.isASale && !o.rawPrice)
  @IsNotEmpty({ message: "Required if Sub Total is empty." })
  @Transform(({ value }) =>
    !isNothing(value, ["string"]) ? toNumber(value, { parserBlacklist: ["$", ","] }) : undefined,
  )
  unitPrice?: string; // decimal

  @ValidateIf((o: AssetOrder) => !!o.isASale && !o.unitPrice)
  @IsNotEmpty({ message: "Required if Price is empty." })
  @Transform(({ value }) =>
    !isNothing(value, ["string"]) ? toNumber(value, { parserBlacklist: ["$", ","] }) : undefined,
  )
  rawPrice?: string; // decimal

  serviceFee?: string = ""; // decimal

  otherFee?: string = ""; // decimal

  // should be derived
  feeTotal?: string; // decimal

  // should be derived
  grandTotal?: string; // decimal

  amountAvailable?: number;

  @ValidateNested()
  @IsNotEmpty({ message: "Required" })
  @Type(() => NonTradeAssetDetails)
  @ValidateIf((o: AssetOrder) => !o?.projectVintage)
  supplementaryAssetDetails?: NonTradeAssetDetails;

  @ValidateNested()
  @Type(() => PrimitiveTypeBook)
  @ValidateIf((o: AssetOrder) => !!o?.isASale)
  source: PrimitiveTypeBook;

  rct?: TrimmedBookResponse;

  @ValidateNested()
  @Type(() => PrimitiveTypeVintage)
  @ValidateIf((o: AssetOrder) => !o?.isAPurchase)
  projectVintage?: PrimitiveTypeVintage;

  associatedVintages?: VintageAssetResponse[];
}

export class TradeOnly {
  constructor() {}

  @ValidateNested()
  @ArrayNotEmpty()
  @Type(() => TradeCounterparty)
  counterparties: TradeCounterparty[];

  totalValue: number;

  mtm?: string; // decimal

  @IsNotEmpty({ message: "Required." })
  tif?: TIFEnum;

  poid?: string;

  /**
   * tif = EOD (today's date at 23:59:99 PM)
   * tif = GTC (empty date)
   */
  goodUntilDate?: string; // date
}

export class SalesOnly {
  constructor(isTraderPreFill?: boolean) {
    this.isTraderPreFill = isTraderPreFill;
    if (isTraderPreFill) {
      this.flowType = PurchaseFlowType.PURCHASE_TO_RETIRE;
    }
  }

  isTraderPreFill?: boolean = false;

  totalValue: number;

  @IsNotEmpty({ message: "Required" })
  customerPortfolio: TrimmedBookResponse;

  @IsNotEmpty({ message: "Required" })
  flowType: PurchaseFlowType;

  needsRiskAdjustment: boolean;

  @IsNotEmpty({ message: "Required" })
  @ValidateIf((o: SalesOnly) => !!o?.customerPortfolio)
  productType?: AssetType;

  @ValidateIf((o: SalesOnly) => !!o?.paymentDueDate)
  @MinDate(minDate, { message: "Date cannot be in the past" })
  @IsDate({ message: "Invalid Date" })
  @Type(() => Date)
  paymentDueDate?: string; // date;
}

export class RetirementOnly {
  constructor() {}

  @IsNotEmpty({ message: "Required" })
  customerPortfolio: TrimmedBookResponse;

  @IsNotEmpty({ message: "Required" })
  @Type(() => Boolean)
  isPublic: boolean;

  @IsNotEmpty({ message: "Required" })
  @ValidateIf((o: RetirementOnly) => !!o?.customerPortfolio)
  productType?: AssetType;

  @IsNotEmpty({ message: "Required" })
  beneficiary: string;
}

export class TransferOutflowOnly {
  constructor() {}

  @IsNotEmpty({ message: "Required" })
  customerPortfolio: TrimmedBookResponse;

  registryAccount: string;
}

export class TransactionModel {
  constructor(isTraderPreFill?: boolean) {
    if (isTraderPreFill) this.sale = new SalesOnly(isTraderPreFill);
  }

  id?: uuid;

  uiKey: string;

  amount: number;

  @IsNotEmpty({ message: "Required." })
  type?: AllTransactionType;

  product?: string;

  @ValidateNested()
  @ArrayNotEmpty()
  @Type(() => AssetOrder)
  assets?: AssetOrder[];

  @ValidateIf((o: TransactionModel) => [TradeType.BUY, TradeType.SELL, TransactionType.TRADE].includes(o.type as any))
  @ValidateNested()
  @Type(() => TradeOnly)
  trade?: TradeOnly;

  @ValidateIf((o: TransactionModel) => o.type === AllTransactionType.PURCHASE)
  @ValidateNested()
  @Type(() => SalesOnly)
  sale?: SalesOnly;

  @ValidateIf((o: TransactionModel) => o.type === AllTransactionType.RETIREMENT)
  @ValidateNested()
  @Type(() => RetirementOnly)
  retirement?: RetirementOnly;

  @ValidateIf((o: TransactionModel) => o.type === AllTransactionType.TRANSFER_OUTFLOW)
  @ValidateNested()
  @Type(() => TransferOutflowOnly)
  transfer?: TransferOutflowOnly;

  @ValidateIf((o: TransactionModel) => !!o?.id)
  @IsNotEmpty({ message: "Required." })
  status: RetirementStatus | TransactionStatus | TransactionUpdateStatus | "paid" | "delivered" | undefined =
    TransactionUpdateStatus.FIRM;

  currentStatus: TransactionStatus;

  isDelivered?: boolean;

  isPaid?: boolean;

  updatableStatusOrder?: TradeUpdatableStatus[];

  @ValidateIf((o: TransactionModel) => o.type === AllTransactionType.RETIREMENT)
  @IsNotEmpty({ message: "Required." })
  memo: string = "";

  updatedAt?: string; // date

  dateStarted?: string; // date

  dateFinished?: string; // date

  inStatusAmend?: boolean;

  docsCount: number;
}

export class TransactionTableFormModel {
  constructor() {}

  @ValidateNested()
  @Type(() => TransactionModel)
  amends: TransactionModel[];
}
