import { uuid, BookType, AdminBookResponse, AssetTypeGroupedAllocationResponse } from "@rubiconcarbon/shared-types";
import { IsNotEmpty } from "class-validator";

type PlainBookLimit = {
  holdingAmountMax?: number;
  holdingAmountMin?: number;
  holdingPriceMax?: string; // decimal
  holdingPriceMin?: string; // decimal
};

export class PrimitiveTypeBook
  implements Pick<AdminBookResponse, "id" | "name" | "description" | "ownerAllocationsByAssetType">
{
  @IsNotEmpty({ message: "Required." })
  id: uuid;

  description?: string;

  isEnabled?: boolean;

  name: string;

  limit: PlainBookLimit;

  type: BookType;

  ownerAllocationsByAssetType?: AssetTypeGroupedAllocationResponse[];

  // Additional properties for portfolio calculations
  priceVintagesAllocated?: string; // decimal values as strings
  priceVintagesPendingBuy?: string; // decimal values as strings
  priceVintagesPendingSell?: string; // decimal values as strings
  amountVintagesAllocated?: number; // integer amounts as numbers
  amountVintagesPendingBuy?: number; // integer amounts as numbers
  amountVintagesPendingSell?: number; // integer amounts as numbers
}
