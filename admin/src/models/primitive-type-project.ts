import {
  TrimmedProjectResponse,
  uuid,
  CountryRequest,
  ProjectEligibilityAccreditation,
  ProjectSdgResponse,
  ProjectTypeResponse,
  RegistryResponse,
  ProjectEmissionsImpactType,
} from "@rubiconcarbon/shared-types";
import { IsNotEmpty } from "class-validator";

export class PrimitiveTypeProject implements TrimmedProjectResponse {
  constructor() {}

  @IsNotEmpty({ message: "Required" })
  id: uuid;

  country?: CountryRequest;

  eligibilityAccreditations: ProjectEligibilityAccreditation[];

  hasBalance: boolean;

  emissionsImpactType?: ProjectEmissionsImpactType;

  isScienceTeamApproved: boolean;

  name: string;

  integrityGradeScore?: number;

  projectDescription?: string;

  projectSDGs?: ProjectSdgResponse[];

  projectType: ProjectTypeResponse;

  rctStandard?: boolean;

  registry?: RegistryResponse;

  registryName?: string;

  registryProjectId: string;

  suspended?: boolean;
}
