import { Box } from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { Nullable } from "@rubiconcarbon/frontend-shared";
import dayjs, { Dayjs } from "dayjs";
import { useCallback, useState } from "react";

interface IFormField {
  error?: boolean;
  message?: string;
  value?: Dayjs;
}

export type DateRange = {
  dateFrom: IFormField;
  dateTo: IFormField;
};

interface DateRangeProps {
  dateFrom: Dayjs;
  dateTo: Dayjs;
  disabled?: boolean;
  shouldDisableDate?: (date: Dayjs, range?: "from" | "to") => boolean;
  onChange: (from: Dayjs, to: Dayjs) => void;
}

export default function DateRangePicker(props: DateRangeProps): JSX.Element {
  const { dateFrom, dateTo, disabled, shouldDisableDate, onChange } = props;
  const [from, setFrom] = useState<IFormField>({
    value: dateFrom,
    error: false,
    message: "",
  });
  const [to, setTo] = useState<IFormField>({
    value: dateTo,
    error: false,
    message: "",
  });

  const dateFromChangeHandler = useCallback(
    (selectedDate: Nullable<Dayjs>) => {
      const newFromDate = dayjs(selectedDate);
      setFrom({
        ...from,
        value: newFromDate,
      });

      onChange(newFromDate, to.value ?? dateTo);
    },
    [from, to.value, onChange, dateTo],
  );

  const dateToChangeHandler = useCallback(
    (selectedDate: Nullable<Dayjs>) => {
      const newToDate = dayjs(selectedDate);

      setTo({
        ...to,
        value: newToDate,
      });

      onChange(from.value ?? dateFrom, newToDate);
    },
    [to, from.value, onChange, dateFrom],
  );

  return (
    <Box display={"inline-flex"}>
      <DatePicker
        views={["year", "month", "day"]}
        format="DD/MM/YYYY"
        sx={{
          marginBottom: "5px",
        }}
        slotProps={{
          textField: {
            size: "small",
            error: from?.error,
            helperText: from?.message,
            sx: { width: "160px", "& .MuiOutlinedInput-root": { fontSize: "14px" } },
          },
        }}
        value={from.value}
        disabled={disabled}
        shouldDisableDate={(date: Dayjs) => shouldDisableDate?.(date, "from") ?? false}
        onChange={dateFromChangeHandler}
      />
      <Box sx={{ padding: "8px" }}> - </Box>
      <DatePicker
        views={["year", "month", "day"]}
        format="DD/MM/YYYY"
        sx={{
          marginBottom: "5px",
        }}
        slotProps={{
          textField: {
            size: "small",
            error: to?.error,
            helperText: to?.message,
            sx: { width: "160px", "& .MuiOutlinedInput-root": { fontSize: "14px" } },
          },
        }}
        value={to.value}
        disabled={disabled}
        shouldDisableDate={(date: Dayjs) => shouldDisableDate?.(date, "to") ?? false}
        onChange={dateToChangeHandler}
      />
    </Box>
  );
}
