import { Typography } from "@mui/material";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import { TrimmedProjectVintageResponse, uuid } from "@rubiconcarbon/shared-types";
import ProjectWithVintage from "../project-with-vintage/project-with-vintage";
import { getPortfolioColor } from "@utils/helpers/portfolio/get-portfolio-helper";
import PortfolioName from "../portfolio-name/portfolio-name";
import { CSSProperties } from "react";
import { AssetOrder } from "@models/transaction";

type ProductNameProps = {
  assets?: AssetOrder[];
  addTags?: {
    rct?: boolean;
    suspended?: boolean;
  };
  style?: CSSProperties;
};

const ProductName = ({ assets = [], addTags, style = {} }: ProductNameProps): JSX.Element => {
  const isMultiple = assets?.length > 1;
  const isPortfolio = assets?.some((asset) => "rct" in asset || !!asset?.rct);
  const { fontSize = "inherit", fontWeight = 300, ...rest } = style || {};

  if (!isMultiple)
    return (
      <>
        <Maybe condition={isPortfolio}>
          <PortfolioName
            name={assets?.at?.(0)?.rct?.name || ''}
            includeColorChip
            color={getPortfolioColor(assets?.at?.(0)?.rct?.id || '' as uuid)}
            sx={{ fontSize, fontWeight, ...rest }}
          />
        </Maybe>
        <Maybe condition={!isPortfolio}>
          <ProjectWithVintage
            vintage={assets?.at?.(0)?.projectVintage as unknown as TrimmedProjectVintageResponse}
            addTags={addTags}
            sx={{ fontSize, fontWeight, ...rest }}
          />
        </Maybe>
      </>
    );
  else return <Typography style={{ fontSize, fontWeight, ...rest }}>Multiple Products</Typography>;
};

export default ProductName;
