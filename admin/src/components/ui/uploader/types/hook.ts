import { Nullable } from "@rubiconcarbon/frontend-shared";
import { EncType } from "@/types/encoding-type";
import { FileType } from "@/types/file-type";
import { AxiosError, AxiosInstance, AxiosResponse } from "axios";
import { ChangeEvent, DragEvent, MouseEvent } from "react";

export type UploaderErrorType =
  | "INVALID_FILE_TYPE"
  | "FILE_TOO_BIG"
  | "EMPTY_FILE"
  | "TOO_MANY_FILES"
  | "UPLOAD_FAILURE";

export type GeneralUploadErrorType = Extract<UploaderErrorType, "TOO_MANY_FILES">;

export type FileDataError = {
  emptyFile?: boolean;
  invalidType?: boolean;
  fileTooBig?: boolean;
  uploadFailure?: boolean;
};

export type FileData = {
  file: File;
  progress: number;
  errors: FileDataError;
};

export type FileDataRecord = Record<string, FileData>;

export type OnFileUploadSuccessMetaData = {
  s3FileId?: string;
  response?: AxiosResponse<any>;
};

export type HeadlessUploaderProps = {
  inputId: string;
  api?: AxiosInstance; // overrides global http when passed
  encType?: EncType; // overrides api content type when passed
  maxFiles?: number;
  maxFileSize?: number;
  concurrentUploads?: number;
  allowedDimension?:
    | [number, number]
    | {
        size: [number, number];
        exclusive: "min" | "max";
      }; // for images
  allowedAspectRatio?: [number, number]; // for images
  allowedExtensions?: FileType[];
  useUniqueLink?: boolean;
  canUpload?: boolean;
  canDragAndDrop?: boolean;
  isS3Upload?: boolean;
  uploadOnFileChange?: boolean;
  hasInterrimStep?: boolean;
  clearOnFileUploadSuccess?: boolean;
  uploadMethod?: "post" | "put" | "patch";
  uploadLink: string | ((file: File) => Promise<string>); // only required prop
  uniqueFileKey?: (file: File) => string;
  onExposeUploadHandler?: (
    inputId: string,
    // eslint-disable-next-line no-unused-vars
    upload: Nullable<(event: MouseEvent<HTMLButtonElement>) => Promise<void>>,
  ) => void;
  onFileDrop?: (event: DragEvent<HTMLElement>) => void;
  onFileChange?: (event: ChangeEvent<HTMLInputElement>) => void;
  onFileRemoval?: (file: File) => Promise<boolean>;
  onFileUploadSuccess?: (file: File, metadata?: OnFileUploadSuccessMetaData) => void;
  onFileUploadError?: (error: AxiosError, file: File) => void;
  onUploadingStatusChange?: (status: boolean) => void;
  handleUploadRequest?: (defaultApiCall?: () => Promise<void>) => Promise<void>;
};
