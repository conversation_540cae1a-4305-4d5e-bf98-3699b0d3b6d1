import { GenericTableRowModel } from "./generic-table-row-model";
import { AllKeys } from "@rubiconcarbon/frontend-shared";
import { GenericTableColumn } from "./generic-table-column";

export type NestedColumnExportRecord<M> = Record<
  AllKeys<M>,
  {
    uniquenessLabel?: string;
    columns: GenericTableColumn<any>[];
  }
>;

export type GenericTableExport<M> = {
  filename: string | (() => string);
  nested?: NestedColumnExportRecord<M>;
  setClientCanExport: (value: boolean) => void;
  bindClientExport: (clientSideExport: () => void) => void;
  handleClientExport: () => void;
  onExport?: (rows: GenericTableRowModel<M>) => void;
};
