import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>act<PERSON><PERSON>, useMemo, useRef } from "react";
import { GenericTableColumn } from "./types/generic-table-column";
import { GenericTableRowModel } from "./types/generic-table-row-model";
import { uuid } from "@rubiconcarbon/shared-types";
import { Box, Paper, Portal, SxProps, Typography } from "@mui/material";
import { GenericTablePageable } from "./types/generic-table-pageable";
import { ActionButtonProps } from "../action-button/action-button-enhanced";
import { GenericTableSort } from "./types/generic-table-sort";
import GenericTableHeader from "./components/generic-table-header";
import { GenericTableLoading, GenericTableNoDataContent } from "./components/generic-table-row";
import GenericTableFooter from "./components/generic-table-footer";
import GenericTableBody from "./components/generic-table-body";
import { GenericTableGlobalSearch } from "./types/generic-table-global-search";
import { GenericTableServerActions } from "./types/generic-table-server-actions";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import { GenericTableExport } from "./types/generic-table-export";
import { createStore, PrimitiveAtom, Provider, useAtomValue, useSetAtom } from "jotai";
import { GenericTableRowToolBarOptions } from "./types/generic-table-row-toolbar-options";
import { Delay, NO_OP, px } from "@rubiconcarbon/frontend-shared";
import { Values } from "./hooks/use-generic-table-utility";
import { GenericTableOnSubmitReturn } from "./types/generic-table-on-submit-return";
import TableContainerUnit from "./components/table-container-unit";
import {
  baseColumnsAtom,
  columnsAtom,
  DEFAULT_PAGE_LIMIT,
  externalAtom,
  globalSearchAtom,
  pagedRowsAtom,
  paginationAtom,
  rowsAtom,
  serverActionsAtom,
  sortsAtom,
} from "./state";
import usePerformantEffect from "@/hooks/use-performant-effect";
import { UseEnhancedFormReturn } from "@/hooks/use-enhanced-form";

type GenericTableProps<D, M, C> = {
  id: string;
  columns: GenericTableColumn<M>[];
  pageableData?: GenericTablePageable<D>;
  elevation?: number;
  loading?: boolean;
  reloadingRow?: boolean;

  classes?: {
    root?: string | ((rows?: GenericTableRowModel<M>[], columns?: GenericTableColumn<M>[]) => string);
    header?: string | ((columns: GenericTableColumn<M>[]) => string);
    body?: string | ((rows: GenericTableRowModel<M>[], columns?: GenericTableColumn<M>[]) => string);
    footer?: string;

    headerRow?: string;
    bodyRow?: string | ((row: GenericTableRowModel<M>, columns?: GenericTableColumn<M>[]) => string);
    footerRow?: string;

    footerCell?: string;
  };

  styles?: {
    root?: SxProps | ((rows?: GenericTableRowModel<M>[], columns?: GenericTableColumn<M>[]) => SxProps);
    header?: SxProps | ((columns: GenericTableColumn<M>[]) => SxProps);
    body?: SxProps | ((rows: GenericTableRowModel<M>[], columns?: GenericTableColumn<M>[]) => SxProps);
    footer?: SxProps;

    headerRow?: SxProps;
    bodyRow?: SxProps | ((row: GenericTableRowModel<M>, columns?: GenericTableColumn<M>[]) => SxProps);
    footerRow?: SxProps;

    footerCell?: SxProps;
  };

  sort?: GenericTableSort<M>;
  dismissFormOnSubmit?: boolean;
  export?: Omit<GenericTableExport<M>, "handleClientExport">;
  expandOn?: {
    create?: boolean;
    edit?: boolean;
    mount?: boolean | ((row: GenericTableRowModel<M>) => boolean);
  };
  collapseOn?: {
    create?: boolean;
    edit?: boolean;
    mount?: boolean | ((row: GenericTableRowModel<M>) => boolean);
  };
  eager?: {
    expand?: boolean | ((row: GenericTableRowModel<M>) => boolean);
    collapse?: boolean | ((row: GenericTableRowModel<M>) => boolean);
  };
  pagination?: {
    rowsPerPageOptions?: number[];
    onPageNumberChange: (pageNumber: number) => void;
    onRowsPerPageChange: (rowsPerPage: number) => void;
  };
  globalSearch?: GenericTableGlobalSearch<M>;
  serverActions?: GenericTableServerActions;
  toolbarItems?: ReactNode;
  toolbarActionButtons?: PropsWithChildren<ActionButtonProps>[];
  rowToolbarOptions?: GenericTableRowToolBarOptions<M>;

  isMultiEditable?: boolean;
  appendNewRows?: boolean;
  isSelectable?: boolean | ((row: GenericTableRowModel<M>) => boolean);
  isExpandable?: boolean | ((row: GenericTableRowModel<M>) => boolean);
  rowId?: (string | uuid) | ((row: GenericTableRowModel<M>) => string | uuid);
  renderLoadingContent?: ReactNode;
  renderNoDataContent?: ReactNode;

  extensions?: C | ((row: M) => C);
  resetForm?: () => void;
  useForm?: () => UseEnhancedFormReturn<Values<M>>;
  bindAddRow?: (addRow: () => void) => void;
  bindCancelRowAmendment?: (cancelRowAmendment: (row?: GenericTableRowModel<M>) => void) => void;
  bindKeepRowAfterAmendment?: (keepRowAfterAmendment: (row: GenericTableRowModel<M>) => Promise<void>) => void;
  toRowModel?: (row: D) => GenericTableRowModel<M>;
  renderExpandContent?: (row: GenericTableRowModel<M>) => JSX.Element | Promise<JSX.Element>;

  onRowFormChange?: (row: M) => void;
  onFormSubmit?: FormEventHandler<HTMLFormElement>;
  onRowRemove?: (row: GenericTableRowModel<M>) => Promise<boolean>;
  onRowSubmit?: (row: GenericTableRowModel<M>) => Promise<GenericTableOnSubmitReturn<M> | void>;
};

const GenericTable = <D, M, C>(props: GenericTableProps<D, M, C>): JSX.Element => {
  const {
    id,
    columns,
    pageableData,
    elevation = 0,
    loading = false,
    reloadingRow = false,
    classes,
    styles,
    sort = {
      sorts: {},
      onColumSortChange: NO_OP,
    },
    dismissFormOnSubmit = true,
    export: ex,
    expandOn,
    collapseOn,
    eager,
    pagination = {},
    globalSearch,
    serverActions,
    toolbarItems,
    toolbarActionButtons = [],
    rowToolbarOptions,

    isMultiEditable,
    appendNewRows,
    isSelectable,
    isExpandable,
    rowId,
    renderLoadingContent = <GenericTableLoading />,
    renderNoDataContent = (
      <GenericTableNoDataContent>
        <Typography variant="body1" textAlign="center">
          No Data Found
        </Typography>
      </GenericTableNoDataContent>
    ),

    extensions,
    resetForm,
    useForm,
    bindAddRow,
    bindCancelRowAmendment,
    bindKeepRowAfterAmendment,
    toRowModel = (row: D): GenericTableRowModel<M> => row as GenericTableRowModel<M>,
    renderExpandContent,

    onRowFormChange,
    onFormSubmit,
    onRowRemove,
    onRowSubmit,
  } = props;

  const tableRoot = useRef<HTMLDivElement>(null);
  const store = useMemo(() => createStore(), []);
  const rows = useMemo(() => pageableData?.data?.map(toRowModel) || [], [pageableData?.data, toRowModel]);
  const page = useMemo(() => {
    if (serverActions?.paginate) return pageableData?.page || {};
    else
      return {
        ...(pageableData?.page || {}),
        limit: DEFAULT_PAGE_LIMIT,
      };
  }, [pageableData?.page, serverActions?.paginate]);

  const setColumns = useSetAtom(columnsAtom, { store });
  const setRows = useSetAtom(rowsAtom, { store });
  const setSorts = useSetAtom(sortsAtom, { store });
  const setExternal = useSetAtom(externalAtom, { store });
  const setGlobalSearch = useSetAtom(globalSearchAtom, { store });
  const setServerActions = useSetAtom(serverActionsAtom, { store });
  const setPagination = useSetAtom(paginationAtom, { store });

  usePerformantEffect(() => {
    setColumns(columns);
  }, [columns]);

  usePerformantEffect(() => {
    setRows(rows);
  }, [rows]);

  usePerformantEffect(() => {
    setSorts(sort);
  }, [sort]);

  usePerformantEffect(() => {
    if (globalSearch) setGlobalSearch(globalSearch);
  }, [globalSearch]);

  usePerformantEffect(() => {
    if (serverActions) setServerActions(serverActions);
  }, [serverActions]);

  usePerformantEffect(() => {
    setPagination({ ...page, ...pagination });
  }, [page, pagination]);

  usePerformantEffect(() => {
    setExternal({
      columns,
      rows,
      serverActions,
      sort,
      expandOn,
      collapseOn,
      dismissFormOnSubmit,
      isExpandable,
      isMultiEditable,
      appendNewRows,
      export: ex,
      pagination: { ...page, ...pagination },
      globalSearch,
      rowToolbarOptions,
      extensions: extensions as Record<string, any> | ((row?: any) => Record<string, any>) | undefined,
      resetForm,
      useForm: useForm as (() => UseEnhancedFormReturn<Values<any>>),
      bindAddRow,
      bindCancelRowAmendment,
      bindKeepRowAfterAmendment,
      onRowFormChange,
      onFormSubmit,
      onRowRemove,
      onRowSubmit,
    });
  }, [
    appendNewRows,
    bindAddRow,
    bindCancelRowAmendment,
    bindKeepRowAfterAmendment,
    collapseOn,
    columns,
    dismissFormOnSubmit,
    ex,
    expandOn,
    extensions,
    globalSearch,
    isExpandable,
    isMultiEditable,
    onFormSubmit,
    onRowFormChange,
    onRowRemove,
    onRowSubmit,
    page,
    pagination,
    resetForm,
    rowToolbarOptions,
    rows,
    serverActions,
    setExternal,
    sort,
    useForm,
  ]);

  const pagedRows = useAtomValue<PrimitiveAtom<GenericTableRowModel<M>>[]>(pagedRowsAtom, { store });
  const tableColumns = useAtomValue(baseColumnsAtom, { store });

  const {
    root: rootClass = "",
    header: headerClass = "",
    body: bodyClass = "",
    footer: footerClass = "",
    headerRow: headerRowClass = "",
    bodyRow: bodyRowClass = "",
    footerRow: footerRowClass = "",
    footerCell: footerCellClass = "",
  } = classes || {};
  const {
    root: rootStyle = {},
    header: headerStyle = {},
    body: bodyStyle = {},
    footer: footerStyle = {},
    headerRow: headerRowStyle = {},
    bodyRow: bodyRowStyle = {},
    footerRow: footerRowStyle = {},
    footerCell: footerCellStyle = {},
  } = styles || {};

  const rc = useMemo(
    () =>
      typeof rootClass === "function"
        ? rootClass(
            pagedRows.map((rowAtom) => store.get(rowAtom)),
            tableColumns,
          )
        : rootClass,
    [rootClass, store, tableColumns, pagedRows],
  );
  const rs = useMemo(
    () =>
      typeof rootStyle === "function"
        ? rootStyle(
            pagedRows.map((rowAtom) => store.get(rowAtom)),
            tableColumns,
          )
        : rootStyle,
    [rootStyle, store, tableColumns, pagedRows],
  );

  return (
    <Provider store={store}>
      <Box width="100%" ref={tableRoot}></Box>
      <Portal container={tableRoot?.current}>
        <Paper
          id={id}
          elevation={elevation}
          component={onFormSubmit ? "form" : "div"}
          {...px({ onSubmit: onFormSubmit })}
        >
          <TableContainerUnit className={rc} sx={rs}>
            <GenericTableHeader
              loading={loading || reloadingRow || !rows?.length}
              actionButtons={toolbarActionButtons}
              toolbarItems={toolbarItems}
              headerClass={headerClass}
              headerStyle={headerStyle}
            />

            <GenericTableBody
              loading={loading}
              reloadingRow={reloadingRow}
              expandOn={expandOn}
              collapseOn={collapseOn}
              eager={eager}
              isSelectable={isSelectable}
              isExpandable={isExpandable}
              rowId={rowId}
              headerRowClass={headerRowClass}
              headerRowStyle={headerRowStyle}
              bodyClass={bodyClass}
              bodyRowClass={bodyRowClass}
              bodyStyle={bodyStyle}
              bodyRowStyle={bodyRowStyle}
              renderLoadingContent={renderLoadingContent}
              renderNoDataContent={renderNoDataContent}
              renderExpandContent={renderExpandContent}
            />

            <Maybe condition={!loading}>
              <Delay ms={500}>
                <GenericTableFooter
                  footerClass={footerClass}
                  footerRowClass={footerRowClass}
                  footerCellClass={footerCellClass}
                  footerStyle={footerStyle}
                  footerRowStyle={footerRowStyle}
                  footerCellStyle={footerCellStyle}
                />
              </Delay>
            </Maybe>
          </TableContainerUnit>
        </Paper>
      </Portal>
    </Provider>
  );
};

export default GenericTable;
