import { useStore, useAtomValue } from "jotai";
import { useMemo } from "react";
import { GenericTableExternal } from "../types/generic-table-external";
import { GenericTableRowModel } from "../types/generic-table-row-model";
import { DeepMap, DeepPartial, FieldErrors } from "react-hook-form";
import { Values } from "./use-generic-table-utility";
import { activeRowsAtom, externalAtom } from "../state";
import { UseEnhancedFormReturn } from "@/hooks/use-enhanced-form";

type UseGenericTableRowStateReturn<M> = {
  creating: boolean;
  editing: boolean;
  deleting: boolean;
  amending: boolean;
  dirty: boolean;
  submitting: boolean;
  isRowActive: boolean;
  disabled: boolean;
  form?: UseEnhancedFormReturn<Values<M>>;
  errors?: FieldErrors<Values<M>>;
  activeRow: GenericTableRowModel<M>;
  dirtyFields?: Partial<Readonly<DeepMap<DeepPartial<Values<M>>, boolean>>>;
};

const useGenericTableRowState = <M>(row: GenericTableRowModel<M>): UseGenericTableRowStateReturn<M> => {
  const store = useStore();

  const { useForm } = useAtomValue<GenericTableExternal<M>>(externalAtom, { store }) || {};
  const activeRows = useAtomValue(activeRowsAtom, { store });
  const amendingIds = (activeRows || [])?.map(({ id }) => id);

  const form = useForm?.();
  const { isDirty: dirty, isSubmitting: submitting, dirtyFields, errors } = form?.formState || {};
  const amending = useMemo(
    () =>
      !!activeRows?.length &&
      activeRows?.every((row: GenericTableRowModel<M>) => row?.creating || row?.editing || row?.deleting),
    [activeRows],
  );
  const isRowActive = useMemo(() => amending && amendingIds?.includes(row?.id), [amending, amendingIds, row?.id]);
  const activeRow = useMemo(
    () => (amending ? activeRows?.find(({ id }) => id === row?.id) : null),
    [activeRows, amending, row?.id],
  ) as GenericTableRowModel<M>;

  return {
    creating: !!activeRow?.creating,
    editing: !!activeRow?.editing,
    deleting: !!activeRow?.deleting,
    amending,
    dirty: !!dirty,
    submitting: !!submitting,
    isRowActive,
    disabled: amending && !isRowActive,
    form,
    errors,
    activeRow,
    dirtyFields,
  };
};

export default useGenericTableRowState;
