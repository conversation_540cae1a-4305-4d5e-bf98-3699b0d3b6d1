import { <PERSON><PERSON><PERSON>, Divider, <PERSON>arProgress, SxProps } from "@mui/material";
import { GenericTableRowModel } from "../types/generic-table-row-model";
import { <PERSON><PERSON><PERSON><PERSON>, Maybe, pickFromRecord, px } from "@rubiconcarbon/frontend-shared";
import { useMemo, PropsWithChildren, useState, useEffect, useCallback } from "react";
import GenericTableField from "./generic-table-field";
import { useAtomValue, useSetAtom, useStore } from "jotai";
import { GenericTableColumn } from "../types/generic-table-column";
import useGenericTableRowState from "../hooks/use-generic-table-row-state";
import { GenericTableExternal } from "../types/generic-table-external";
import useGenericTableRowActions from "../hooks/use-generic-table-row-actions";
import { Values } from "../hooks/use-generic-table-utility";
import { useGetSetState } from "react-use";
import TableRowUnit from "./table-row-unit";
import TableCellUnit from "./table-cell-unit";
import { GenericTableFieldSizeEnum } from "../constants/generic-table-field-size.enum";
import { columnsAtom, expandedRowsAtom, externalAtom, rowColumnsAtom, updateExpandedRowsAtom } from "../state";
import usePerformantEffect from "@/hooks/use-performant-effect";

import classes from "../styles/generic-table-row.module.scss";

type DSS<M> = (value?: any, row?: GenericTableRowModel<M>) => SxProps;
type DSW<M> = (value?: any, row?: GenericTableRowModel<M>) => number | GenericTableFieldSizeEnum;

type GenericTableRowProps<M> = {
  row: GenericTableRowModel<M>;
  reloadingRow?: boolean;
  expandOn?: {
    create?: boolean;
    edit?: boolean;
    mount?: boolean | ((row: GenericTableRowModel<M>) => boolean);
  };
  collapseOn?: {
    create?: boolean;
    edit?: boolean;
    mount?: boolean | ((row: GenericTableRowModel<M>) => boolean);
  };
  eager?: {
    expand?: boolean | ((model: GenericTableRowModel<M>) => boolean);
    collapse?: boolean | ((model: GenericTableRowModel<M>) => boolean);
  };
  isExpandable?: boolean | ((model: GenericTableRowModel<M>) => boolean);
  bodyRowClass?: string | ((row: GenericTableRowModel<M>, columns?: GenericTableColumn<M>[]) => string);
  bodyRowStyle?: SxProps | ((row: GenericTableRowModel<M>, columns?: GenericTableColumn<M>[]) => SxProps);
  renderExpandContent?: (model: GenericTableRowModel<M>) => JSX.Element | Promise<JSX.Element>;
};

export const GenericTableDivider = (): JSX.Element => (
  <TableRowUnit className={`${classes.TableRow} ${classes.Divider}`}>
    <TableCellUnit width="100%" maxWidth="100%" padding={0}>
      <Divider />
    </TableCellUnit>
  </TableRowUnit>
);

export const GenericTableLoading = (): JSX.Element => (
  <TableRowUnit id="loading-table-data" className={`${classes.TableRow} ${classes.TableRowLoading}`}>
    <TableCellUnit width="100%" maxWidth="100%" padding={0}>
      <LinearProgress />
    </TableCellUnit>
  </TableRowUnit>
);

export const GenericTableNoDataContent = ({ children }: PropsWithChildren): JSX.Element => (
  <TableRowUnit id="no-table-data" className={classes.TableRow}>
    <TableCellUnit width="100%" maxWidth="100%" padding="20px 0">
      {children}
    </TableCellUnit>
  </TableRowUnit>
);

const GenericTableRow = <M,>(props: GenericTableRowProps<M>): JSX.Element => {
  const {
    row,
    reloadingRow = false,
    expandOn,
    collapseOn,
    eager,
    isExpandable = false,
    bodyRowClass,
    bodyRowStyle,
    renderExpandContent,
  } = props;

  const store = useStore();

  const expanded = useAtomValue(expandedRowsAtom, { store });
  const rowColumns = useAtomValue<GenericTableColumn<M>[]>(rowColumnsAtom, { store });
  const tableColumns = useAtomValue(columnsAtom, { store });
  const { dismissFormOnSubmit, useForm } = useAtomValue<GenericTableExternal<M>>(externalAtom, { store });

  const updateExpandedRows = useSetAtom(updateExpandedRowsAtom, { store });

  const { setAmendingRow } = useGenericTableRowActions<M>();
  const { activeRow, creating, editing, isRowActive } = useGenericTableRowState<M>(row);

  const [renderedContent, setRenderedContent] = useState(null);
  const [rowFormValue, setRowFormValue] = useGetSetState<GenericTableRowModel<M>>(activeRow);

  const { expand: ee = false, collapse: ec = false } = eager || {};
  const { create: eoc = false, edit: eoe = false, mount: eom = false } = expandOn || {};
  const { mount: com = false } = collapseOn || {};

  const queriedRow = useMemo(() => (isRowActive ? rowFormValue() : row), [isRowActive, rowFormValue, row]);

  const canExpand = isExpandable
    ? typeof isExpandable === "boolean"
      ? isExpandable
      : isExpandable?.(queriedRow)
    : false;
  const eagerExpand = canExpand && !!ee ? (typeof ee === "boolean" ? ee : ee(queriedRow)) : false;
  const eagerCollapse = canExpand && !!ec ? (typeof ec === "boolean" ? ec : ec(queriedRow)) : false;
  const expand = useMemo(
    () => (isRowActive && ((creating && eoc) || (editing && eoe))) || expanded?.[queriedRow?.id],
    [creating, editing, eoc, eoe, expanded, isRowActive, queriedRow?.id],
  );
  const form = useForm?.();
  const { watch } = form || {};

  const brc = useMemo(
    () => (typeof bodyRowClass === "function" ? bodyRowClass(queriedRow, tableColumns) : bodyRowClass),
    [bodyRowClass, queriedRow, tableColumns],
  );
  const brs = useMemo(
    () => (typeof bodyRowStyle === "function" ? bodyRowStyle(queriedRow, tableColumns) : bodyRowStyle),
    [bodyRowStyle, queriedRow, tableColumns],
  );

  useEffect(() => {
    const subscription = watch?.((value) => {
      const v = value as Values<M>;
      const rowFormValue = v?.amends?.find((amend) => amend?.id === row?.id) as GenericTableRowModel<M>;
      setRowFormValue(rowFormValue);
    });
    return (): void => subscription?.unsubscribe();
  }, [watch, row?.id, setRowFormValue]);

  useEffect(() => {
    const renderContent = async (): Promise<void> => {
      if (renderExpandContent && expand) {
        const content = await renderExpandContent(queriedRow);
        setRenderedContent(content);
      }
    };

    renderContent();
  }, [expand, queriedRow, renderExpandContent]);

  useEffect(() => {
    if (dismissFormOnSubmit && !form?.formState?.isSubmitting && form?.formState?.isSubmitSuccessful)
      setTimeout(() => setAmendingRow("remove", queriedRow), 200);
  }, [
    dismissFormOnSubmit,
    form?.formState?.isSubmitSuccessful,
    form?.formState?.isSubmitting,
    queriedRow,
    setAmendingRow,
  ]);

  usePerformantEffect(() => {
    if (!!eom && !expanded?.[row?.id]) {
      if (typeof eom === "function") {
        const expand = eom(row);
        if (expand) updateExpandedRows({ id: row?.id });
      } else updateExpandedRows({ id: row?.id });
    }

    if (!!com && !!expanded?.[row?.id]) {
      if (typeof com === "function") {
        const collapse = com(row);
        if (collapse) updateExpandedRows({ id: row?.id });
      } else updateExpandedRows({ id: row?.id });
    }
  }, [eom, com, row]);

  const toggleExpand = useCallback((): void => {
    updateExpandedRows({ id: queriedRow?.id });
  }, [queriedRow?.id, updateExpandedRows]);

  return (
    <>
      <Maybe condition={isRowActive && reloadingRow}>
        <GenericTableLoading />
      </Maybe>
      <TableRowUnit
        className={`${classes.TableRow} ${eagerExpand ? `${classes.TableRowHover}` : ""} ${brc}`}
        sx={brs}
        {...px({ onClick: eagerExpand && !isRowActive && ((): void => toggleExpand()) }, [false])}
      >
        {rowColumns.map((column, index) => {
          const {
            field,
            type,
            creatable = true,
            editable = true,
            minWidth,
            width,
            maxWidth,
            fixedWidth,
            dataCellClass,
            dataCellStyle,
          } = column;
          const amendable = isRowActive && ((creatable && creating) || (editable && editing));

          const dc =
            typeof dataCellClass === "function"
              ? dataCellClass(pickFromRecord<M>(queriedRow, [field as AllKeys<M>], true)[field as unknown as keyof M], queriedRow)
              : dataCellClass;
          const ds =
            typeof dataCellStyle === "function"
              ? (dataCellStyle as DSS<M>)(
                  pickFromRecord<M>(queriedRow, [field as AllKeys<M>], true)[field as unknown as keyof M],
                  queriedRow,
                )
              : dataCellStyle;
          const mnw =
            typeof minWidth === "function"
              ? (minWidth as DSW<M>)(
                  pickFromRecord<M>(queriedRow, [field as AllKeys<M>], true)[field as unknown as keyof M],
                  queriedRow,
                )
              : minWidth;
          const w =
            typeof width === "function"
              ? (width as DSW<M>)(
                  pickFromRecord<M>(queriedRow, [field as AllKeys<M>], true)[field as unknown as keyof M],
                  queriedRow,
                )
              : width;
          const mxw =
            typeof maxWidth === "function"
              ? (maxWidth as DSW<M>)(
                  pickFromRecord<M>(queriedRow, [field as AllKeys<M>], true)[field as unknown as keyof M],
                  queriedRow,
                )
              : maxWidth;

          return (
            <TableCellUnit
              key={`${field}-${type}-${index}`}
              name={field}
              fixedWidth={fixedWidth}
              className={dc}
              sx={{
                ...ds,
                minWidth: mnw,
                width: w,
                maxWidth: mxw,
                ...px({ flexGrow: fixedWidth && 0, flexShrink: fixedWidth && 0 }, [undefined, null]),
              }}
            >
              <GenericTableField field={field} editing={amendable} inRow row={queriedRow} />
            </TableCellUnit>
          );
        })}
      </TableRowUnit>
      <GenericTableDivider />
      <Maybe condition={canExpand && !!renderExpandContent}>
        <>
          <TableRowUnit
            className={`${classes.TableRow} ${classes.TableRowCollapse} ${eagerCollapse ? `${classes.TableRowHover}` : ""}`}
            {...px({ onClick: eagerCollapse && !isRowActive && ((): void => toggleExpand?.()) }, [false])}
          >
            <TableCellUnit width="100%" maxWidth="100%" padding={0}>
              <Collapse in={expand} timeout="auto" unmountOnExit>
                {renderedContent}
              </Collapse>
            </TableCellUnit>
          </TableRowUnit>
          <Maybe condition={expand}>
            <GenericTableDivider />
          </Maybe>
        </>
      </Maybe>
    </>
  );
};

export default GenericTableRow;
