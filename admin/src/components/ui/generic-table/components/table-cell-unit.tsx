import { Box, BoxProps, Stack } from "@mui/material";
import { isNothing, omitFromRecord } from "@rubiconcarbon/frontend-shared";
import { useAtom, useStore } from "jotai";
import { forwardRef, PropsWithChildren, useMemo } from "react";
import { useEnsuredForwardedRef, useMeasure } from "react-use";
import usePerformantEffect from "@/hooks/use-performant-effect";
import { observedColumnWidthAtomFamily } from "../state";

type TableCellUnitProps = {
  name?: string; // column name
  fixedWidth?: boolean;
} & BoxProps;

const TableCellUnit = forwardRef<HTMLDivElement, PropsWithChildren<TableCellUnitProps>>(
  (
    { children, name = "", fixedWidth = false, sx, ...rest },
    ref,
  ): JSX.Element => {
    const store = useStore();
    const [observedWidth, set] = useAtom(observedColumnWidthAtomFamily(name), { store });

    const guranteedRef = useEnsuredForwardedRef(ref as any);
    const [measureRef, { width }] = useMeasure();

    const combinedRef = (node: HTMLDivElement): void => {
      if (node) measureRef(node);
      guranteedRef.current = node;
    };

    const sxWidth = typeof sx === 'object' && sx !== null ? (sx as any)?.width : undefined;
    const sxMinWidth = typeof sx === 'object' && sx !== null ? (sx as any)?.minWidth : undefined;
    const sxMaxWidth = typeof sx === 'object' && sx !== null ? (sx as any)?.maxWidth : undefined;

    const trueWidth = useMemo(() => {
      return Math.max(sxWidth || 0, observedWidth || 0);
    }, [observedWidth, sxWidth]);

    usePerformantEffect(() => {
      set(width);
    }, [width]);

    return (
      <Box
        data-id="table-cell"
        role="cell"
        component={Stack}
        ref={combinedRef}
        padding={1}
        sx={{
          boxSizing: "content-box",
          justifyContent: "center",
          ...omitFromRecord(sx as any, ["minWidth", "width", "maxWidth"] as any),
        }}
        flexGrow={1}
        flexShrink={1}
        flexBasis={fixedWidth ? sxWidth : !isNothing(trueWidth) ? trueWidth : "auto"}
        minWidth={sxMinWidth || sxWidth || "none"}
        maxWidth={sxMaxWidth || "none"}
        {...rest}
      >
        {children}
      </Box>
    );
  },
);

TableCellUnit.displayName = "TableCellUnit";

export default TableCellUnit;
