import React, {
  useState,
  useEffect,
  useMemo,
  SyntheticEvent,
  ChangeEvent,
  Dispatch,
  SetStateAction,
  KeyboardEvent,
} from "react";
import {
  AdminProjectQueryResponse,
  ProjectSearchResponse,
  ProjectVintageRequest,
  AdminProjectVintageResponse,
  uuid,
} from "@rubiconcarbon/shared-types";
import { Box, TextField, Typography, Autocomplete, Stack, IconButton, useMediaQuery, Skeleton } from "@mui/material";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import CheckIcon from "@mui/icons-material/Check";
import CancelIcon from "@mui/icons-material/Cancel";
import COLORS from "@components/ui/theme/colors";
import dayjs, { Dayjs } from "dayjs";
import { isEmpty, isFinite } from "lodash";
import { DateRange } from "@mui/icons-material";
import YearRangePicker from "@components/ui/year-range/year-range";
import DateRangePicker from "@components/ui/date-range/date-range";
import GenericDialog from "@components/ui/generic-dialog/generic-dialog";
import { Nullable, useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import { useLogger } from "@providers/logging";
import { UseAutoCompleteOptionsReturnEntry } from "@hooks/use-auto-complete-options";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import useAutoCompleteOptionsAsync from "@hooks/use-auto-complete-options-async";
import usePerformantEffect from "@/hooks/use-performant-effect";
import { useToggle } from "react-use";

import classes from "../styles/component.module.scss";
import dialogClasses from "../styles/dialog.module.scss";

interface DateRange {
  from?: Date;
  to?: Date;
}

type AddNewVintageFormProps = {
  projectId: uuid;
  submitting: boolean;
  setCanSumbit: Dispatch<SetStateAction<boolean>>;
  setSubmitting: Dispatch<SetStateAction<boolean>>;
  onSubmitSuccess: (vintage: AdminProjectVintageResponse) => void;
};

type DateData = {
  value: string;
  range: DateRange;
};

const InitialDateData = {
  value: "",
  fValue: "",
  range: {},
  fRange: {},
};

const getDateFromStr = (input: string): Date => {
  const [day, month, year] = input.split("/");
  return new Date(+year, +month - 1, +day);
};

const buildDateRange = (dateFrom: Dayjs, dateTo: Dayjs): string => {
  if (dateFrom?.isValid() && dateTo?.isValid() && !dateFrom.isSame(dateTo)) {
    return `${dayjs(dateFrom).format("MM/DD/YYYY")} - ${dayjs(dateTo).format("MM/DD/YYYY")}`;
  } else if (dateFrom?.isValid()) {
    return `${dayjs(dateFrom).format("MM/DD/YYYY")}`;
  } else if (dateTo?.isValid()) {
    return `${dayjs(dateTo).format("MM/DD/YYYY")}`;
  }
  return "";
};

const buildYearRange = (dateFrom: Dayjs, dateTo: Dayjs): string => {
  if (dateFrom?.isValid() && dateTo?.isValid() && dateFrom.year() !== dateTo.year()) {
    return `${dayjs(dateFrom).year()} - ${dayjs(dateTo).year()}`;
  } else if (dateFrom?.isValid()) {
    return `${dayjs(dateFrom).year()}`;
  } else if (dateTo?.isValid()) {
    return `${dayjs(dateTo).year()}`;
  }
  return "";
};

const extractDatesFromRange = (input: string): DateRange => {
  if (isEmpty(input)) {
    return {};
  }

  const splitInput = input.split("-");
  if (!!splitInput && splitInput.length === 2) {
    const from = splitInput[0].trim();
    const to = splitInput[1].trim();
    //years range
    if (isFinite(+splitInput[0].trim()) && isFinite(+splitInput[1].trim())) {
      return {
        from: new Date(`01/01/${from}`),
        to: from === to ? undefined : new Date(`12/31/${to}`),
      };
    } else {
      //dates range
      return {
        from: getDateFromStr(from),
        to: from === to ? undefined : getDateFromStr(to),
      };
    }
  }

  if (isFinite(+input)) {
    return {
      from: new Date(`01/01/${input}`),
      to: new Date(`12/31/${input}`),
    };
  }

  const inputDate = getDateFromStr(input);
  if (dayjs(inputDate).isValid()) {
    return {
      from: inputDate,
      to: new Date(`12/31/${inputDate.getFullYear()}`),
    };
  }

  return {};
};

const extractProjectLabel = (project: Partial<ProjectSearchResponse> = {} as any): string =>
  `${project?.registryProjectId} - ${project?.name}`;

const AddNewVintageForm = ({
  projectId,
  submitting,
  setCanSumbit,
  setSubmitting,
  onSubmitSuccess,
}: AddNewVintageFormProps): JSX.Element => {
  const { logger } = useLogger();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const shouldStackDateField = useMediaQuery("(max-width: 799px)");

  const [openDialog, setOpenDialog] = useState<boolean>(false);
  const [projectSearchTerm, setProjectSearchTerm] = useState<string>("");
  const [formProject, setFormProject] = useState<UseAutoCompleteOptionsReturnEntry>();
  const [formVintage, setFormVintage] = useState<{
    name: string;
    interval: string;
  }>();
  const [vintageData, setVintageData] = useState<DateData>(InitialDateData);
  const [editingVintage, setEditingVintage] = useState<boolean>(false);
  const [requestBody, setRequestBody] = useState<ProjectVintageRequest>();
  const [open, setOpen] = useToggle(false);

  const { trigger: getProject, isMutating: gettingProject } = useTriggerRequest<AdminProjectQueryResponse>({
    url: "/admin/projects",
    queryParams: {
      ids: [projectId],
      limit: 500,
    },
    swrOptions: {
      onSuccess: (response) => {
        const data = response?.data || [];
        const option: UseAutoCompleteOptionsReturnEntry = {
          label: extractProjectLabel(data?.at(0)),
          value: data?.at(0)?.id,
        };

        setFormProject(option);
      },
      onError: (error: any) => {
        enqueueError(`Unable to fetch project for id ${projectId}.`);
        logger.error(
          `Unable to fetch project for id ${projectId} in Add Vintage Modal in Trades: ${error?.message}`,
          {},
        );
      },
    },
  });

  const projectOptions = useAutoCompleteOptionsAsync<ProjectSearchResponse, ProjectSearchResponse>({
    q: projectSearchTerm,
    keys: ["id", "name", "registryProjectId"],
    label: (entry) => extractProjectLabel(entry),
    value: (entry) => entry?.id,
    request: {
      url: "admin/projects/search",
      queryParams: {
        name: true,
        id: true,
        fuzzy: true,
        limit: 50,
      },
      swrOptions: {
        onError: (error: any): void => {
          setProjectSearchTerm("");
          enqueueError(`Unable to search projects for search term ${projectSearchTerm}.`);
          logger.error(
            `Unable to search projects for search term ${projectSearchTerm} in Add Vintage Modal in Trade. Error: ${error?.message}`,
            {},
          );
        },
      },
    },
    postTransform: (data: UseAutoCompleteOptionsReturnEntry[]) =>
      data?.sort((a, b) => a?.label?.localeCompare(b?.label)),
  });

  useEffect(() => {
    if (projectId) setTimeout(async () => await getProject());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectId]);

  usePerformantEffect(() => {
    if (!projectSearchTerm) setOpen(false);
    else
      setTimeout(() => {
        setOpen(true);
      }, 500);
  }, [projectSearchTerm]);

  const { trigger: createVintage } = useTriggerRequest<AdminProjectVintageResponse, ProjectVintageRequest>({
    url: "admin/project-vintages",
    method: "post",
    requestBody,
    swrOptions: {
      onSuccess: (data) => {
        setSubmitting(false);
        enqueueSuccess("Successfully created new vintage");
        onSubmitSuccess(data);
      },
      onError: (error: any) => {
        setSubmitting(false);

        if (error?.response?.data?.message) {
          enqueueError(
            Array.isArray(error.response.data.message)
              ? error.response.data.message.join(", ")
              : error.response.data.message,
          );
        } else enqueueError("Unable to create new vintage");
      },
    },
  });

  const toInterval = (value: string): DateRange => extractDatesFromRange(value);

  const validationMessage = useMemo(() => {
    const dates = vintageData?.range;

    if (!dayjs(dates?.from).isValid()) return "Please select a valid start date";

    if (!dayjs(dates.to).isValid()) return "Please select a valid end date";

    if (dates?.from && dates?.to && dayjs(dates.from).isValid() && dayjs(dates.to).isValid() && dates.from > dates.to)
      return "End date should be greater than start date";

    return "";
  }, [vintageData?.range]);

  const noProjectPicked = useMemo(() => !formProject?.value, [formProject?.value]);

  useEffect(() => {
    setRequestBody((previous) => previous ? {
      ...previous,
      projectId: formProject?.value || '',
      name: previous.name || '',
      interval: previous.interval || '',
    } : {
      projectId: formProject?.value || '',
      name: '',
      interval: '',
    });
  }, [formProject]);

  useEffect(() => {
    setRequestBody((previous) => previous ? {
      ...previous,
      name: formVintage?.name || '',
      interval: formVintage?.interval || '',
    } : {
      name: formVintage?.name || '',
      interval: formVintage?.interval || '',
      projectId: formProject?.value || undefined,
    });
  }, 
  // eslint-disable-next-line react-hooks/exhaustive-deps
  [formVintage]);

  useEffect(() => {
    setCanSumbit(!!formProject?.value && !!formVintage?.name);
  }, [formProject?.value, formVintage?.name, setCanSumbit]);

  useEffect(() => {
    setOpenDialog(submitting);
  }, [submitting]);

  const handleKeyDown = (event: KeyboardEvent): void => {
    if (event.key === "Escape") setOpen(false);
  };

  const handleClose = (): void => setOpen(false);

  const handleProjectSearch = (event: SyntheticEvent, value: string, reason: "input" | "clear"): void => {
    event?.preventDefault();

    if (reason === "input") {
      setProjectSearchTerm(value);
    }
    if (reason === "clear") {
      setProjectSearchTerm("");
      setFormProject({ label: "", value: null });
    }

    setOpen(false);
  };

  const handleProjectSelectionChange = (event: SyntheticEvent<Element, Event>, value: Nullable<UseAutoCompleteOptionsReturnEntry>): void => {
    event?.preventDefault();
    if (value) setFormProject(value);
  };

  const handleVintageChange = (event: ChangeEvent<HTMLInputElement>): void => {
    event?.preventDefault();
    setFormVintage((previous) => previous ? { ...previous, name: event.target.value } : { name: event.target.value, interval: '' });
  };

  const handleYearRange = (yearFrom: Dayjs, yearTo: Dayjs): void => {
    const value = buildYearRange(yearFrom, yearTo);
    setVintageData({ value, range: toInterval(value) });
  };

  const handleDateRange = (dateFrom: Dayjs, dateTo: Dayjs): void => {
    const value = buildDateRange(dateFrom, dateTo);
    const range = toInterval(value);

    setVintageData({
      value:
        !range.to || (range.from?.getFullYear() === range.to.getFullYear())
          ? `${range.from?.getFullYear()}`
          : `${range.from?.getFullYear()} - ${range.to.getFullYear()}`,
      range,
    });
  };

  const commitVintageData = (): void => {
    setFormVintage({
      name: vintageData.value,
      interval: `${vintageData?.range?.from?.toISOString()} - ${vintageData?.range?.to?.toISOString()}`,
    });
    setEditingVintage(false);
  };

  const cancelVintageDataCommit = (): void => {
    setVintageData({
      value: formVintage?.name || "",
      range: toInterval(formVintage?.name || "") || { from: null, to: null },
    });
    setEditingVintage(false);
  };

  return (
    <Stack gap={2} justifyContent="center" height={200}>
      <Maybe condition={gettingProject}>
        <>
          <Skeleton variant="rectangular" width="100%" height={56} />
          <Skeleton variant="rectangular" width="100%" height={56} />
        </>
      </Maybe>
      <Maybe condition={!gettingProject}>
        {/* Project */}
        <Maybe condition={!projectId}>
          <Autocomplete
            id="project"
            open={open}
            options={projectOptions}
            value={formProject}
            onChange={handleProjectSelectionChange}
            onInputChange={(event: SyntheticEvent<Element, Event>, value: string, reason: "input" | "clear" | "reset") => handleProjectSearch(event, value, reason === "reset" ? "clear" : reason)}
            onKeyDown={handleKeyDown}
            onClose={handleClose}
            renderInput={(params) => (
              <TextField
                required
                {...params}
                label="Project"
                placeholder="Search and select a project"
                InputLabelProps={{ shrink: true }}
              />
            )}
            classes={{
              popupIndicator: classes.AsyncPopoutIndicator,
            }}
          />
        </Maybe>
        <Maybe condition={!!projectId && !!formProject}>
          <Stack p={2} gap={1}>
            <Typography variant="body2" fontWeight={400}>
              Project
            </Typography>
            <Typography>{formProject?.label}</Typography>
          </Stack>
        </Maybe>
        {/* Vintage */}
        <Maybe condition={!editingVintage}>
          <TextField
            type="text"
            label="Vintage"
            placeholder="Click to enter vintage range"
            value={formVintage?.name}
            onChange={handleVintageChange}
            required
            disabled={noProjectPicked}
            InputLabelProps={{ shrink: true }}
            inputProps={{ maxLength: 256 }}
            onFocus={() => setEditingVintage(true)}
          />
        </Maybe>
        <Maybe condition={editingVintage}>
          <Box
            sx={{
              minHeight: "120px",
              border: "solid",
              borderColor: noProjectPicked ? "rgba(118, 118, 118, 0.3)" : COLORS.rubiconGreen,
              borderWidth: "1px",
              borderRadius: "5px",
              padding: "10px",
            }}
          >
            <Typography
              variant="body1"
              component="p"
              sx={{
                fontWeight: 500,
                color: noProjectPicked ? "rgba(118, 118, 118)" : COLORS.rubiconGreen,
                marginTop: "5px",
              }}
            >
              Vintage
            </Typography>
            <Stack
              direction={shouldStackDateField ? "column" : "row"}
              justifyContent="space-between"
              alignItems="center"
              sx={{ marginTop: "10px" }}
              gap={2}
            >
              <YearRangePicker
                dateFrom={dayjs(vintageData?.range?.from)}
                dateTo={dayjs(vintageData?.range?.to)}
                disabled={noProjectPicked}
                onChange={handleYearRange}
              />
              <Box sx={{ color: "gray" }}>- or -</Box>
              <DateRangePicker
                dateFrom={dayjs(vintageData?.range?.from)}
                dateTo={dayjs(vintageData?.range?.to)}
                disabled={noProjectPicked}
                onChange={handleDateRange}
              />
              <Stack direction="row" gap={0}>
                <IconButton
                  sx={{ color: COLORS.rubiconGreen }}
                  edge="start"
                  disabled={!!validationMessage || noProjectPicked}
                  onClick={commitVintageData}
                >
                  <CheckIcon />
                </IconButton>
                <IconButton
                  sx={{ marginLeft: "1px", color: COLORS.rubiconGreen }}
                  edge="start"
                  disabled={noProjectPicked}
                  onClick={cancelVintageDataCommit}
                >
                  <CancelIcon />
                </IconButton>
              </Stack>
            </Stack>
            <Typography
              sx={{
                color: noProjectPicked ? "rgba(118, 118, 118)" : COLORS.red,
                fontSize: "12px",
                marginLeft: "5px",
                marginTop: "4px",
              }}
              variant="body2"
              component="p"
            >
              {validationMessage}
            </Typography>
          </Box>
        </Maybe>
      </Maybe>
      <GenericDialog
        open={openDialog}
        title="Please confirm"
        positiveAction={{
          buttonText: "YES, PROCEED",
          onClick: () => setTimeout(async () => await createVintage()),
        }}
        negativeAction
        onClose={() => setSubmitting(false)}
        onNegativeClick={() => setSubmitting(false)}
        classes={{
          title: dialogClasses.Title,
          content: dialogClasses.Content,
        }}
      >
        <Typography>
          You are about to create a new vintage named <strong>{formVintage?.name}</strong>.
        </Typography>
      </GenericDialog>
    </Stack>
  );
};

export default AddNewVintageForm;
