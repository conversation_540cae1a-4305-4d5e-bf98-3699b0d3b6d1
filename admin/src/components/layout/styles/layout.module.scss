@function span_end($a, $b) {
    @return  $a / span $b;
}

.Layout {
    display: grid;
    grid-template-columns: [c1] var(--drawer-min-width) [c2] calc(100% - var(--drawer-min-width)) [cend];
    background-color: var(--main-background-color);

    .SideBarRoot {
        @media (max-width: 900px) {
            display: none;
        }

        .SideBar {
            display: grid;
            grid-template-rows: [r1] 64px [r2] auto [rend];
            transition: width 200ms ease-in-out;
            border-right: solid #a8a8a833 2.5px;
    
            &.SideBarPaper {
                width: var(--drawer-min-width);

                  &.SideBarPaperExpanded {
                    width: var(--drawer-max-width);
                  }
            }
            
            .Header {
                grid-row: r1 / r2;
    
                display: flex;
                align-items: center;
                padding: 0 10px;
                position: sticky;
                top: 0;
                z-index: 1000;
                background-color: white;
            }
    
            .Content {
                grid-row: span_end(r2, rend);
            }
        }
    }

    .Main {
        grid-column: span_end(c2, cend);
        display: grid;
        grid-template-rows: [r1] 64px [r2] auto [rend];
        transition: width 200ms ease-in-out;
        min-height: 100vh;

        @media (max-width: 900px) {
            grid-column: span_end(c1, cend);
        }

        .Header {
            grid-row: r1 / r2;
            width: calc(100% - var(--drawer-min-width));

            @media (max-width: 900px) {
                width: 100%;
            }
        }

        .Content {
            grid-row: span_end(r2, rend);

            width: calc(100vw - var(--drawer-min-width) - 11px);
            padding: 8px 10px;

            @media (max-width: 900px) {
                width: 100%;
            }
        }
    }

    &.LayoutPinned {
        grid-template-columns: [c1] var(--drawer-max-width) [c2] calc(100% - var(--drawer-max-width)) [cend];

        .Main {
            .Header {
                width: calc(100% - var(--drawer-max-width));

                @media (max-width: 900px) {
                    width: 100%;
                }
            }

            .Content {
                width: calc(100vw - var(--drawer-max-width) - 11px);

                @media (max-width: 900px) {
                    width: 100%;
                }
            }
        }
    }
}