// eslint-disable-next-line react/function-component-definition
function PDFIcon(props: { color?: string; height?: number; width?: number; disabled?: boolean }) {
  const { color = "#1E4639", disabled = false, height = 16, width = 16 } = props;
  let vColor = color;
  if (disabled) {
    vColor = "lightGray";
  }
  return (
    <svg
      width={width.toString()}
      height={height.toString()}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.8333 0.5H2.16667C1.25 0.5 0.5 1.25 0.5 2.16667V13.8333C0.5 14.75 1.25 15.5 2.16667 15.5H13.8333C14.75 15.5 15.5 14.75 15.5 13.8333V2.16667C15.5 1.25 14.75 0.5 13.8333 0.5ZM5.91667 7.58333C5.91667 8.25 5.33333 8.83333 4.66667 8.83333H3.83333V10.5H2.58333V5.5H4.66667C5.33333 5.5 5.91667 6.08333 5.91667 6.75V7.58333ZM10.0833 9.25C10.0833 9.91667 9.5 10.5 8.83333 10.5H6.75V5.5H8.83333C9.5 5.5 10.0833 6.08333 10.0833 6.75V9.25ZM13.4167 6.75H12.1667V7.58333H13.4167V8.83333H12.1667V10.5H10.9167V5.5H13.4167V6.75ZM8 6.75H8.83333V9.25H8V6.75ZM3.83333 6.75H4.66667V7.58333H3.83333V6.75Z"
        fill={vColor}
      />
    </svg>
  );
}

export default PDFIcon;
