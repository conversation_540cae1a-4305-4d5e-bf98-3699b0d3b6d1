"use client";

import { Jumbotron, StaticComponentProps } from "@app/types/static-content";
import { useEffect, useState, useRef, useCallback, forwardRef, useImperativeHandle, CSSProperties } from "react";
import { Maybe } from "@rubiconcarbon/frontend-shared";

import classes from "./styles.module.scss";

// Define control interface for ref
export interface ImageJumbotronControls {
  pauseAnimation: () => void;
  resumeAnimation: () => void;
  getCurrentIndex: () => number;
  goToImage: (index: number) => void;
}

const ImageJumbotron = forwardRef<ImageJumbotronControls, StaticComponentProps<Jumbotron>>(
  ({ data, classes: jumbotronClasses, style = {} }, ref) => {
    const {
      images,
      text,
      subText,
      fullWidth,
      fullHeight,
      width,
      minWidth = 0,
      height,
      minHeight = 0,
      topLeftAction,
      bottomRightAction,
      animation = {},
      onImageChange,
      onTransitionProgress,
    } = data;

    // Extract animation config with defaults
    const { type = "slide", auto = true, duration = 1000, interval = 5000 } = animation;

    const hasMultipleImages = images.length > 1;

    // State for current and next image indices
    const [currentIndex, setCurrentIndex] = useState(0);
    const [nextIndex, setNextIndex] = useState(0);
    const [isPaused, setIsPaused] = useState(!auto);
    const [isTransitioning, setIsTransitioning] = useState(false);
    const [transitionProgress, setTransitionProgress] = useState(0);

    // Timer references
    const timerRef = useRef<NodeJS.Timeout | null>(null);
    const transitionTimerRef = useRef<NodeJS.Timeout | null>(null);
    const animationFrameRef = useRef<number | null>(null);

    // Function to track transition progress using requestAnimationFrame
    const trackTransitionProgress = useCallback(
      (startTime: number, timestamp: number, targetIndex: number) => {
        const elapsed = timestamp - startTime;
        const progress = Math.min(elapsed / duration, 1);

        setTransitionProgress(progress);

        // Call the onTransitionProgress callback with nextIndex if provided
        if (onTransitionProgress) {
          onTransitionProgress(progress, targetIndex);
        }

        if (progress < 1) {
          animationFrameRef.current = requestAnimationFrame((newTimestamp) =>
            trackTransitionProgress(startTime, newTimestamp, targetIndex),
          );
        } else {
          // Cleanup when animation is done
          if (animationFrameRef.current) {
            cancelAnimationFrame(animationFrameRef.current);
            animationFrameRef.current = null;
          }
        }
      },
      [duration, onTransitionProgress],
    );

    // Function to start transition to next image
    const startTransition = useCallback(() => {
      if (isPaused || !hasMultipleImages || isTransitioning) return;

      const next = (currentIndex + 1) % images.length;
      setNextIndex(next);
      setIsTransitioning(true);
      setTransitionProgress(0);

      // Start tracking progress with requestAnimationFrame
      const startTime = performance.now();
      animationFrameRef.current = requestAnimationFrame((timestamp) =>
        trackTransitionProgress(startTime, timestamp, next),
      );

      // Set timer to end transition
      transitionTimerRef.current = setTimeout(() => {
        setCurrentIndex(next);
        setIsTransitioning(false);
        setTransitionProgress(0);

        // Call the onImageChange callback if provided
        if (onImageChange) {
          onImageChange(next);
        }

        // Cleanup animation frame
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
          animationFrameRef.current = null;
        }
      }, duration);
    }, [
      currentIndex,
      isPaused,
      hasMultipleImages,
      isTransitioning,
      images.length,
      duration,
      onImageChange,
      trackTransitionProgress,
    ]);

    // Control functions
    const pauseAnimation = useCallback(() => {
      setIsPaused(true);
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    }, []);

    const resumeAnimation = useCallback(() => {
      if (hasMultipleImages) {
        setIsPaused(false);
      }
    }, [hasMultipleImages]);

    const getCurrentIndex = useCallback(() => currentIndex, [currentIndex]);

    // Function to navigate to a specific image
    const goToImage = useCallback(
      (index: number) => {
        if (index < 0 || index >= images.length || index === currentIndex || isTransitioning) return;

        // Clean up any existing transition
        if (transitionTimerRef.current) {
          clearTimeout(transitionTimerRef.current);
        }

        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
        }

        // Set the next index and start transition
        setNextIndex(index);
        setIsTransitioning(true);
        setTransitionProgress(0);

        // Start tracking progress with requestAnimationFrame
        const startTime = performance.now();
        animationFrameRef.current = requestAnimationFrame((timestamp) =>
          trackTransitionProgress(startTime, timestamp, index),
        );

        // Set timer to end transition
        transitionTimerRef.current = setTimeout(() => {
          setCurrentIndex(index);
          setIsTransitioning(false);
          setTransitionProgress(0);

          // Call the onImageChange callback if provided
          if (onImageChange) {
            onImageChange(index);
          }

          // Cleanup animation frame
          if (animationFrameRef.current) {
            cancelAnimationFrame(animationFrameRef.current);
            animationFrameRef.current = null;
          }
        }, duration);
      },
      [currentIndex, isTransitioning, images.length, duration, onImageChange, trackTransitionProgress],
    );

    // Expose control functions through ref
    useImperativeHandle(ref, () => ({
      pauseAnimation,
      resumeAnimation,
      getCurrentIndex,
      goToImage,
    }));

    // Set up the animation timer
    useEffect(() => {
      if (hasMultipleImages && !isPaused && !isTransitioning) timerRef.current = setInterval(startTransition, interval);

      return (): void => {
        if (timerRef.current) clearInterval(timerRef.current);
      };
    }, [hasMultipleImages, isPaused, isTransitioning, startTransition, interval]);

    // Clean up timers and animation frames on unmount
    useEffect(() => {
      return (): void => {
        if (timerRef.current) clearInterval(timerRef.current);
        if (transitionTimerRef.current) clearTimeout(transitionTimerRef.current);
        if (animationFrameRef.current) cancelAnimationFrame(animationFrameRef.current);
      };
    }, []);

    // Handle mouse events
    const handleMouseEnter = (): void => {
      if (hasMultipleImages) pauseAnimation();
    };

    const handleMouseLeave = (): void => {
      if (hasMultipleImages && auto) resumeAnimation();
    };

    const { root = "" } = jumbotronClasses ?? {};

    return (
      <div
        className={`${classes.ImageJumbotron} ${hasMultipleImages ? classes.Animated : ""} ${root}`}
        style={
          {
            minWidth,
            width: `${fullWidth || !width ? "100%" : "initial"}`,
            minHeight,
            height: `${fullHeight || !height ? "100%" : height}`,
            ...style,
          } as CSSProperties
        }
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        data-animation-type={type}
        data-transitioning={isTransitioning ? "true" : undefined}
        data-progress={(transitionProgress * 100).toFixed(0)}
      >
        {/* For single image */}
        {!hasMultipleImages && (
          <div
            className={classes.BackgroundImage}
            style={{
              backgroundImage: `url(${images[0]?.src})`,
            }}
          />
        )}

        {/* For multiple images */}
        {hasMultipleImages && (
          <>
            {/* Current image */}
            <div
              className={`${classes.BackgroundImage} ${isTransitioning ? classes[`${type}Out`] : ""}`}
              style={{
                backgroundImage: `url(${images[currentIndex]?.src})`,
                animationDuration: isTransitioning ? `${duration}ms` : undefined,
              }}
            />

            {/* Next image - only visible during transitions */}
            {isTransitioning && (
              <div
                className={`${classes.BackgroundImage} ${classes[`${type}In`]}`}
                style={{
                  backgroundImage: `url(${images[nextIndex]?.src})`,
                  animationDuration: `${duration}ms`,
                }}
              />
            )}
          </>
        )}

        <div className={classes.Content}>
          <div className={classes.Top}>
            <Maybe condition={!!topLeftAction}>{topLeftAction}</Maybe>
          </div>
          <div className={classes.Bottom}>
            <div className={classes.TextContainer}>
              <div className={classes.Text}>{text}</div>
              <Maybe condition={!!subText}>
                <div className={classes.SubText}>{subText}</div>
              </Maybe>
            </div>
            <Maybe condition={!!bottomRightAction}>{bottomRightAction}</Maybe>
          </div>
        </div>
      </div>
    );
  },
);

ImageJumbotron.displayName = "ImageJumbotron";

export default ImageJumbotron;
